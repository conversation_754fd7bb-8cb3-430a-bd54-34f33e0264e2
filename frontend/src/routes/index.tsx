import { create<PERSON><PERSON>er<PERSON>outer, RouterProvider } from "react-router-dom";
import { Suspense, lazy } from "react";
import RouteGuard from "@/components/auth/RouteGuard";

// Layouts
import MainLayout from "@/layouts/MainLayout";
import AuthLayout from "@/layouts/AuthLayout";
import NewDashboardLayout from "@/components/NewLayout/NewDashboardLayout";
// import BackgroundRemovalPage from "@/pages/dashboard/BackgroundRemovalPage";
import CommunityPage from "@/pages/CommunityPage";
import NewAdminDashboardLayout from "@/components/NewLayout/NewAdminDashboardLayout";

// Pages - Lazy loaded
const HomePage = lazy(() => import("@/pages/HomePage"));
const NewHomePage = lazy(() => import("@/pages/dashboard/HomePage"));
const LoginPage = lazy(() => import("@/pages/auth/LoginPage"));
const RegisterPage = lazy(() => import("@/pages/auth/RegisterPage"));
// const ForgotPasswordPage = lazy(
//   () => import("@/pages/auth/ForgotPasswordPage")
// );
// const ResetPasswordPage = lazy(() => import("@/pages/auth/ResetPasswordPage"));

// const DashboardHomePage = lazy(
//   () => import("@/pages/dashboard/DashboardHomePage")
// );

// const NewHomePage = lazy(() => import("@/pages/dashboard/NewHomePage"));
// const VideoGenerationPage = lazy(
//   () => import("@/pages/dashboard/VideoGenerationPage")
// );
// const ImageGenerationPage = lazy(
//   () => import("@/pages/dashboard/ImageGenerationPage")
// );
// const BackgroundRemovalPage = lazy(
//   () => import("@/pages/dashboard/BackgroundRemovalPage")
// );
// const VirtualTryOnPage = lazy(
//   () => import("@/pages/dashboard/VirtualTryOnPage")
// );
// const MyContentPage = lazy(() => import("@/pages/dashboard/MyContentPage"));
const SubscriptionManagement = lazy(
  () => import("@/pages/SubscriptionManagement")
);
// const SubscriptionPage = lazy(
//   () => import("@/pages/dashboard/SubscriptionPage")
// );
const UserProfilePage = lazy(() => import("@/pages/dashboard/UserProfilePage"));

const BlogPage = lazy(() => import("@/pages/blog/BlogPage"));
// const BlogPostPage = lazy(() => import("@/pages/blog/BlogPostPage"));
const BlogPostPage = lazy(() => import("@/pages/blog/BlogDetailsPage"));

// About Page
const AboutPage = lazy(() => import("@/pages/about/Page"));

// Use Cases
const UseCasesPage = lazy(() => import("@/pages/use-case/Page"));
// Referral Page
const ReferralPage = lazy(() => import("@/pages/referral/Page"));
//
const CommunityPageNew = lazy(() => import("@/pages/community/Page"));
// API Page
const ApiPage = lazy(() => import("@/pages/api/Page"));
// const PricingPage = lazy(() => import("@/pages/PricingPage"));
const Pricing = lazy(() => import("@/pages/pricing/Page"));
const NotFoundPage = lazy(() => import("@/pages/NotFoundPage"));

// Admin pages
const AdminDashboardPage = lazy(
  () => import("@/pages/admin/AdminDashboardPage")
);
const AdminUsersPage = lazy(() => import("@/pages/admin/AdminUsersPage"));
const AdminUserDetailPage = lazy(
  () => import("@/pages/admin/AdminUserDetailPage")
);
const AdminBlogPage = lazy(() => import("@/pages/admin/AdminBlogPage"));
const AdminBlogEditPage = lazy(() => import("@/pages/admin/AdminBlogEditPage"));
const AdminPaymentSettingsPage = lazy(
  () => import("@/pages/admin/AdminPaymentSettingsPage")
);
const AdminAnalyticsPage = lazy(
  () => import("@/pages/admin/AdminAnalyticsPage")
);
const AdminSubscriptionPlansPage = lazy(
  () => import("@/pages/admin/AdminSubscriptionPlansPage")
);
const AdminCreditPackagesPage = lazy(
  () => import("@/pages/admin/AdminCreditPackagesPage")
);
const AdminServiceCostsPage = lazy(
  () => import("@/pages/admin/AdminServiceCostsPage")
);
const AdminPaymentsPage = lazy(() => import("@/pages/admin/AdminPaymentsPage"));
const AdminRefundsPage = lazy(() => import("@/pages/admin/AdminRefundsPage"));
const AdminSettingsPage = lazy(() => import("@/pages/admin/AdminSettingsPage"));

// Virtual Try-On Admin pages
const AdminVirtualTryOnModelsPage = lazy(
  () => import("@/pages/admin/AdminVirtualTryOnModelsPage")
);
const AdminVirtualTryOnClothingPage = lazy(
  () => import("@/pages/admin/AdminVirtualTryOnClothingPage")
);
const AdminVirtualTryOnStatsPage = lazy(
  () => import("@/pages/admin/AdminVirtualTryOnStatsPage")
);

// const ResultLibraryNewPage = lazy(() => import("@/pages/ResultLibraryNewPage"));

const BackgroundRemoverPage = lazy(
  () => import("@/pages/BackgroundRemoverPage")
);

const SpeedPaintingPage = lazy(() => import("@/pages/SpeedPaintingPage"));

const VirtualTryOnPage = lazy(() => import("@/pages/VirtualTryOnPage"));

const BillingDetailsPage = lazy(() => import("@/pages/BillingDetailsPage"));
const TransactionHistoryPage = lazy(
  () => import("@/pages/TransactionHistoryPage")
);

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
  </div>
);

// Router configuration
const router = createBrowserRouter([
  {
    path: "/",
    element: <MainLayout />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <HomePage />
          </Suspense>
        ),
      },
      {
        path: "about",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AboutPage />
          </Suspense>
        ),
      },
      {
        path: "pricing",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <Pricing />
          </Suspense>
        ),
      },
      {
        path: "use-cases",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <UseCasesPage />
          </Suspense>
        ),
      },
      {
        path: "docs-api",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <ApiPage />
          </Suspense>
        ),
      },
      {
        path: "blog",
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <BlogPage />
              </Suspense>
            ),
          },
          {
            path: ":slug",
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <BlogPostPage />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: "referral",
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <ReferralPage />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: "community",
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <CommunityPageNew />
              </Suspense>
            ),
          },
        ],
      },
    ],
  },
  {
    path: "/auth",
    element: <AuthLayout />,
    children: [
      {
        path: "login",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <LoginPage />
          </Suspense>
        ),
      },
      {
        path: "register",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <RegisterPage />
          </Suspense>
        ),
      },
    ],
  },

  //     {
  //       path: "forgot-password",
  //       element: (
  //         <Suspense fallback={<LoadingFallback />}>
  //           <ForgotPasswordPage />
  //         </Suspense>
  //       ),
  //     },
  //     {
  //       path: "reset-password",
  //       element: (
  //         <Suspense fallback={<LoadingFallback />}>
  //           <ResetPasswordPage />
  //         </Suspense>
  //       ),
  //     },
  //   ],
  // },
  {
    path: "/dashboard",
    element: (
      <RouteGuard requireAuth>
        {/* <DashboardLayout isAdmin={false} /> */}
        <NewDashboardLayout />
      </RouteGuard>
    ),
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <NewHomePage />
          </Suspense>
        ),
      },
      {
        path: "community",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <CommunityPage />
          </Suspense>
        ),
      },
      {
        path: "subscription",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <SubscriptionManagement />
          </Suspense>
        ),
      },
      {
        path: "profile",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <UserProfilePage />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: "ai-tool",
    element: <NewDashboardLayout />,
    children: [
      {
        path: "background-remover",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <BackgroundRemoverPage />
          </Suspense>
        ),
      },
      {
        path: "speedpainting",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <SpeedPaintingPage />
          </Suspense>
        ),
      },
      {
        path: "virtual-try-on",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <VirtualTryOnPage />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: "/admin",
    element: (
      <RouteGuard requireAuth requireAdmin>
        <NewAdminDashboardLayout />
      </RouteGuard>
    ),
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminDashboardPage />
          </Suspense>
        ),
      },
      {
        path: "users",
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminUsersPage />
              </Suspense>
            ),
          },
          {
            path: ":id",
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminUserDetailPage />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: "blog",
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminBlogPage />
              </Suspense>
            ),
          },
          {
            path: "edit/:id",
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminBlogEditPage />
              </Suspense>
            ),
          },
          {
            path: "new",
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminBlogEditPage isNew />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: "payment-settings",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminPaymentSettingsPage />
          </Suspense>
        ),
      },
      {
        path: "analytics",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminAnalyticsPage />
          </Suspense>
        ),
      },
      {
        path: "subscription-plans",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminSubscriptionPlansPage />
          </Suspense>
        ),
      },
      {
        path: "credit-packages",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminCreditPackagesPage />
          </Suspense>
        ),
      },
      {
        path: "service-costs",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminServiceCostsPage />
          </Suspense>
        ),
      },
      {
        path: "payments",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminPaymentsPage />
          </Suspense>
        ),
      },
      {
        path: "refunds",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminRefundsPage />
          </Suspense>
        ),
      },
      {
        path: "settings",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <AdminSettingsPage />
          </Suspense>
        ),
      },
      {
        path: "virtual-try-on",
        children: [
          {
            path: "models",
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminVirtualTryOnModelsPage />
              </Suspense>
            ),
          },
          {
            path: "clothing",
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminVirtualTryOnClothingPage />
              </Suspense>
            ),
          },
          {
            path: "statistics",
            element: (
              <Suspense fallback={<LoadingFallback />}>
                <AdminVirtualTryOnStatsPage />
              </Suspense>
            ),
          },
        ],
      },
    ],
  },
  {
    path: "account", // ot added link
    element: <NewDashboardLayout />,
    children: [
      {
        path: "credit",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <BillingDetailsPage />
          </Suspense>
        ),
      },
      {
        path: "transaction",
        element: (
          <Suspense fallback={<LoadingFallback />}>
            <TransactionHistoryPage />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: "*",
    element: (
      <Suspense fallback={<LoadingFallback />}>
        <NotFoundPage />
      </Suspense>
    ),
  },
]);

export default function Router() {
  return <RouterProvider router={router} />;
}
