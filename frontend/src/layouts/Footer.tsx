import Section from "@/components/layout/Section";
// import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
// import { Gith<PERSON>, Linkedin, Twitter } from "lucide-react";
import { Link } from "react-router-dom";
const Footer = () => {
  const footerLinks = [
    [
      { name: "Streaming Avatar", path: "/products/streaming-avatar" },
      { name: "Talking Avatar", path: "/products/talking-avatar" },
      { name: "Video Translation", path: "/products/video-translation" },
      { name: "Face Swap", path: "/products/face-swap" },
      { name: "Live Camera", path: "/products/live-camera" },
      { name: "AI Video Editor", path: "/products/ai-video-editor" },
      { name: "Talking Photo", path: "/products/talking-photo" },
      { name: "Image Generator", path: "/products/image-generator" },
      { name: "Background Change", path: "/products/background-change" },
      { name: "Video Campaign", path: "/products/video-campaign" },
      { name: "<PERSON> Moderator", path: "/products/jarvis-moderator" },
      { name: "AI Support Agent", path: "/products/ai-support-agent" },
      {
        name: "Real-Time Translation",
        path: "/products/real-time-translation",
      },
    ],
    [
      { name: "Help Center", path: "/help-center" },
      { name: "Case Studies", path: "/case-studies" },
      { name: "Use Cases", path: "/use-cases" },
      { name: "Videos", path: "/videos" },
      { name: "Webinars", path: "/webinars" },
      { name: "Guides", path: "/guides" },
      { name: "Alternatives", path: "/alternatives" },
      { name: "API", path: "/api" },
      { name: "Our Tools", path: "/our-tools" },
      { name: "Knowledge Base", path: "/knowledge-base" },
      { name: "App Integrations", path: "/app-integrations" },
      { name: "Community", path: "/community" },
    ],
    [
      { name: "About", path: "/about" },
      { name: "Blog", path: "/blog" },
      { name: "Research", path: "/research" },
      { name: "News", path: "/news" },
      { name: "Events", path: "/events" },
      { name: "Creator Fund", path: "/creator-fund" },
      { name: "Affiliate Program", path: "/affiliate-program" },
      { name: "Referral Program", path: "/referral-program" },
      { name: "Education Programs", path: "/education-programs" },
      { name: "Partner Programs", path: "/partner-programs" },
      { name: "Trust and Safety", path: "/trust-and-safety" },
    ],
  ];

  return (
    <footer className="bg-black border-t border-gray-800 pt-16 pb-8">
      <Section>
        <div className="flex mb-5 flex-wrap items-start justify-between gap-12">
          <div className="flex-1">
            <img src="/icons/logo.svg" alt="logo" className="w-[200px]" />
          </div>
          <div className="grid mt-[50px] gap-8 grid-cols-1 sm:grid-cols-3">
            {footerLinks.map((links) => (
              <>
                {links.map((link, index) => (
                  <Link to={link.path} key={index}>
                    <Text variant={"card_body"}>{link.name}</Text>
                  </Link>
                ))}
              </>
            ))}
            {/* <div className="flex flex-col gap-5">
              <Link to={"/products/streaming-avatar"}>
                <Text variant={"card_body"}>Streaming Avatar</Text>
              </Link>
              <Link to={"/products/talking-avatar"}>
                <Text variant={"card_body"}>Talking Avatar</Text>
              </Link>
              <Link to={"/products/video-translation"}>
                <Text variant={"card_body"}>Video Translation</Text>
              </Link>
              <Link to={"/products/face-swap"}>
                <Text variant={"card_body"}>Face Swap</Text>
              </Link>
              <Link to={"/products/live-camera"}>
                <Text variant={"card_body"}>Live Camera</Text>
              </Link>
              <Link to={"/products/ai-video-editor"}>
                <Text variant={"card_body"}>AI Video Editor</Text>
              </Link>
              <Link to={"/products/talking-photo"}>
                <Text variant={"card_body"}>Talking Photo</Text>
              </Link>
              <Link to={"/products/image-generator"}>
                <Text variant={"card_body"}>Image Generator</Text>
              </Link>
              <Link to={"/products/background-change"}>
                <Text variant={"card_body"}>Background Change</Text>
              </Link>
              <Link to={"/products/video-campaign"}>
                <Text variant={"card_body"}>Video Campaign</Text>
              </Link>
              <Link to={"/products/jarvis-moderator"}>
                <Text variant={"card_body"}>Jarvis Moderator</Text>
              </Link>
              <Link to={"/products/ai-support-agent"}>
                <Text variant={"card_body"}>AI Support Agent</Text>
              </Link>
              <Link to={"/products/real-time-translation"}>
                <Text variant={"card_body"}>Real-Time Translation</Text>
              </Link>
            </div>
            <div className="flex flex-col gap-5">
              <Link to="/help-center">
                <Text variant={"card_body"}>Help Center</Text>
              </Link>
              <Link to="/case-studies">
                <Text variant={"card_body"}>Case Studies</Text>
              </Link>
              <Link to="/use-cases">
                <Text variant={"card_body"}>Use Cases</Text>
              </Link>
              <Link to="/videos">
                <Text variant={"card_body"}>Videos</Text>
              </Link>
              <Link to="/webinars">
                <Text variant={"card_body"}>Webinars</Text>
              </Link>
              <Link to="/guides">
                <Text variant={"card_body"}>Guides</Text>
              </Link>
              <Link to="/alternatives">
                <Text variant={"card_body"}>Alternatives</Text>
              </Link>
              <Link to="/api">
                <Text variant={"card_body"}>API</Text>
              </Link>
              <Link to="/our-tools">
                <Text variant={"card_body"}>Our Tools</Text>
              </Link>
              <Link to="/knowledge-base">
                <Text variant={"card_body"}>Knowledge Base</Text>
              </Link>
              <Link to="/app-integrations">
                <Text variant={"card_body"}>App Integrations</Text>
              </Link>
              <Link to="/community">
                <Text variant={"card_body"}>Community</Text>
              </Link>
            </div>
            <div className="flex flex-col gap-5">
              <Link to="/about">
                <Text variant={"card_body"}>About</Text>
              </Link>
              <Link to="/blog">
                <Text variant={"card_body"}>Blog</Text>
              </Link>
              <Link to="/research">
                <Text variant={"card_body"}>Research</Text>
              </Link>
              <Link to="/news">
                <Text variant={"card_body"}>News</Text>
              </Link>
              <Link to="/events">
                <Text variant={"card_body"}>Events</Text>
              </Link>
              <Link to="/creator-fund">
                <Text variant={"card_body"}>Creator Fund</Text>
              </Link>
              <Link to="/affiliate-program">
                <Text variant={"card_body"}>Affiliate Program</Text>
              </Link>
              <Link to="/referral-program">
                <Text variant={"card_body"}>Referral Program</Text>
              </Link>
              <Link to="/education-programs">
                <Text variant={"card_body"}>Education Programs</Text>
              </Link>
              <Link to="/partner-programs">
                <Text variant={"card_body"}>Partner Programs</Text>
              </Link>
              <Link to="/trust-and-safety">
                <Text variant={"card_body"}>Trust and Safety</Text>
              </Link>
            </div> */}
          </div>
        </div>
        <div className="flex items-center gap-5">
          <div className="rounded-full border border-[#FFFFFF45] h-[50px] w-[50px] flex items-center justify-center">
            <img src="/icons/x.svg" alt="logo" className="w-[29px] h-[29px]" />
          </div>
          <div className="rounded-full border border-[#FFFFFF45] h-[50px] w-[50px] flex items-center justify-center">
            <img
              src="/icons/tiktok.svg"
              alt="logo"
              className="w-[29px] h-[29px]"
            />
          </div>
          <div className="rounded-full border border-[#FFFFFF45] h-[50px] w-[50px] flex items-center justify-center">
            <img
              src="/icons/linkedin_icon.svg"
              alt="logo"
              className="w-[29px] h-[29px]"
            />
          </div>
          <div className="rounded-full border border-[#FFFFFF45] h-[50px] w-[50px] flex items-center justify-center">
            <img
              src="/icons/insta.svg"
              alt="logo"
              className="w-[29px] h-[29px]"
            />
          </div>
        </div>
      </Section>
    </footer>
  );
};

export default Footer;
