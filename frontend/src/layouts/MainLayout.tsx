import { Outlet, useLocation } from "react-router-dom";
import { useEffect, useMemo } from "react";
import NewFooter from "./NewFooter";

import { metaData } from "@/utils/DUMMY_DATA";
import TopBanner from "@/components/header/TopBanner";
import Header from "@/components/header/Header";
import { faqData } from "@/components/common/variable";

const MainLayout = () => {
  const location = useLocation();

  useEffect(() => {
    const meta = metaData[location.pathname];
    if (meta) {
      document.title = meta.title;

      let favicon = document.querySelector(
        "link[rel~='icon']"
      ) as HTMLLinkElement;
      if (!favicon) {
        favicon = document.createElement("link");
        favicon.rel = "icon";
        document.head.appendChild(favicon);
      }
      favicon.href = meta.favicon;
    }
  }, [location.pathname]);

  // Memoize the FAQ schema to avoid re-creating it unnecessarily
  const faqSchema = useMemo(() => {
    return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      mainEntity: faqData.map((item) => ({
        "@type": "Question",
        name: item.question,
        acceptedAnswer: {
          "@type": "Answer",
          text: item.answer,
        },
      })),
    };
  }, []);

  // Effect for FAQPage for SEO
  useEffect(() => {
    if (faqSchema.mainEntity.length > 0) {
      const script = document.createElement("script");
      script.type = "application/ld+json";
      script.textContent = JSON.stringify(faqSchema);
      script.id = "miragic-ai-schema";
      document.head.appendChild(script);

      // Cleanup function: Remove the script when the component unmounts
      // or dependencies change (though faqSchema is memoized)
      return () => {
        const existingScript = document.getElementById("miragic-ai-schema");
        if (existingScript) {
          document.head.removeChild(existingScript);
        }
      };
    }
  }, [faqSchema]);

  return (
    <div className="flex flex-col min-h-screen">
      <TopBanner />
      <Header />
      <main className="flex-1">
        <Outlet />
      </main>
      <NewFooter />
    </div>
  );
};

export default MainLayout;
