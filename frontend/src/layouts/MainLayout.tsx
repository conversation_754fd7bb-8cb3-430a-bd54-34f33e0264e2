import { Outlet, useLocation } from "react-router-dom";
import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useApp } from "@/contexts/useApp";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Globe, ChevronDown } from "lucide-react";
import NewFooter from "./NewFooter";
import LoginModal from "@/components/auth/LoginModal";
import SignupModal from "@/components/auth/SignUpModal";
import ForgotPasswordModal from "@/components/auth/ForgotPasswordModal";
import { metaData } from "@/utils/DUMMY_DATA";
import { Text } from "@/components/ui/text";

const MainLayout = () => {
  // const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProductDropdownOpen, setIsProductDropdownOpen] = useState(false);
  const [isResourcesDropdownOpen, setIsResourcesDropdownOpen] = useState(false);
  const [isCompanyDropdownOpen, setIsCompanyDropdownOpen] = useState(false);
  const [isBannerVisible, setIsBannerVisible] = useState(true);
  const [isShowLoginModal, setIsShowLoginModal] = useState(false);
  const [isShowSignUpModal, setIsShowSignUpModal] = useState(false);
  const [isShowForgotPassModal, setIsShowForgotPassModal] = useState(false);
  const { user, isAuthenticated } = useApp();
  const productDropdownRef = useRef<HTMLDivElement>(null);
  const resourcesDropdownRef = useRef<HTMLDivElement>(null);
  const companyDropdownRef = useRef<HTMLDivElement>(null);

  const products = [
    {
      name: "Background Remover",
      description: "Remove image backgrounds instantly with AI",
      color: "bg-orange-500",
      href: "/ai-tool/background-remover",
    },
    {
      name: "Virtual-Try-On",
      description: "Try products on virtually in real-time with AI",
      color: "bg-purple-500",
      href: "/ai-tool/virtual-try-on",
    },
    {
      name: "Speedpainting",
      description: "Create stunning digital art quickly with AI",
      color: "bg-green-500",
      href: "/ai-tool/speedpainting",
    },
  ];

  const resources = [
    {
      name: "Community",
      description: "Connect, share, and grow with the Miragic",
      color: "bg-orange-500",
      href: "/community",
    },
    {
      name: "Use-Cases",
      description: "Explore diverse Miragic use-cases driving innovation",
      color: "bg-purple-500",
      href: "/use-cases",
    },
  ];

  const companyItems = [
    {
      name: "About Us",
      description: "Miragic empowers creativity with AI-driven solutions",
      href: "/about",
    },
    {
      name: "Blog",
      description:
        "Insights, tips, and updates on AI and technology innovations",
      href: "/blog",
    },
    {
      name: "Contact Us",
      description: "Reach out to Miragic for support and inquiries",
      href: "/about",
    },
  ];

  const programItems = [
    {
      name: "Referral Program",
      description: "Refer friends and earn rewards",
      href: "/referral",
    },
    // {
    //   name: "Social Program",
    //   description: "Share Miragic with your friends and earn rewards",
    //   href: "/affiliate",
    // },
  ];

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        productDropdownRef.current &&
        !productDropdownRef.current.contains(event.target as Node)
      ) {
        setIsProductDropdownOpen(false);
      }
      if (
        resourcesDropdownRef.current &&
        !resourcesDropdownRef.current.contains(event.target as Node)
      ) {
        setIsResourcesDropdownOpen(false);
      }
      if (
        companyDropdownRef.current &&
        !companyDropdownRef.current.contains(event.target as Node)
      ) {
        setIsCompanyDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleProductClick = (href: string) => {
    window.location.href = href;
    setIsProductDropdownOpen(false);
  };

  const handleResourceClick = (href: string) => {
    window.location.href = href;
    setIsResourcesDropdownOpen(false);
  };

  const handleCompanyClick = (href: string) => {
    window.location.href = href;
    setIsCompanyDropdownOpen(false);
  };

  // Handlers to open a dropdown and close others
  const openProductDropdown = () => {
    setIsProductDropdownOpen(true);
    setIsResourcesDropdownOpen(false);
    setIsCompanyDropdownOpen(false);
  };

  const openResourcesDropdown = () => {
    setIsResourcesDropdownOpen(true);
    setIsProductDropdownOpen(false);
    setIsCompanyDropdownOpen(false);
  };

  const openCompanyDropdown = () => {
    setIsCompanyDropdownOpen(true);
    setIsProductDropdownOpen(false);
    setIsResourcesDropdownOpen(false);
  };

  const location = useLocation();

  useEffect(() => {
    const meta = metaData[location.pathname];
    console.log(meta);
    if (meta) {
      document.title = meta.title;

      let favicon = document.querySelector(
        "link[rel~='icon']"
      ) as HTMLLinkElement;
      if (!favicon) {
        favicon = document.createElement("link");
        favicon.rel = "icon";
        document.head.appendChild(favicon);
      }
      favicon.href = meta.favicon;
    }
  }, [location.pathname]);
  return (
    <div className="flex flex-col min-h-screen">
      {/* Top Banner */}
      {isBannerVisible && (
        <div className="bg-gradient-to-r from-[#12091E] via-[#4F2884] to-[#190C29] text-white px-4 py-3 relative">
          <div className="container mx-auto flex items-center justify-center text-center">
            <div className="flex items-center gap-2 text-sm">
              <span className="hidden sm:inline font-inter font-semibold">
                As Featured in Tech Crunch: How MiragicAI is Building a Gen AI
                Unicorn
              </span>
              <span className="sm:hidden">
                Featured in Tech Crunch: MiragicAI Gen AI Unicorn
              </span>
              <span className="text-purple-300">👉</span>
              <span className="text-purple-300 underline cursor-pointer hover:text-white transition">
                Read the Full Story
              </span>
            </div>
            <button
              onClick={() => setIsBannerVisible(false)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-purple-200 hover:text-white transition-colors p-1"
              aria-label="Close banner"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}
      {/* Header */}
      <header
        className={`border-b border-gray-800 sticky top-0 z-50 w-full transition-all duration-300 ${"bg-gradient-to-b from-[#120d1f] to-black"}`}
      >
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-16">
            <div className="flex items-center gap-5">
              <Link to="/" className="flex items-center gap-2">
                <img
                  src="/png/new_miragic_logo.png"
                  className="w-auto h-[42px]"
                />
              </Link>

              <nav className="hidden md:flex items-center gap-6">
                {/* Products Dropdown */}
                <div className="relative" ref={productDropdownRef}>
                  <button
                    className="text-sm text-gray-400 hover:text-white transition flex items-center gap-1"
                    onMouseEnter={openProductDropdown}
                  >
                    Products
                    <ChevronDown
                      className={`w-4 h-4 transition-transform duration-200 ${
                        isProductDropdownOpen ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {isProductDropdownOpen && (
                    <div
                      className="absolute top-full left-0 mt-2 px-7 py-5 w-[450px] bg-primary/90 border border-gray-700 rounded-xl shadow-2xl z-50 overflow-hidden"
                      onMouseLeave={() => setIsProductDropdownOpen(false)}
                    >
                      <div className="mb-3">
                        <h3 className="text-white font-medium">Our products</h3>
                      </div>

                      <div className="py-2 flex flex-col gap-3">
                        {products.map((product, index) => (
                          <div
                            key={index}
                            onClick={() => handleProductClick(product.href)}
                            className="flex items-center gap-3 bg-gray-800/40 px-7 py-3 hover:bg-gray-800/50 rounded-4xl border border-gray-600 cursor-pointer transition-colors duration-150"
                          >
                            <div className="flex-1 min-w-0">
                              <h4 className="text-white font-medium text-sm mb-1">
                                {product.name}
                              </h4>
                              <p className="text-gray-400 text-xs leading-relaxed">
                                {product.description}
                              </p>
                            </div>
                            <div
                              className={`w-10 h-10 ${product.color} rounded-lg flex items-center justify-center flex-shrink-0`}
                            >
                              <div className="w-5 h-5 bg-white rounded-sm opacity-90"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Resources Dropdown */}
                <div className="relative" ref={resourcesDropdownRef}>
                  <button
                    className="text-sm text-gray-400 hover:text-white transition flex items-center gap-1"
                    onMouseEnter={openResourcesDropdown}
                  >
                    Resources
                    <ChevronDown
                      className={`w-4 h-4 transition-transform duration-200 ${
                        isResourcesDropdownOpen ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {isResourcesDropdownOpen && (
                    <div
                      className="absolute top-full left-0 mt-2 px-7 py-5 w-[450px] bg-primary/90 border border-gray-700 rounded-xl shadow-2xl z-50 overflow-hidden"
                      onMouseLeave={() => setIsResourcesDropdownOpen(false)}
                    >
                      <div className="mb-3">
                        <h3 className="text-white font-medium ">
                          Our resources
                        </h3>
                      </div>

                      <div className="py-2 flex flex-col gap-3">
                        {resources.map((resource, index) => (
                          <div
                            key={index}
                            onClick={() => handleResourceClick(resource.href)}
                            className="flex items-center gap-3 px-7 py-3 bg-gray-800/40 hover:bg-gray-800/50 rounded-4xl border border-gray-600 cursor-pointer transition-colors duration-150"
                          >
                            <div className="flex-1 min-w-0">
                              <h4 className="text-white font-medium text-sm mb-1">
                                {resource.name}
                              </h4>
                              <p className="text-gray-400 text-xs leading-relaxed">
                                {resource.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Company Dropdown */}
                <div className="relative" ref={companyDropdownRef}>
                  <button
                    className="text-sm text-gray-400 hover:text-white transition flex items-center gap-1"
                    onMouseEnter={openCompanyDropdown}
                  >
                    Company
                    <ChevronDown
                      className={`w-4 h-4 transition-transform duration-200 ${
                        isCompanyDropdownOpen ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {isCompanyDropdownOpen && (
                    <div
                      className="absolute top-full left-0 mt-2 p-6 w-[450px] bg-primary/90 backdrop-blur-sm border border-gray-700 rounded-2xl shadow-2xl z-50"
                      onMouseLeave={() => setIsCompanyDropdownOpen(false)}
                    >
                      {/* Programs Section */}
                      <div className="mb-6">
                        <h3 className="text-gray-300 font-medium text-xs mb-4 uppercase tracking-wider">
                          Programs
                        </h3>
                        <div className="grid grid-cols-2 gap-3">
                          {programItems.map((program, index) => (
                            <div
                              key={index}
                              onClick={() => handleCompanyClick(program.href)}
                              className="bg-gray-800/40 border border-gray-700 rounded-xl p-4 cursor-pointer hover:bg-gray-700/50 hover:border-gray-600 transition-all duration-200"
                            >
                              <h4 className="text-white font-medium text-sm mb-2">
                                {program.name}
                              </h4>
                              <p className="text-gray-400 text-xs leading-relaxed">
                                {program.description}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Information Section */}
                      <div>
                        <h3 className="text-gray-300 font-medium text-xs mb-4 uppercase tracking-wider">
                          Information
                        </h3>
                        <div className="flex flex-col gap-2">
                          {companyItems.map((item, index) => (
                            <div
                              key={index}
                              onClick={() => handleCompanyClick(item.href)}
                              className="bg-gray-800/20 border border-gray-700/50 rounded-lg p-3 cursor-pointer hover:bg-gray-700/30 hover:border-gray-600 transition-all duration-200"
                            >
                              <h4 className="text-white font-medium text-sm mb-1">
                                {item.name}
                              </h4>
                              <p className="text-gray-400 text-xs">
                                {item.description}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </nav>
            </div>
            <div className="flex items-center gap-[36px]">
              <Link
                to="/docs-api"
                className="text-sm text-gray-400 hover:text-white transition"
              >
                API
              </Link>
              <Link
                to="/pricing"
                className="text-sm text-gray-400 hover:text-white transition"
              >
                Pricing
              </Link>
            </div>
          </div>

          {isAuthenticated ? (
            <div className="hidden md:flex items-center space-x-4">
              <Link to={user?.role === "ADMIN" ? "/admin" : "/dashboard"}>
                <Avatar>
                  <AvatarImage src={user?.profile?.avatarUrl} />
                  <AvatarFallback>{user?.profile?.firstName}</AvatarFallback>
                </Avatar>
              </Link>
            </div>
          ) : (
            <div className="flex gap-[50px] items-center">
              <Link
                to="/about"
                className="flex text-[14px] items-center gap-3 text-gray-400 hover:text-white transition"
              >
                <Globe />
                <div>Contact Sales</div>
              </Link>
              <div className="hidden md:flex items-center space-x-4">
                {/* <Link to="/auth/login">
                </Link> */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsShowLoginModal(true)}
                  className="text-gray-400 hover:text-gray-300 hover:bg-black cursor-pointer border-2 border-gray-500 hover:border-gray-300 rounded-3 px-6 py-4"
                >
                  <Text variant={"card_body"} className="text-[14px]">
                    Log in
                  </Text>
                </Button>
                <Button
                  onClick={() => setIsShowSignUpModal(true)}
                  className="px-6 text-white text-base rounded-3 cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
                >
                  <Text variant={"card_body"} className="text-[14px]">
                    Sign up
                  </Text>
                </Button>
              </div>
            </div>
          )}
          <button
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-6 h-6"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                />
              )}
            </svg>
          </button>
        </div>

        {isMobileMenuOpen && (
          <div className="md:hidden bg-background border-t">
            <div className="container mx-auto px-4 py-4 flex flex-col space-y-4">
              <Link
                to="/"
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                to="/pricing"
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Pricing
              </Link>
              <Link
                to="/blog"
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Blog
              </Link>
              <div className="flex flex-col space-y-2 pt-2 border-t">
                {/* <Link
                  to="/auth/login"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                </Link> */}
                <Button
                  onClick={() => setIsShowLoginModal(true)}
                  variant="default"
                  className="w-full"
                >
                  Log in
                </Button>
                {/* <Link
                  to="/auth/register"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                </Link> */}
                <Button
                  onClick={() => setIsShowSignUpModal(true)}
                  className="w-full"
                >
                  Sign up
                </Button>
              </div>
            </div>
          </div>
        )}
      </header>
      {/* Main Content */}
      <main className="flex-1">
        <Outlet />
      </main>

      {/* Footer */}
      <NewFooter />

      {/* Modal - Login Signup */}
      <LoginModal
        isOpen={isShowLoginModal}
        onClose={() => setIsShowLoginModal(false)}
        onClickSignUpModal={() => {
          setIsShowLoginModal(false);
          setIsShowForgotPassModal(false);
          setIsShowSignUpModal(true);
        }}
        onClickForgotPassword={() => {
          setIsShowLoginModal(false);
          setIsShowSignUpModal(false);
          setIsShowForgotPassModal(true);
        }}
      />
      <SignupModal
        isOpen={isShowSignUpModal}
        onClose={() => setIsShowSignUpModal(false)}
        onClickLoginModal={() => {
          setIsShowLoginModal(true);
          setIsShowSignUpModal(false);
        }}
      />
      <ForgotPasswordModal
        isOpen={isShowForgotPassModal}
        onClose={() => setIsShowForgotPassModal(false)}
        onBack={() => {
          setIsShowForgotPassModal(false);
          setIsShowLoginModal(true);
        }}
      />
    </div>
  );
};

export default MainLayout;
