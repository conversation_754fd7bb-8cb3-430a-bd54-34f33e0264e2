import {
  InstagramIcon,
  LinkedInIcon,
  MusicIcon,
  XTwitterIcon,
} from "@/lib/icons";
import { Link } from "react-router-dom";

const NewFooter = () => {
  const SOCIAL_MEDIA = [
    { Icon: XTwitterIcon, href: "https://x.com" },
    { Icon: MusicIcon, href: "https://www.tiktok.com/@miragic.ai" },
    { Icon: LinkedInIcon, href: "https://www.linkedin.com/company/miragic-ai" },
    { Icon: InstagramIcon, href: "https://www.instagram.com/miragic.ai/" },
  ];
  const menuSections = [
    {
      title: "Products",
      links: [
        {
          name: "Background Remover",
          link: "/ai-tool/background-remover",
        },
        {
          name: "Virtual Try On",
          link: "/ai-tool/virtual-try-on",
        },
        {
          name: "Speed Painting",
          link: "/ai-tool/speedpainting",
        },
      ],
    },
    {
      title: "Resources",
      links: [
        {
          name: "API",
          link: "/docs-api", // Adjusted assuming "Community" matches your earlier resource data
        },
        {
          name: "Case Studies",
          link: "/use-cases",
        },
        {
          name: "Use Cases",
          link: "/use-cases",
        },
      ],
    },
    {
      title: "Company",
      links: [
        {
          name: "About",
          link: "/about",
        },
        {
          name: "Blog",
          link: "/blog",
        },
        // {
        //   name: "Research",
        //   link: "/about", // Assuming this is same as "Contact Us" or placeholder
        // },
      ],
    },
  ];

  return (
    <footer className="bg-[#1A1A1A] text-white py-10 px-4 md:px-20">
      <div className="max-w-7xl mx-auto">
        {/* Grid layout: stack on small screens, 5 columns on md screens */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          {/* Logo + Social Media (merged into 2 columns on md screens) */}
          <div className="md:col-span-2 flex flex-col items-start justify-between space-y-6">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <img src="/png/new_miragic_logo.png" className="w-[170px]" />
            </div>

            {/* <h3 className="text-gray-300 mb-10">Miragic transforms content creation with generative AI</h3> */}

            {/* Social Media Icons */}
            <div className="flex space-x-4">
              {SOCIAL_MEDIA.map(({ Icon, href }, i) => (
                <div
                  key={href + i}
                  className="flex justify-center items-center rounded-full p-2 w-12 h-12 border border-gray-400"
                >
                  <a
                    href={href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className=""
                  >
                    <Icon />
                  </a>
                </div>
              ))}
            </div>
          </div>

          {menuSections.map((section, i) => (
            <div key={i} className="flex flex-col space-y-3">
              <h3 className="text-lg font-semibold mb-2">{section.title}</h3>
              {/* <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      All Products
    </a> */}
              {section.links.map((link, i) => (
                <Link
                  key={link.link + i}
                  to={link.link}
                  className="text-gray-300 hover:text-[#00A3FF] transition"
                >
                  {link.name}
                </Link>
              ))}

              {/* <a
                href="#"
                className="text-gray-300 hover:text-[#00A3FF] transition"
              >
                Virtual Try On
              </a>
              <a
                href="#"
                className="text-gray-300 hover:text-[#00A3FF] transition"
              >
                Speed Painting
              </a> */}
              {/* <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Face Swap
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Live Camera
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      AI Video Editor
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Talking Photo
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Image Generator
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Background Change
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Video Campaign
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Jarvis Moderator
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      AI Support Agent
    </a>
    <a
      href="#"
      className="text-gray-300 hover:text-[#00A3FF] transition"
    >
      Real-Time Translation
    </a> */}
            </div>
          ))}
          {/* Platform Column */}
        </div>
      </div>
    </footer>
  );
};

export default NewFooter;
