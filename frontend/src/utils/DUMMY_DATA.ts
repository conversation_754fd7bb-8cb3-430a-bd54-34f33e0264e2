// import type { SubscriptionPlan } from "@/services/admin.service";

export interface ClothingItem {
  id: string;
  type: string;
  image: string;
}

// export const dummySubscriptionPlans: SubscriptionPlan[] = [
//   {
//     id: "plan_1",
//     name: "Starter",
//     price: 9.99,
//     currency: "USD",
//     interval: "month",
//     features: {
//       videoGenerationQuota: 10,
//       imageGenerationQuota: 50,
//       backgroundRemovalQuota: 20,
//       accessToBasicTemplates: true,
//       customerSupport: "email-only",
//       maxVideoLength: 30, // seconds
//     },
//     stripePriceId: "price_starter_monthly",
//     isActive: true,
//     createdAt: "2023-01-15T10:30:00Z",
//     updatedAt: "2023-06-20T14:45:00Z",
//   },
//   {
//     id: "plan_2",
//     name: "Pro",
//     price: 29.99,
//     currency: "USD",
//     interval: "month",
//     features: {
//       videoGenerationQuota: 50,
//       imageGenerationQuota: 200,
//       backgroundRemovalQuota: 100,
//       accessToPremiumTemplates: true,
//       customerSupport: "priority-email",
//       maxVideoLength: 120,
//       watermarkFree: true,
//     },
//     stripePriceId: "price_pro_monthly",
//     paypalPlanId: "P-123456789",
//     isActive: true,
//     createdAt: "2023-01-15T10:30:00Z",
//     updatedAt: "2023-05-10T09:15:00Z",
//   },
//   {
//     id: "plan_3",
//     name: "Business",
//     price: 99.99,
//     currency: "USD",
//     interval: "month",
//     features: {
//       videoGenerationQuota: 200,
//       imageGenerationQuota: 1000,
//       backgroundRemovalQuota: 500,
//       accessToAllTemplates: true,
//       customerSupport: "24/7-chat",
//       maxVideoLength: 300,
//       watermarkFree: true,
//       dedicatedAccountManager: true,
//       apiAccess: true,
//     },
//     stripePriceId: "price_business_monthly",
//     paypalPlanId: "P-*********",
//     isActive: true,
//     createdAt: "2023-02-20T08:15:00Z",
//   },
//   {
//     id: "plan_4",
//     name: "Annual Pro",
//     price: 299.99,
//     currency: "USD",
//     interval: "year",
//     features: {
//       videoGenerationQuota: 600, // 50/month equivalent
//       imageGenerationQuota: 2400, // 200/month equivalent
//       backgroundRemovalQuota: 1200, // 100/month equivalent
//       accessToPremiumTemplates: true,
//       customerSupport: "priority-email",
//       maxVideoLength: 120,
//       watermarkFree: true,
//       annualDiscount: true,
//     },
//     stripePriceId: "price_pro_yearly",
//     isActive: true,
//     createdAt: "2023-03-05T11:20:00Z",
//     updatedAt: "2023-04-18T16:30:00Z",
//   },
//   {
//     id: "plan_5",
//     name: "Legacy Plan",
//     price: 14.99,
//     currency: "USD",
//     interval: "month",
//     features: {
//       videoGenerationQuota: 15,
//       imageGenerationQuota: 75,
//       backgroundRemovalQuota: 30,
//       accessToBasicTemplates: true,
//     },
//     isActive: false, // inactive plan
//     createdAt: "2022-11-10T09:00:00Z",
//     updatedAt: "2023-07-01T12:00:00Z",
//   },
// ];

export const metaData: {
  [key: string]: {
    title: string;
    favicon: string;
  };
} = {
  "/": {
    title: "Generative AI Software | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/api-docs": {
    title: "API | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/about": {
    title: "About Us | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/pricing": {
    title: "Pricing | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/blog": {
    title: "Blog | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/blog/:slug": {
    title: "Blog Post | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/auth/login": {
    title: "Login | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/dashboard": {
    title: "Dashboard | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/dashboard/community": {
    title: "Community | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/ai-tool": {
    title: "AI Tool | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/admin": {
    title: "Admin Dashboard | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/admin/users": {
    title: "Admin Users | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/admin/blog": {
    title: "Admin Blog | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/admin/payment-settings": {
    title: "Payment Settings | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/admin/analytics": {
    title: "Admin Analytics | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/admin/subscription-plans": {
    title: "Subscription Plans | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/admin/settings": {
    title: "Admin Settings | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/account/billing-details": {
    title: "Billing Details | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/account/transaction": {
    title: "Transaction History | Miragic",
    favicon: "/icons/favicon.ico",
  },
  // "affiliate"
  "/referral": {
    title: "Referral Program | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "/community": {
    title: "Community | Miragic",
    favicon: "/icons/favicon.ico",
  },
  "*": {
    title: "Page Not Found | Miragic",
    favicon: "/icons/favicon.ico",
  },
};

// Sample clothing items and models data
export const DRESSES_JUMP_SUIT: ClothingItem[] = [
  {
    image:
      "/Dresses-Jumpsuits/06e9bc0b92184cd7ab2e72c6f237c114.female_1096_03-372.jpg",
    id: "Dresses_Jumpsuits_0",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/07c4e0607df549acb9e6952ddfb8e1fd.female_1080_01-321.jpg",
    id: "Dresses_Jumpsuits_1",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/08bbe704ff6f45e5aa0ac79ae7e5a47c.female_1081_01-325.jpg",
    id: "Dresses_Jumpsuits_2",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/0b0861f982b04746a01f071e267f2bed.female_1104_02-395.jpg",
    id: "Dresses_Jumpsuits_3",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/0d9e02006b5b410591623287acdc41c6.female_1006_01-21.jpg",
    id: "Dresses_Jumpsuits_4",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/118aa4879da44ea497e69e9040967174.female_1052_03-195.jpg",
    id: "Dresses_Jumpsuits_5",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/1396dcb17f864f1bbb1c58a4c1c9922d.female_1062_01-238.jpg",
    id: "Dresses_Jumpsuits_6",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/1668d31b640341a6b75f54838092c6fa.female_1021_01-77.jpg",
    id: "Dresses_Jumpsuits_7",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/17616bb752d6424b83280024c16fe9c3.female_1044_02-164.jpg",
    id: "Dresses_Jumpsuits_8",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/1ebc601f995b4fefa0123923595d2a29.female_1047_02-176.jpg",
    id: "Dresses_Jumpsuits_9",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/212b7df90cf14151b8695c7b96dc433d.female_1068_01-261.jpg",
    id: "Dresses_Jumpsuits_10",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/2e05ecc0de214ddd9f12b8d27ddc2e6f.female_1019_02-70.jpg",
    id: "Dresses_Jumpsuits_11",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/3591072ed35a4f859c9e0c6b0cdd928d.female_1102_03-390.jpg",
    id: "Dresses_Jumpsuits_12",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/385b3f13d3674a05b27fcd5d2bfecd68.female_1077_01-309.jpg",
    id: "Dresses_Jumpsuits_13",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/3e649d26517b45fdae85030c37958acd.female_1066_02-254.jpg",
    id: "Dresses_Jumpsuits_14",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/43081908e2264cb6bb624535d4f7cf1d.female_1123_01-439.jpg",
    id: "Dresses_Jumpsuits_15",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/431803344db84245863afc91295a31e6.female_1061_02-234.jpg",
    id: "Dresses_Jumpsuits_16",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/466c2210724844c08c65e76754d63f4c.female_1055_03-209.jpg",
    id: "Dresses_Jumpsuits_17",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/4d6fbe085f30446b980e309c5bebe6e2.female_1106_03-402.jpg",
    id: "Dresses_Jumpsuits_18",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/50ca50a4666149c1a7bb75d4163e3e00.female_Sum9_1-1079.jpg",
    id: "Dresses_Jumpsuits_19",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/597fe8f46a044a4182847aab4b0b9a42.female_1011_01-39.jpg",
    id: "Dresses_Jumpsuits_20",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/5f7caf41f0e641b08b5d57054d76f473.female_Sum31_1-947.jpg",
    id: "Dresses_Jumpsuits_21",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/5ff0d2b0892c48cd99d7db060e67924f.female_1119_01-431.jpg",
    id: "Dresses_Jumpsuits_22",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/6177e84e05364962b625722e3ff7343f.female_1056_02-216.jpg",
    id: "Dresses_Jumpsuits_23",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/64550d768e3241e2a445f3f5f2d74c5c.female_1043_05-157.jpg",
    id: "Dresses_Jumpsuits_24",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/6a07443f457142ebbde24e50b13ec131.female_Sum40_1-711.jpg",
    id: "Dresses_Jumpsuits_25",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/6ffd6c41864546b1b5054e8b06d766dc.female_1072_01-277.jpg",
    id: "Dresses_Jumpsuits_26",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/7135b00426cb46dbbda208ac1c567a96.female_1043_01-153.jpg",
    id: "Dresses_Jumpsuits_27",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/74177ae2b0c54cf18634809f8c5b71b0.female_1022_01-80.jpg",
    id: "Dresses_Jumpsuits_28",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/77b0a504caff4a759fdd7f8a2eb6f61f.female_1101_02-386.jpg",
    id: "Dresses_Jumpsuits_29",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/79cf147332f04ce4a031d8d135e9be9f.female_Sum5_1-1075.jpg",
    id: "Dresses_Jumpsuits_30",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/7d07ead8745d4ba19cb8986de150d5fe.female_1118_01-429.jpg",
    id: "Dresses_Jumpsuits_31",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/817259c05ede46cda5ca1b203f5b4eac.female_1097_01-373.jpg",
    id: "Dresses_Jumpsuits_32",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/8896115fe7a94351a58a022616126cd0.female_Sum6_1-1099.jpg",
    id: "Dresses_Jumpsuits_33",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/88b2b56bf8024209b600efe5f3a5a268.female_1075_02-290.jpg",
    id: "Dresses_Jumpsuits_34",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/8be0960e4ec144ceaee281e382c329f5.female_XL_09-465.jpg",
    id: "Dresses_Jumpsuits_35",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/8d179652a2044183a0bf0c24012cdfb8.female_1116_02-426.jpg",
    id: "Dresses_Jumpsuits_36",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/8ec07b866d474b7ba61cb1ac79374619.female_scene1_2-618.jpg",
    id: "Dresses_Jumpsuits_37",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/95a20e03690e41ad9181e3fdc3080ebf.female_1067_04-260.jpg",
    id: "Dresses_Jumpsuits_38",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/ab85c4f3eb944f6cb378e9724a1f07f1.female_1082_01-328.jpg",
    id: "Dresses_Jumpsuits_39",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/ad3c078330d342c3ac39028cc86f3efb.female_Sum11_1-1086.jpg",
    id: "Dresses_Jumpsuits_40",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/ae50d13d29d04ccfbfa62513be275eeb.female_1051_04-192.jpg",
    id: "Dresses_Jumpsuits_41",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/af46036452084f449da79855a9de4790.female_1020_02-74.jpg",
    id: "Dresses_Jumpsuits_42",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/b7dc052ca5a94666ae43de96748e1c65.female_1065_01-249.jpg",
    id: "Dresses_Jumpsuits_43",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/b8d42d65480c42619eeb8cba02191a10.female_Sum37_1-847.jpg",
    id: "Dresses_Jumpsuits_44",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/be15890f952c405884836775b57a010d.female_1018_01-65.jpg",
    id: "Dresses_Jumpsuits_45",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/bffab8f3cb9340b59a7ad8c6f5f08e53.female_1105_03-399.jpg",
    id: "Dresses_Jumpsuits_46",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/cd4eb4d0f0c64b9ba5b079bb7a4c18fd.female_1073_01-281.jpg",
    id: "Dresses_Jumpsuits_47",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/d3e4f631ef364162866dbd024f1e3af4.female_1092_01-358.jpg",
    id: "Dresses_Jumpsuits_48",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/d6762aa2fa644cff90aa7237bde26e83.female_1079_04-320.jpg",
    id: "Dresses_Jumpsuits_49",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/debe0b1eea804759b069bc4193e8aa3f.female_Sum41_1-697.jpg",
    id: "Dresses_Jumpsuits_50",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/e0483a2c95db42379628ac209218cd43.female_1013_01-45.jpg",
    id: "Dresses_Jumpsuits_51",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/e2916a19316c495da3cdc520ff186a2f.female_1031_01-111.jpg",
    id: "Dresses_Jumpsuits_52",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/e97d0c4e0ee04a879c43238d3b81118a.female_1100_01-382.jpg",
    id: "Dresses_Jumpsuits_53",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/ef250e0f70cf483e970fc45393d2da8d.female_1108_02-407.jpg",
    id: "Dresses_Jumpsuits_54",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/fcb389cc3010432bbd33539ee892ca7e.female_1069_02-266.jpg",
    id: "Dresses_Jumpsuits_55",
    type: "Dresses & Jumpsuits",
  },
  {
    image:
      "/Dresses-Jumpsuits/fe19735f011f4659b8e388b14534654f.female_Sum38_1-739.jpg",
    id: "Dresses_Jumpsuits_56",
    type: "Dresses & Jumpsuits",
  },
];

export const MODELS_IMAGES: ClothingItem[] = Array.from(
  { length: 27 },
  (_, i) => ({
    id: `Human_model_${i + 1}`,
    image: `/Human_model/${i + 1}.jpg`,
    type: "model",
  })
);

export const TOP_CLOTH_IMAGES: ClothingItem[] = [
  {
    image:
      "/TopClothes/CoatsJPG/3abc4e2e0fa24c1fb859e7a3fa83b654.female_1028_01-101.jpg",
    id: "TopClothes_1",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/432d440dda4c41d595a20af4989458a1.female_Spr3_1-967.jpg",
    id: "TopClothes_2",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/49e6c46ba38646668f65f42b0b6237c1.female_1003_02-10.jpg",
    id: "TopClothes_3",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/58629eaea2a54610b84b746a37dd88e2.female_1002_01-5.jpg",
    id: "TopClothes_4",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/6e335dddba694bb182dd4c7cc89abea9.female_1010_01-36.jpg",
    id: "TopClothes_5",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/748249d93d97477f87772f8d3e76b4fd.female_1026_02-94.jpg",
    id: "TopClothes_6",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/cca3a2bc4beb4e129fe051a617e92365.female_1009_01-33.jpg",
    id: "TopClothes_7",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/d1103552a64c40049ead5239ce7c22b4.female_1027_01-97.jpg",
    id: "TopClothes_8",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/e65fdc0565a44a42a6614dc14ea43744.female_1050_02-186.jpg",
    id: "TopClothes_9",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/CoatsJPG/f8da304367ee42d89ec179b318e430e1.female_Spr1_1-959.jpg",
    id: "TopClothes_10",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/0fb502f09d554f98a20a999945eb030e.female_1014_01-49.jpg",
    id: "TopClothes_11",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/61c38c263423432899e2d4dc845025eb.female_1087_02-344.jpg",
    id: "TopClothes_12",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/70fc5a57303f43c3a2c3acc671eb3d5e.female_1085_03-339.jpg",
    id: "TopClothes_13",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/7415f11ef1434becb95eabdd482c0e8e.female_1008_01-29 (1).jpg",
    id: "TopClothes_14",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/7415f11ef1434becb95eabdd482c0e8e.female_1008_01-29.jpg",
    id: "TopClothes_15",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/9704c2227cf14c82ac7af61a13fa30e6.female_1088_01-346 (1).jpg",
    id: "TopClothes_16",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/9704c2227cf14c82ac7af61a13fa30e6.female_1088_01-346.jpg",
    id: "TopClothes_17",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/9e33319f051046259eb5cbc8f31a6b77.female_1012_01-42.jpg",
    id: "TopClothes_18",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/a2c75d0d16c643d88935eb6a507efcc6.female_1005_02-18.jpg",
    id: "TopClothes_19",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/LongSleeveJPGs/bc33017cf3714608bb04c4e3456d0983.female_1086_01-340.jpg",
    id: "TopClothes_20",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/068e8312044d44b68465d3b73ededbf5.female_1099_01-379.jpg",
    id: "TopClothes_21",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/2f9d7a29019d4ce183aa63901b4df5ee.female_1054_04-204.jpg",
    id: "TopClothes_22",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/45a4932c9cb14ce4a3dae751c828b06e.female_1091_01-355.jpg",
    id: "TopClothes_23",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/4e3dbf1958a64455a21c3b095ea09c60.female_Sum27_6-974.jpg",
    id: "TopClothes_24",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/6f68225d8ee644bbaf39365a93988a9e.female_Sum15_6-1020.jpg",
    id: "TopClothes_25",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/8f83edb17478419cad89d42ed29ee60d.female_aliyun_04-478.jpg",
    id: "TopClothes_26",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/a1719185492441f1812274e14ce325f4.female_1115_01-423.jpg",
    id: "TopClothes_27",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/d9d718ba646d465d844c5c1933dc018e.female_scene2_1-1051.jpg",
    id: "TopClothes_28",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/e313b5e7d7a7436b83e96dd50d397b7e.female_Sum7_1-955.jpg",
    id: "TopClothes_29",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/ShortSleevJPG/f0da17aa85124fcb95d1ee559609e620.female_1045_04-168.jpg",
    id: "TopClothes_30",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/07399a56ba064d8da43d07ea752e98f5.female_Sum28_2-984.jpg",
    id: "TopClothes_31",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/197e611eb8ef47d6be6484537dd900de.female_1090_01-352.jpg",
    id: "TopClothes_32",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/2b780188b9694b1fa3d7e2d4ebaab621.female_1098_01-376.jpg",
    id: "TopClothes_33",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/2f9c763f111846b08d9755cf5c3748f9.female_Sum16_6-1022 (1).jpg",
    id: "TopClothes_34",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/2f9c763f111846b08d9755cf5c3748f9.female_Sum16_6-1022.jpg",
    id: "TopClothes_35",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/32657c78932d4b8cbb0ff404166c1198.female_Sum19_1-973.jpg",
    id: "TopClothes_36",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/3784f365433f4f53aa6818ba8b39632d.female_1111_01-415.jpg",
    id: "TopClothes_37",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/40f7c5e5072b41879410e2676802c9ca.female_Sum25_6-998.jpg",
    id: "TopClothes_38",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/53b42956141b4bb49fcd88894f015c0a.female_Sum8_1-1103.jpg",
    id: "TopClothes_39",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/719ba0ae2b444039b6f9cec9785d0334.female_Sum34_2-854.jpg",
    id: "TopClothes_40",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/c10be29070b548a792487384de49ca04.female_Sum17_2-1010.jpg",
    id: "TopClothes_41",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/c2a4bc585f8b44d6983ece5b2fcee963.female_Sum1_1-1115.jpg",
    id: "TopClothes_42",
    type: "TopClothes",
  },
  {
    image:
      "/TopClothes/SleevelessJPG/fb9a8fdffe5b4cc69f3e1052feb24d58.female_Sum2_1-1111.jpg",
    id: "TopClothes_43",
    type: "TopClothes",
  },
];

export const BOTTOM_CLOTH_IMAGES: ClothingItem[] = [
  {
    image:
      "/bottom/bottom-long-skirts/12022c899dae480594c4048cd19a9dbf.female_1057_01-217.jpg",
    id: "bottom_1",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/2406b49e0a7541d9b8c6dc2146142ead.female_Sum36_2-850.jpg",
    id: "bottom_2",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/2e385a3198c942d3968a732aa6f02088.female_Sum33_1-1095.jpg",
    id: "bottom_3",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/5a0f829973be4dfcb59b13a3b401cc47.female_Sum44_1-731.jpg",
    id: "bottom_4",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/5e13c52aa8ca4e9a9b301198385a40b9.female_Sum33_1-1095.jpg",
    id: "bottom_5",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/9db28fe779404a1d834d48badfcbb146.female_Sum35_1-867.jpg",
    id: "bottom_6",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/9f1476a311744f4892ae450a468dfc8f.female_1095_01-367.jpg",
    id: "bottom_7",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/cbaf9aeca24944699a36bb93a154d69c.female_1060_02-230.jpg",
    id: "bottom_8",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-long-skirts/d3a8ff14d5db4bc1b1668a0abcafa512.female_1120_01-433.jpg",
    id: "bottom_9",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/00a3fbcf13c24b05a71de53b25263b45.female_1076_04-296.jpg",
    id: "bottom_10",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/3af7190c6cf94c6e9d3e5249edaf5830.female_aliyun_08-482.jpg",
    id: "bottom_11",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/82f5e9b8547648f18a2aeeab89183211.female_1107_01-403.jpg",
    id: "bottom_12",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/85ae94134da945f2af338bf981599ae6.female_1089_01-349.jpg",
    id: "bottom_13",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/a3f1ebbeaa1c4e24920c34d343c92869.female_1122_01-437.jpg",
    id: "bottom_14",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/acaa31eaaf354e4585d8784f1ed850ec.female_scene4_1-1055.jpg",
    id: "bottom_15",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/ad850ed0ad4b478abae3b11dfdd07b21.female_scene5_1-749.jpg",
    id: "bottom_16",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/cc6bb9f211a440d5a4e3ce6084f152da.female_1042_01-149.jpg",
    id: "bottom_17",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/e2dfc5c8501d4e4bb31bb58a19e41681.female_aliyun_02-476.jpg",
    id: "bottom_18",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-shorts/f5b85dbe3bf84be69ede4de696036700.female_1076_11-303.jpg",
    id: "bottom_19",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/146d175285ef41f68070354b7ca325a4.female_Sum32_2-1071.jpg",
    id: "bottom_20",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/1955d7256a0845baa22b81ae0c59e2f7.female_1041_03-147.jpg",
    id: "bottom_21",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/9e9cc7e00aea4812924fb75a7e25d17d.female_1076_13-305.jpg",
    id: "bottom_22",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/aa2f6889fb494ddcb305cbc835cb2a3a.female_scene6_1-755.jpg",
    id: "bottom_23",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/adfe68bd24cb436fbdd452139ffd764c.female_1103_01-391.jpg",
    id: "bottom_24",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/af1c5cc73b0e41dfb0a3ce19630df6c3.female_1071_04-276.jpg",
    id: "bottom_25",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/b8461c87323e4d9cbc33b27af7c7d7ae.female_Sum13_2-1060.jpg",
    id: "bottom_26",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/d2ff9a315a8c49db929b917d633846ce.female_1054_01-201.jpg",
    id: "bottom_27",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/d7c0298e13014dd09a7780b8abd8493b.female_Sum12_2-1062.jpg",
    id: "bottom_28",
    type: "bottom",
  },
  {
    image:
      "/bottom/bottom-skirts/e6148e09459b423f91fb135717de551e.female_1109_01-409.jpg",
    id: "bottom_29",
    type: "bottom",
  },
];
