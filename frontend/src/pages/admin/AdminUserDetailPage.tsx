import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Mock user data
const mockUser = {
  id: "usr-001",
  name: "<PERSON>",
  email: "<EMAIL>",
  plan: "Pro",
  joinDate: "May 16, 2025",
  status: "active",
  lastActive: "Today, 10:23 AM",
  credits: 87,
  totalSpent: "$237.00",
  avatar: "https://i.pravatar.cc/150?u=emma",
  phone: "+****************",
  company: "Thompson Creative",
  role: "user",
  billingAddress: {
    street: "123 Main St",
    city: "San Francisco",
    state: "CA",
    zip: "94105",
    country: "USA",
  },
};

// Mock billing history
const mockBillingHistory = [
  {
    id: "inv-001",
    date: "May 15, 2025",
    amount: "$49.00",
    description: "Pro Plan - Monthly",
    status: "paid",
  },
  {
    id: "inv-002",
    date: "April 15, 2025",
    amount: "$49.00",
    description: "Pro Plan - Monthly",
    status: "paid",
  },
  {
    id: "inv-003",
    date: "March 15, 2025",
    amount: "$49.00",
    description: "Pro Plan - Monthly",
    status: "paid",
  },
  {
    id: "inv-004",
    date: "May 10, 2025",
    amount: "$20.00",
    description: "Credit Pack - 100 Credits",
    status: "paid",
  },
  {
    id: "inv-005",
    date: "April 28, 2025",
    amount: "$20.00",
    description: "Credit Pack - 100 Credits",
    status: "paid",
  },
];

// Mock usage history
const mockUsageHistory = [
  {
    id: "job-001",
    date: "May 17, 2025, 14:32",
    type: "video",
    description: "Product Explainer Video",
    status: "completed",
    credits: 10,
  },
  {
    id: "job-002",
    date: "May 16, 2025, 11:15",
    type: "image",
    description: "Marketing Banner Generation",
    status: "completed",
    credits: 2,
  },
  {
    id: "job-003",
    date: "May 15, 2025, 16:48",
    type: "bgRemoval",
    description: "Product Image Background Removal",
    status: "completed",
    credits: 1,
  },
  {
    id: "job-004",
    date: "May 14, 2025, 09:27",
    type: "video",
    description: "Company Introduction Video",
    status: "completed",
    credits: 10,
  },
  {
    id: "job-005",
    date: "May 13, 2025, 15:19",
    type: "image",
    description: "Social Media Post Images",
    status: "completed",
    credits: 5,
  },
];

const AdminUserDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState(mockUser);
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState(mockUser);
  const [activeTab, setActiveTab] = useState("overview");
  console.log(activeTab);
  useEffect(() => {
    // In a real app, fetch user data based on ID
    // For now, we'll just use our mock data
    console.log(`Fetching user with ID: ${id}`);
  }, [id]);

  const handleInputChange = (field: string, value: string) => {
    setEditedUser({
      ...editedUser,
      [field]: value,
    });
  };

  const handleAddressChange = (field: string, value: string) => {
    setEditedUser({
      ...editedUser,
      billingAddress: {
        ...editedUser.billingAddress,
        [field]: value,
      },
    });
  };

  const handleSave = () => {
    // In a real app, save changes to API
    setUser(editedUser);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedUser(user);
    setIsEditing(false);
  };

  const handleDeleteUser = () => {
    // In a real app, delete user via API
    alert("User would be deleted in a real application");
    navigate("/admin/users");
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Details</h1>
          <p className="text-muted-foreground">
            View and manage user information
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate("/admin/users")}>
            Back to Users
          </Button>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>Edit User</Button>
          ) : (
            <Button onClick={handleSave}>Save Changes</Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* User Profile Card */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <div className="w-32 h-32 rounded-full overflow-hidden mb-4">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-full h-full object-cover"
              />
            </div>
            <h3 className="text-xl font-medium">{user.name}</h3>
            <p className="text-muted-foreground">{user.email}</p>
            <div className="mt-2 flex items-center">
              <span
                className={`inline-block w-2 h-2 rounded-full mr-2 ${
                  user.status === "active" ? "bg-green-500" : "bg-red-500"
                }`}
              ></span>
              <span className="capitalize">{user.status}</span>
            </div>
            <div className="mt-4 w-full">
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Plan</span>
                <span className="font-medium">{user.plan}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Credits</span>
                <span className="font-medium">{user.credits}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Joined</span>
                <span className="font-medium">{user.joinDate}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Last Active</span>
                <span className="font-medium">{user.lastActive}</span>
              </div>
              <div className="flex justify-between py-2">
                <span className="text-muted-foreground">Total Spent</span>
                <span className="font-medium">{user.totalSpent}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-2">
            <Button variant="outline" className="w-full">
              Add Credits
            </Button>
            <Button
              variant="destructive"
              className="w-full"
              onClick={handleDeleteUser}
            >
              Delete User
            </Button>
          </CardFooter>
        </Card>

        {/* User Details Tabs */}
        <div className="md:col-span-3">
          <Tabs
            defaultValue="overview"
            className="w-full"
            onValueChange={setActiveTab}
          >
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="billing">Billing</TabsTrigger>
              <TabsTrigger value="usage">Usage</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <Card>
                <CardHeader>
                  <CardTitle>User Information</CardTitle>
                  <CardDescription>
                    Personal and account details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Name</label>
                      {isEditing ? (
                        <Input
                          value={editedUser.name}
                          onChange={(e) =>
                            handleInputChange("name", e.target.value)
                          }
                        />
                      ) : (
                        <div className="p-2 border rounded-md">{user.name}</div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Email</label>
                      {isEditing ? (
                        <Input
                          type="email"
                          value={editedUser.email}
                          onChange={(e) =>
                            handleInputChange("email", e.target.value)
                          }
                        />
                      ) : (
                        <div className="p-2 border rounded-md">
                          {user.email}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Phone</label>
                      {isEditing ? (
                        <Input
                          value={editedUser.phone}
                          onChange={(e) =>
                            handleInputChange("phone", e.target.value)
                          }
                        />
                      ) : (
                        <div className="p-2 border rounded-md">
                          {user.phone}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Company</label>
                      {isEditing ? (
                        <Input
                          value={editedUser.company}
                          onChange={(e) =>
                            handleInputChange("company", e.target.value)
                          }
                        />
                      ) : (
                        <div className="p-2 border rounded-md">
                          {user.company}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Role</label>
                      {isEditing ? (
                        <select
                          className="w-full p-2 border rounded-md"
                          value={editedUser.role}
                          onChange={(e) =>
                            handleInputChange("role", e.target.value)
                          }
                        >
                          <option value="USER">User</option>
                          <option value="ADMIN">Admin</option>
                        </select>
                      ) : (
                        <div className="p-2 border rounded-md capitalize">
                          {user.role}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Status</label>
                      {isEditing ? (
                        <select
                          className="w-full p-2 border rounded-md"
                          value={editedUser.status}
                          onChange={(e) =>
                            handleInputChange("status", e.target.value)
                          }
                        >
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                        </select>
                      ) : (
                        <div className="p-2 border rounded-md capitalize">
                          {user.status}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="pt-4">
                    <h3 className="text-lg font-medium mb-4">
                      Billing Address
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Street</label>
                        {isEditing ? (
                          <Input
                            value={editedUser.billingAddress.street}
                            onChange={(e) =>
                              handleAddressChange("street", e.target.value)
                            }
                          />
                        ) : (
                          <div className="p-2 border rounded-md">
                            {user.billingAddress.street}
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">City</label>
                        {isEditing ? (
                          <Input
                            value={editedUser.billingAddress.city}
                            onChange={(e) =>
                              handleAddressChange("city", e.target.value)
                            }
                          />
                        ) : (
                          <div className="p-2 border rounded-md">
                            {user.billingAddress.city}
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">State</label>
                        {isEditing ? (
                          <Input
                            value={editedUser.billingAddress.state}
                            onChange={(e) =>
                              handleAddressChange("state", e.target.value)
                            }
                          />
                        ) : (
                          <div className="p-2 border rounded-md">
                            {user.billingAddress.state}
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">ZIP Code</label>
                        {isEditing ? (
                          <Input
                            value={editedUser.billingAddress.zip}
                            onChange={(e) =>
                              handleAddressChange("zip", e.target.value)
                            }
                          />
                        ) : (
                          <div className="p-2 border rounded-md">
                            {user.billingAddress.zip}
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Country</label>
                        {isEditing ? (
                          <Input
                            value={editedUser.billingAddress.country}
                            onChange={(e) =>
                              handleAddressChange("country", e.target.value)
                            }
                          />
                        ) : (
                          <div className="p-2 border rounded-md">
                            {user.billingAddress.country}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
                {isEditing && (
                  <CardFooter className="flex justify-end gap-2">
                    <Button variant="outline" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button onClick={handleSave}>Save Changes</Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>

            <TabsContent value="billing">
              <Card>
                <CardHeader>
                  <CardTitle>Billing History</CardTitle>
                  <CardDescription>
                    View user's billing and payment history
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">Invoice</th>
                          <th className="text-left py-3 px-4">Date</th>
                          <th className="text-left py-3 px-4">Description</th>
                          <th className="text-left py-3 px-4">Amount</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockBillingHistory.map((invoice) => (
                          <tr key={invoice.id} className="border-b">
                            <td className="py-3 px-4">{invoice.id}</td>
                            <td className="py-3 px-4">{invoice.date}</td>
                            <td className="py-3 px-4">{invoice.description}</td>
                            <td className="py-3 px-4">{invoice.amount}</td>
                            <td className="py-3 px-4 capitalize">
                              <span
                                className={`inline-block px-2 py-1 rounded-full text-xs ${
                                  invoice.status === "paid"
                                    ? "bg-green-100 text-green-800"
                                    : invoice.status === "pending"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {invoice.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-right">
                              <Button variant="ghost" size="sm">
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline">Download All Invoices</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="usage">
              <Card>
                <CardHeader>
                  <CardTitle>Usage History</CardTitle>
                  <CardDescription>
                    View user's platform usage history
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">ID</th>
                          <th className="text-left py-3 px-4">Date</th>
                          <th className="text-left py-3 px-4">Type</th>
                          <th className="text-left py-3 px-4">Description</th>
                          <th className="text-left py-3 px-4">Credits</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockUsageHistory.map((job) => (
                          <tr key={job.id} className="border-b">
                            <td className="py-3 px-4">{job.id}</td>
                            <td className="py-3 px-4">{job.date}</td>
                            <td className="py-3 px-4 capitalize">
                              {job.type === "video"
                                ? "Video Generation"
                                : job.type === "image"
                                ? "Image Generation"
                                : "Background Removal"}
                            </td>
                            <td className="py-3 px-4">{job.description}</td>
                            <td className="py-3 px-4">{job.credits}</td>
                            <td className="py-3 px-4">
                              <span
                                className={`inline-block px-2 py-1 rounded-full text-xs ${
                                  job.status === "completed"
                                    ? "bg-green-100 text-green-800"
                                    : job.status === "processing"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {job.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-right">
                              <Button variant="ghost" size="sm">
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings">
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                  <CardDescription>
                    Manage user account settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Plan Management</h3>
                    <div className="p-4 border rounded-md">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">{user.plan} Plan</h4>
                          <p className="text-sm text-muted-foreground">
                            {user.plan === "Free"
                              ? "Limited access to platform features"
                              : user.plan === "Starter"
                              ? "Basic access to all platform features"
                              : user.plan === "Pro"
                              ? "Advanced access with higher usage limits"
                              : "Unlimited access to all platform features"}
                          </p>
                        </div>
                        <Button variant="outline">Change Plan</Button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Credits</h3>
                    <div className="p-4 border rounded-md">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">
                            {user.credits} Credits Available
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Used for pay-as-you-go content generation
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline">Add Credits</Button>
                          <Button variant="outline">Transfer Credits</Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Account Actions</h3>
                    <div className="flex flex-col gap-4">
                      <div className="p-4 border rounded-md">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium">Reset Password</h4>
                            <p className="text-sm text-muted-foreground">
                              Send a password reset email to the user
                            </p>
                          </div>
                          <Button variant="outline">Send Reset Email</Button>
                        </div>
                      </div>
                      <div className="p-4 border rounded-md">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium">Account Status</h4>
                            <p className="text-sm text-muted-foreground">
                              {user.status === "active"
                                ? "User can currently access the platform"
                                : "User is currently blocked from accessing the platform"}
                            </p>
                          </div>
                          <Button
                            variant={
                              user.status === "active"
                                ? "destructive"
                                : "outline"
                            }
                          >
                            {user.status === "active"
                              ? "Deactivate Account"
                              : "Activate Account"}
                          </Button>
                        </div>
                      </div>
                      <div className="p-4 border rounded-md border-destructive">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium text-destructive">
                              Delete Account
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              Permanently delete this user account and all
                              associated data
                            </p>
                          </div>
                          <Button
                            variant="destructive"
                            onClick={handleDeleteUser}
                          >
                            Delete Account
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AdminUserDetailPage;
