import ShadowButton from "@/components/ui/shadowButton";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import * as Select from "@radix-ui/react-select";
import { ChevronDownIcon } from "lucide-react";
import { useEffect, useState } from "react";

// Mock data for blog posts
const mockBlogPosts = [
  {
    id: "post-001",
    title: "Getting Started with AI Video Generation",
    slug: "getting-started-with-ai-video-generation",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "<PERSON>",
    publishedAt: "May 16, 2025",
    status: "published",
    featuredImageUrl: "https://picsum.photos/seed/bridge1/400/200",
    categories: ["Tutorials"],
    views: 1245,
  },
  {
    id: "post-002",
    title: "Getting Started with AI Video Generation",
    slug: "10-creative-ways-to-use-ai-generated-images",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "<PERSON>",
    publishedAt: "May 14, 2025",
    status: "published",
    featuredImageUrl: "https://picsum.photos/seed/bridge2/400/200",
    categories: ["Tutorials"],
    views: 987,
  },
  {
    id: "post-003",
    title: "Getting Started with AI Video Generation",
    slug: "the-future-of-content-creation-with-ai",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "Sophia Williams",
    publishedAt: "May 10, 2025",
    status: "published",
    featuredImageUrl: "https://picsum.photos/seed/bridge3/400/200",
    categories: ["Tutorials"],
    views: 1876,
  },
  {
    id: "post-004",
    title: "Getting Started with AI Video Generation",
    slug: "how-to-remove-backgrounds-like-a-pro",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "David Brown",
    publishedAt: "May 8, 2025",
    status: "published",
    featuredImageUrl: "https://picsum.photos/seed/bridge4/400/200",
    categories: ["Tutorials"],
    views: 756,
  },
  {
    id: "post-005",
    title: "Getting Started with AI Video Generation",
    slug: "comparing-ai-video-platforms-in-2025",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "Emma Thompson",
    publishedAt: null,
    status: "draft",
    featuredImageUrl: "https://picsum.photos/seed/bridge5/400/200",
    categories: ["Tutorials"],
    views: 0,
  },
  {
    id: "post-006",
    title: "Getting Started with AI Video Generation",
    slug: "optimizing-your-ai-prompts-for-better-results",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "Michael Johnson",
    publishedAt: null,
    status: "draft",
    featuredImageUrl: "https://picsum.photos/seed/bridge6/400/200",
    categories: ["Tutorials"],
    views: 0,
  },
  {
    id: "post-007",
    title: "Getting Started with AI Video Generation",
    slug: "case-study-how-company-x-increased-engagement-with-ai-videos",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "Sophia Williams",
    publishedAt: "May 5, 2025",
    status: "published",
    featuredImageUrl: "https://picsum.photos/seed/bridge7/400/200",
    categories: ["Tutorials"],
    views: 543,
  },
  {
    id: "post-008",
    title: "Getting Started with AI Video Generation",
    slug: "the-ethics-of-ai-generated-content",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "David Brown",
    publishedAt: "May 3, 2025",
    status: "published",
    featuredImageUrl: "https://picsum.photos/seed/bridge8/400/200",
    categories: ["Tutorials"],
    views: 1102,
  },
  {
    id: "post-009",
    title: "Getting Started with AI Video Generation",
    slug: "advanced-video-editing-techniques",
    excerpt: "+12% from last month. Learn how to create professional stuff.",
    author: "Alice Cooper",
    publishedAt: "May 1, 2025",
    status: "published",
    featuredImageUrl: "https://picsum.photos/seed/bridge9/400/200",
    categories: ["Tutorials"],
    views: 892,
  },
];

type PostStatus = "all" | "published" | "draft";
type PostCategory = "all" | string;

const AdminBlogPage = () => {
  const { setHeaderContent } = useAdminHeader();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<PostStatus>("all");
  const [categoryFilter, setCategoryFilter] = useState<PostCategory>("all");
  const [selectedPost, setSelectedPost] = useState<
    (typeof mockBlogPosts)[0] | null
  >(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  useEffect(() => {
    setHeaderContent({
      title: "Blog Management",
      description: "Create, edit, and manage blog posts",
      content: (
        <button className="px-4 py-2 rounded-lg border-2 border-gray-500 flex items-center gap-2 transition-colors">
          <span className="text-white text-lg">+ &nbsp; |</span>
          <span className="text-purple-500">Add New Post</span>
        </button>
      ),
    });
    return () => setHeaderContent({});
  }, []);

  const tabs = [
    { id: "all", label: "All Posts" },
    { id: "published", label: "Published" },
    { id: "draft", label: "Drafts" },
  ];

  const handleTabChange = (tabId: PostStatus) => {
    setActiveTab(tabId);
  };

  // Get unique categories from all posts
  const allCategories = Array.from(
    new Set(mockBlogPosts.flatMap((post) => post.categories))
  );

  // Filter posts based on search query and filters
  const filteredPosts = mockBlogPosts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.author.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = activeTab === "all" || post.status === activeTab;

    const matchesCategory =
      categoryFilter === "all" ||
      post.categories.includes(categoryFilter as string);

    return matchesSearch && matchesStatus && matchesCategory;
  });

  const handleDeletePost = (id: string) => {
    console.log(`Deleting post with ID: ${id}`);
    setIsDeleteModalOpen(false);
    setSelectedPost(null);
  };

  return (
    <div className="min-h-screen text-white p-6">
      <div className="mx-auto">
        <div className="flex justify-between items-center border-b-2 border-gray-600 pb-3 mb-6">
          {/* Tabs */}
          <div className="flex gap-10 space-x-1 px-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id as PostStatus)}
                className={`text-sm font-medium rounded-lg cursor-pointer transition-colors ${
                  activeTab === tab.id
                    ? "text-white"
                    : "text-gray-400 hover:text-purple-500"
                }`}
                type="button"
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Search and Filter Bar */}
          <div className="flex justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <Select.Root
                value={categoryFilter}
                onValueChange={setCategoryFilter} // Directly update filter
              >
                <Select.Trigger
                  className="flex items-center justify-between bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-sm text-gray-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 w-full"
                  aria-label="Category"
                >
                  <Select.Value placeholder="Select a category" />
                  <Select.Icon>
                    <ChevronDownIcon size={16} />
                  </Select.Icon>
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50 overflow-hidden">
                    <Select.Viewport className="p-2">
                      <Select.Item
                        value="all"
                        className="text-gray-200 text-sm cursor-pointer px-4 py-2 hover:bg-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <Select.ItemText>All Categories</Select.ItemText>
                      </Select.Item>
                      {allCategories.map((category) => (
                        <Select.Item
                          key={category}
                          value={category}
                          className="text-gray-200 text-sm cursor-pointer px-4 py-2 hover:bg-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          <Select.ItemText>{category}</Select.ItemText>
                        </Select.Item>
                      ))}
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-white/10 border border-gray-600 text-white px-4 py-2 pl-10 rounded-lg text-sm focus:outline-none focus:border-purple-500 w-64"
              />
              <svg
                className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Blog Posts Grid */}
        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.map((post) => (
              <div
                key={post.id}
                className="bg-white/5 rounded-2xl overflow-hidden border border-gray-700 hover:border-gray-600 transition-colors"
              >
                <div className="relative">
                  <img
                    src={post.featuredImageUrl}
                    alt={post.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-3 right-3">
                    <span
                      style={{
                        boxShadow: "inset 0 0 10px rgba(255, 255, 255, 0.6)", // Strong white inner shadow
                      }}
                      className="bg-purple-600 text-white px-5 py-[6px] rounded-lg text-xs font-medium"
                    >
                      {post.status.charAt(0).toUpperCase() +
                        post.status.slice(1)}
                    </span>
                  </div>
                </div>

                <div className="p-4">
                  <h3 className="font-semibold mb-2 text-sm text-white line-clamp-2">
                    {post.title}
                  </h3>

                  <p className="text-gray-400 text-sm mb-2 line-clamp-2">
                    {post.excerpt}
                  </p>

                  <div className="flex items-center justify-between mb-2">
                    {post.categories?.map((cat) => (
                      <span className="text-white px-3 py-[2px] rounded text-xs rounded-full border border-gray-600">
                        {cat}
                      </span>
                    ))}
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-gray-400 text-sm">
                      <svg
                        className="w-4 h-4 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                      {post.views > 0
                        ? `${
                            post.views > 1000
                              ? `${(post.views / 1000).toFixed(1)}k`
                              : post.views
                          }`
                        : "0"}
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex gap-2">
                        <button className="border border-gray-500 p-2 hover:bg-gray-700 rounded-lg text-white hover:bg-white hover:text-primary cursor-pointer transition-colors">
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                            />
                          </svg>
                        </button>
                        <button
                          className="border border-gray-500 p-2 hover:bg-gray-700 rounded-lg text-white hover:bg-white hover:text-primary cursor-pointer transition-colors"
                          onClick={() => {
                            setSelectedPost(post);
                            setIsDeleteModalOpen(true);
                          }}
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-4">
              <svg
                className="w-16 h-16 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-medium mb-2">No posts found</h3>
            <p className="text-gray-400 mb-6">
              {searchQuery
                ? `No results for "${searchQuery}"`
                : "No blog posts match your current filters"}
            </p>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => {
                  setSearchQuery("");
                  setActiveTab("all");
                  setCategoryFilter("all");
                }}
                className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 transition-colors"
              >
                Clear Filters
              </button>
              <ShadowButton className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                Create New Post
              </ShadowButton>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {selectedPost && isDeleteModalOpen && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-lg shadow-lg max-w-md w-full p-6 border border-gray-700">
              <h3 className="text-lg font-medium mb-2 text-white">
                Delete Blog Post
              </h3>
              <p className="text-gray-300 mb-6">
                Are you sure you want to delete the blog post "
                {selectedPost.title}"? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDeletePost(selectedPost.id)}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminBlogPage;

// import { useState } from "react";
// import { Link } from "react-router-dom";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Card, CardContent } from "@/components/ui/card";

// // Mock data for blog posts
// const mockBlogPosts = [
//   {
//     id: "post-001",
//     title: "Getting Started with AI Video Generation",
//     slug: "getting-started-with-ai-video-generation",
//     excerpt:
//       "Learn how to create professional videos with AI avatars using Miragic-AI's video generation tool.",
//     author: "Emma Thompson",
//     publishedAt: "May 16, 2025",
//     status: "published",
//     featuredImageUrl: "https://picsum.photos/seed/1/800/400",
//     categories: ["Tutorials", "Video Generation"],
//     views: 1245,
//   },
//   {
//     id: "post-002",
//     title: "10 Creative Ways to Use AI-Generated Images",
//     slug: "10-creative-ways-to-use-ai-generated-images",
//     excerpt:
//       "Discover innovative ways to leverage AI-generated images for your marketing, social media, and design projects.",
//     author: "Michael Johnson",
//     publishedAt: "May 14, 2025",
//     status: "published",
//     featuredImageUrl: "https://picsum.photos/seed/2/800/400",
//     categories: ["Inspiration", "Image Generation"],
//     views: 987,
//   },
//   {
//     id: "post-003",
//     title: "The Future of Content Creation with AI",
//     slug: "the-future-of-content-creation-with-ai",
//     excerpt:
//       "Explore how artificial intelligence is revolutionizing content creation across industries.",
//     author: "Sophia Williams",
//     publishedAt: "May 10, 2025",
//     status: "published",
//     featuredImageUrl: "https://picsum.photos/seed/3/800/400",
//     categories: ["Insights", "AI Technology"],
//     views: 1876,
//   },
//   {
//     id: "post-004",
//     title: "How to Remove Backgrounds Like a Pro",
//     slug: "how-to-remove-backgrounds-like-a-pro",
//     excerpt:
//       "Master the art of background removal with our step-by-step guide using Miragic-AI's tools.",
//     author: "David Brown",
//     publishedAt: "May 8, 2025",
//     status: "published",
//     featuredImageUrl: "https://picsum.photos/seed/4/800/400",
//     categories: ["Tutorials", "Background Removal"],
//     views: 756,
//   },
//   {
//     id: "post-005",
//     title: "Comparing AI Video Platforms in 2025",
//     slug: "comparing-ai-video-platforms-in-2025",
//     excerpt:
//       "An in-depth comparison of the leading AI video generation platforms available today.",
//     author: "Emma Thompson",
//     publishedAt: null,
//     status: "draft",
//     featuredImageUrl: "https://picsum.photos/seed/5/800/400",
//     categories: ["Reviews", "Video Generation"],
//     views: 0,
//   },
//   {
//     id: "post-006",
//     title: "Optimizing Your AI Prompts for Better Results",
//     slug: "optimizing-your-ai-prompts-for-better-results",
//     excerpt:
//       "Learn how to craft effective prompts that yield the best results from AI generation tools.",
//     author: "Michael Johnson",
//     publishedAt: null,
//     status: "draft",
//     featuredImageUrl: "https://picsum.photos/seed/6/800/400",
//     categories: ["Tips & Tricks", "AI Technology"],
//     views: 0,
//   },
//   {
//     id: "post-007",
//     title: "Case Study: How Company X Increased Engagement with AI Videos",
//     slug: "case-study-how-company-x-increased-engagement-with-ai-videos",
//     excerpt:
//       "Discover how Company X achieved a 300% increase in engagement using AI-generated videos.",
//     author: "Sophia Williams",
//     publishedAt: "May 5, 2025",
//     status: "published",
//     featuredImageUrl: "https://picsum.photos/seed/7/800/400",
//     categories: ["Case Studies", "Video Generation"],
//     views: 543,
//   },
//   {
//     id: "post-008",
//     title: "The Ethics of AI-Generated Content",
//     slug: "the-ethics-of-ai-generated-content",
//     excerpt:
//       "Exploring the ethical considerations and best practices for using AI-generated content.",
//     author: "David Brown",
//     publishedAt: "May 3, 2025",
//     status: "published",
//     featuredImageUrl: "https://picsum.photos/seed/8/800/400",
//     categories: ["Insights", "AI Technology"],
//     views: 1102,
//   },
// ];

// type PostStatus = "all" | "published" | "draft";
// type PostCategory = "all" | string;

// const AdminBlogPage = () => {
//   const [searchQuery, setSearchQuery] = useState("");
//   const [statusFilter, setStatusFilter] = useState<PostStatus>("all");
//   const [categoryFilter, setCategoryFilter] = useState<PostCategory>("all");
//   const [selectedPost, setSelectedPost] = useState<
//     (typeof mockBlogPosts)[0] | null
//   >(null);
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

//   // Get unique categories from all posts
//   const allCategories = Array.from(
//     new Set(mockBlogPosts.flatMap((post) => post.categories))
//   );

//   // Filter posts based on search query and filters
//   const filteredPosts = mockBlogPosts.filter((post) => {
//     const matchesSearch =
//       post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
//       post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
//       post.author.toLowerCase().includes(searchQuery.toLowerCase());

//     const matchesStatus =
//       statusFilter === "all" || post.status === statusFilter;

//     const matchesCategory =
//       categoryFilter === "all" ||
//       post.categories.includes(categoryFilter as string);

//     return matchesSearch && matchesStatus && matchesCategory;
//   });

//   const handleDeletePost = (id: string) => {
//     // In a real app, this would call your API to delete the post
//     console.log(`Deleting post with ID: ${id}`);
//     setIsDeleteModalOpen(false);
//     setSelectedPost(null);
//     // For demo purposes, we'll just close the modal
//   };

//   return (
//     <div className="space-y-8">
//       <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
//         <div>
//           <h1 className="text-3xl font-bold tracking-tight">Blog Management</h1>
//           <p className="text-muted-foreground">
//             Create, edit, and manage blog posts
//           </p>
//         </div>
//         <Link to="/admin/blog/new">
//           <Button>
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               fill="none"
//               viewBox="0 0 24 24"
//               strokeWidth={1.5}
//               stroke="currentColor"
//               className="w-4 h-4 mr-2"
//             >
//               <path
//                 strokeLinecap="round"
//                 strokeLinejoin="round"
//                 d="M12 4.5v15m7.5-7.5h-15"
//               />
//             </svg>
//             New Post
//           </Button>
//         </Link>
//       </div>

//       {/* Filters and Search */}
//       <div className="flex flex-col md:flex-row justify-between gap-4">
//         <div className="flex flex-wrap gap-2">
//           <Button
//             variant={statusFilter === "all" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setStatusFilter("all")}
//           >
//             All Posts
//           </Button>
//           <Button
//             variant={statusFilter === "published" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setStatusFilter("published")}
//           >
//             Published
//           </Button>
//           <Button
//             variant={statusFilter === "draft" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setStatusFilter("draft")}
//           >
//             Drafts
//           </Button>
//         </div>
//         <div className="flex flex-wrap gap-2">
//           <select
//             className="h-9 px-3 py-2 text-sm rounded-md border border-input bg-background"
//             value={categoryFilter}
//             onChange={(e) => setCategoryFilter(e.target.value)}
//           >
//             <option value="all">All Categories</option>
//             {allCategories.map((category) => (
//               <option key={category} value={category}>
//                 {category}
//               </option>
//             ))}
//           </select>
//           <div className="w-full md:w-64">
//             <Input
//               placeholder="Search posts..."
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//             />
//           </div>
//         </div>
//       </div>

//       {/* Blog Posts Grid */}
//       {filteredPosts.length > 0 ? (
//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//           {filteredPosts.map((post) => (
//             <Card key={post.id} className="overflow-hidden">
//               <div className="relative aspect-video bg-muted">
//                 <img
//                   src={post.featuredImageUrl}
//                   alt={post.title}
//                   className="w-full h-full object-cover"
//                 />
//                 <div className="absolute top-2 right-2">
//                   <span
//                     className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
//                       ${
//                         post.status === "published"
//                           ? "bg-green-100 text-green-800"
//                           : "bg-yellow-100 text-yellow-800"
//                       }`}
//                   >
//                     {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
//                   </span>
//                 </div>
//               </div>
//               <CardContent className="p-4">
//                 <h3 className="font-medium text-lg truncate mb-1">
//                   {post.title}
//                 </h3>
//                 <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
//                   {post.excerpt}
//                 </p>
//                 <div className="flex justify-between items-center text-sm text-muted-foreground mb-4">
//                   <span>{post.author}</span>
//                   <span>{post.publishedAt || "Not published"}</span>
//                 </div>
//                 <div className="flex flex-wrap gap-1 mb-4">
//                   {post.categories.map((category) => (
//                     <span
//                       key={category}
//                       className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary"
//                     >
//                       {category}
//                     </span>
//                   ))}
//                 </div>
//                 <div className="flex justify-between">
//                   <div className="flex items-center text-sm text-muted-foreground">
//                     <svg
//                       xmlns="http://www.w3.org/2000/svg"
//                       fill="none"
//                       viewBox="0 0 24 24"
//                       strokeWidth={1.5}
//                       stroke="currentColor"
//                       className="w-4 h-4 mr-1"
//                     >
//                       <path
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"
//                       />
//                       <path
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
//                       />
//                     </svg>
//                     {post.views} views
//                   </div>
//                   <div className="flex gap-2">
//                     <Link to={`/admin/blog/edit/${post.id}`}>
//                       <Button variant="ghost" size="sm">
//                         <svg
//                           xmlns="http://www.w3.org/2000/svg"
//                           fill="none"
//                           viewBox="0 0 24 24"
//                           strokeWidth={1.5}
//                           stroke="currentColor"
//                           className="w-4 h-4"
//                         >
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
//                           />
//                         </svg>
//                       </Button>
//                     </Link>
//                     <Button
//                       variant="ghost"
//                       size="sm"
//                       onClick={() => {
//                         setSelectedPost(post);
//                         setIsDeleteModalOpen(true);
//                       }}
//                     >
//                       <svg
//                         xmlns="http://www.w3.org/2000/svg"
//                         fill="none"
//                         viewBox="0 0 24 24"
//                         strokeWidth={1.5}
//                         stroke="currentColor"
//                         className="w-4 h-4 text-destructive"
//                       >
//                         <path
//                           strokeLinecap="round"
//                           strokeLinejoin="round"
//                           d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
//                         />
//                       </svg>
//                     </Button>
//                   </div>
//                 </div>
//               </CardContent>
//             </Card>
//           ))}
//         </div>
//       ) : (
//         <Card className="p-8 text-center">
//           <div className="flex flex-col items-center justify-center">
//             <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
//               <svg
//                 xmlns="http://www.w3.org/2000/svg"
//                 fill="none"
//                 viewBox="0 0 24 24"
//                 strokeWidth={1.5}
//                 stroke="currentColor"
//                 className="w-8 h-8 text-muted-foreground"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
//                 />
//               </svg>
//             </div>
//             <h3 className="text-lg font-medium mb-2">No posts found</h3>
//             <p className="text-muted-foreground mb-4">
//               {searchQuery
//                 ? `No results for "${searchQuery}"`
//                 : "No blog posts match your current filters"}
//             </p>
//             <div className="flex gap-2">
//               <Button
//                 variant="outline"
//                 onClick={() => {
//                   setSearchQuery("");
//                   setStatusFilter("all");
//                   setCategoryFilter("all");
//                 }}
//               >
//                 Clear Filters
//               </Button>
//               <Link to="/admin/blog/new">
//                 <Button>Create New Post</Button>
//               </Link>
//             </div>
//           </div>
//         </Card>
//       )}

//       {/* Delete Confirmation Modal */}
//       {selectedPost && isDeleteModalOpen && (
//         <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
//           <div className="bg-background rounded-lg shadow-lg max-w-md w-full p-6">
//             <h3 className="text-lg font-medium mb-2">Delete Blog Post</h3>
//             <p className="text-muted-foreground mb-4">
//               Are you sure you want to delete the blog post "
//               {selectedPost.title}"? This action cannot be undone.
//             </p>
//             <div className="flex justify-end gap-2">
//               <Button
//                 variant="outline"
//                 onClick={() => setIsDeleteModalOpen(false)}
//               >
//                 Cancel
//               </Button>
//               <Button
//                 variant="destructive"
//                 onClick={() => handleDeletePost(selectedPost.id)}
//               >
//                 Delete
//               </Button>
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default AdminBlogPage;
