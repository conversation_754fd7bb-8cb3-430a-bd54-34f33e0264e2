import { useEffect, useMemo, useState } from "react";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import { Upload, Trash2, Plus } from "lucide-react";
import AdminService from "@/services/admin.service";

interface ModelImage {
  id: string;
  imagePath: string;
  modelName: string;
  gender: string;
  // bodyType: string;
  poseType: string;
  ethnicity: string;
  isDefault: boolean;
  usageCount: number;
  createdAt: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const AdminVirtualTryOnModelsPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [models, setModels] = useState<ModelImage[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [filter, setFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [showUploadForm, setShowUploadForm] = useState<boolean>(false);

  // Upload form state
  const [uploadData, setUploadData] = useState({
    modelImage: null as File | null,
    modelName: "",
    gender: "UNISEX",
    // bodyType: "AVERAGE",
    poseType: "STANDING",
    ethnicity: "MIXED",
  });

  const headerContent = useMemo(
    () => (
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          <Input
            placeholder="Search models..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64 bg-white/5"
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Models</SelectItem>
              <SelectItem value="true">Admin Models</SelectItem>
              {/* <SelectItem value="false">User Models</SelectItem> */}
            </SelectContent>
          </Select>
        </div>
        <Button
          onClick={() => setShowUploadForm(!showUploadForm)}
          className="bg-purple-600 hover:bg-purple-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Upload Model
        </Button>
      </div>
    ),
    [searchTerm, filter, showUploadForm]
  );

  useEffect(() => {
    setHeaderContent({
      title: "Virtual Try-On Models",
      description: "Manage model images for virtual try-on feature",
      content: headerContent,
    });

    return () => setHeaderContent({});
  }, [headerContent, setHeaderContent]);

  const fetchModels = async () => {
    try {
      setIsLoading(true);
      const response = await AdminService.getVirtualTryOnModels({
        page: pagination.page,
        limit: pagination.limit,
        isDefault: filter !== "all" ? filter : undefined,
        search: searchTerm || undefined,
      });

      if (response.success) {
        setModels(response.data.modelImages);
        setPagination(response.data.pagination);
      } else {
        toast.error("Failed to fetch models");
      }
    } catch (error) {
      console.error("Error fetching models:", error);
      toast.error("Failed to fetch models");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchModels();
  }, [pagination.page, filter, searchTerm]);

  const handleUpload = async () => {
    if (!uploadData.modelImage) {
      toast.error("Please select an image file");
      return;
    }

    try {
      setIsUploading(true);
      const response = await AdminService.uploadVirtualTryOnModel(uploadData);

      if (response.success) {
        toast.success("Model uploaded successfully");
        setShowUploadForm(false);
        setUploadData({
          modelImage: null,
          modelName: "",
          gender: "UNISEX",
          // bodyType: "AVERAGE",
          poseType: "STANDING",
          ethnicity: "MIXED",
        });
        fetchModels();
      } else {
        toast.error(response.message || "Failed to upload model");
      }
    } catch (error) {
      console.error("Error uploading model:", error);
      toast.error("Failed to upload model");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async (modelId: string) => {
    if (!confirm("Are you sure you want to delete this model?")) return;

    try {
      const response = await AdminService.deleteVirtualTryOnModel(modelId);
      if (response.success) {
        toast.success("Model deleted successfully");
        fetchModels();
      } else {
        toast.error("Failed to delete model");
      }
    } catch (error) {
      console.error("Error deleting model:", error);
      toast.error("Failed to delete model");
    }
  };

  return (
    <div className="space-y-6 px-4 sm:px-6 lg:px-8">
      {/* Upload Form */}
      {showUploadForm && (
        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader>
            <CardTitle>Upload New Model</CardTitle>
            <CardDescription>
              Add a new model image for virtual try-on
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="modelImage">Model Image</Label>
                <Input
                  id="modelImage"
                  type="file"
                  accept="image/*"
                  className="bg-white/5"
                  onChange={(e) =>
                    setUploadData({
                      ...uploadData,
                      modelImage: e.target.files?.[0] || null,
                    })
                  }
                />
              </div>
              <div>
                <Label htmlFor="modelName">Model Name</Label>
                <Input
                  id="modelName"
                  className="bg-white/5"
                  value={uploadData.modelName}
                  onChange={(e) =>
                    setUploadData({
                      ...uploadData,
                      modelName: e.target.value,
                    })
                  }
                  placeholder="Enter model name"
                />
              </div>
              <div>
                <Label htmlFor="gender">Gender</Label>
                <Select
                  value={uploadData.gender}
                  onValueChange={(value) =>
                    setUploadData({
                      ...uploadData,
                      gender: value,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MALE">Male</SelectItem>
                    <SelectItem value="FEMALE">Female</SelectItem>
                    <SelectItem value="UNISEX">Unisex</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* <div>
                <Label htmlFor="bodyType">Body Type</Label>
                <Select
                  value={uploadData.bodyType}
                  onValueChange={(value) =>
                    setUploadData({
                      ...uploadData,
                      bodyType: value,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SLIM">Slim</SelectItem>
                    <SelectItem value="AVERAGE">Average</SelectItem>
                    <SelectItem value="PLUS_SIZE">Plus Size</SelectItem>
                  </SelectContent>
                </Select>
              </div> */}
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleUpload}
                disabled={isUploading}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isUploading ? (
                  <Spinner className="w-4 h-4 mr-2" />
                ) : (
                  <Upload className="w-4 h-4 mr-2" />
                )}
                Upload Model
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowUploadForm(false)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Models Grid */}
      <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
        <CardHeader>
          <CardTitle>Model Images ({pagination.total})</CardTitle>
          <CardDescription>
            Manage model images for the virtual try-on feature
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Spinner className="w-8 h-8" />
            </div>
          ) : models.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              No models found
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {models.map((model) => (
                <div
                  key={model.id}
                  className="bg-white/10 rounded-lg overflow-hidden border border-gray-600"
                >
                  <div className="aspect-square relative">
                    <img
                      src={model.imagePath}
                      alt={model.modelName}
                      className="w-full h-full object-cover"
                    />
                    {model.isDefault && (
                      <div className="absolute top-2 left-2 bg-purple-600 text-white text-xs px-2 py-1 rounded">
                        Admin
                      </div>
                    )}
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium truncate">{model.modelName}</h3>
                    <p className="text-sm text-gray-400">{model.gender}</p>
                    <p className="text-xs text-gray-500">
                      Used {model.usageCount} times
                    </p>
                    <div className="flex gap-1 mt-2">
                      {/* <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Edit className="w-3 h-3" />
                      </Button> */}
                      <Button
                        size="sm"
                        variant="destructive"
                        className="flex-1"
                        onClick={() => handleDelete(model.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                disabled={pagination.page === 1}
                onClick={() =>
                  setPagination({ ...pagination, page: pagination.page - 1 })
                }
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                variant="outline"
                disabled={pagination.page === pagination.totalPages}
                onClick={() =>
                  setPagination({ ...pagination, page: pagination.page + 1 })
                }
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminVirtualTryOnModelsPage;
