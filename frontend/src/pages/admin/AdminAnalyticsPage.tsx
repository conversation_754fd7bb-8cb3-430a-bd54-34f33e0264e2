import React, { useEffect, useState } from "react";
import ShadowButton from "@/components/ui/shadowButton";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import AdminService from "@/services/admin.service";
import { toast } from "sonner";
import { Edit, Trash2 } from "lucide-react";

// Type definitions
type TabType = "overview" | "revenue" | "usage" | "users";
type TimeFrameType = "daily" | "weekly" | "monthly";

interface TabItem {
  id: TabType;
  label: string;
}

interface TimeFrameItem {
  id: TimeFrameType;
  label: string;
}

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalCredits: number;
  creditsUsed: number;
  videoGenerations: number;
  imageGenerations: number;
  backgroundRemovals: number;
  activeSubscriptions: number;
  activeJobs: number;
  completedJobs: number;
  blogPosts: number;
  conversionRate: number;
  totalJobs: number;
  backgroundRemoval?: number;
  imageGeneration?: number;
}

interface ChartDataPoint {
  date: string;
  amount: number;
  toString(): string;
}

interface TimeSeriesData {
  labels: string[];
  datasets: { label: string; data: number[] }[];
  daily?: ChartDataPoint[];
  weekly?: ChartDataPoint[];
  monthly?: ChartDataPoint[];
  videoGeneration?: ChartDataPoint[];
  imageGeneration?: ChartDataPoint[];
  backgroundRemoval?: ChartDataPoint[];
}

interface PlanDistribution {
  name: string;
  count: number;
  percentage: number;
}

interface UserRetention {
  period: string;
  rate: number;
}

interface UserActivity {
  dailyActiveUsers: number;
  monthlyActiveUsers: number;
  averageSessionDuration: number;
  averageActionsPerSession: number;
}

const defaultTimeSeriesData: TimeSeriesData = {
  labels: [],
  datasets: [{ label: "", data: [] }],
  daily: [],
  weekly: [],
  monthly: [],
  videoGeneration: [],
  imageGeneration: [],
  backgroundRemoval: [],
};

const AdminAnalyticsPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [activeTab, setActiveTab] = useState<TabType>("overview");
  const [timeFrame, setTimeFrame] = useState<TimeFrameType>("monthly");
  const [loading, setLoading] = useState<boolean>(true);
  const [adminStats, setAdminStats] = useState<AdminStats | null>(null);
  const [revenueData, setRevenueData] = useState<TimeSeriesData>(
    defaultTimeSeriesData
  );
  const [usageData, setUsageData] = useState<TimeSeriesData>(
    defaultTimeSeriesData
  );
  const [userGrowthData, setUserGrowthData] = useState<TimeSeriesData>(
    defaultTimeSeriesData
  );
  const [planDistribution, setPlanDistribution] = useState<PlanDistribution[]>(
    []
  );
  const [userRetention, setUserRetention] = useState<UserRetention[]>([]);
  const [userActivity, setUserActivity] = useState<UserActivity | null>(null);

  const tabs: TabItem[] = [
    { id: "overview", label: "Overview" },
    { id: "revenue", label: "Revenue" },
    { id: "usage", label: "Usage" },
    { id: "users", label: "Users" },
  ];

  const timeFrames: TimeFrameItem[] = [
    { id: "daily", label: "Daily" },
    { id: "weekly", label: "Weekly" },
    { id: "monthly", label: "Monthly" },
  ];

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const stats = await AdminService.getAdminStats();
      // const completeStats: AdminStats = {
      //   ...stats,
      //   totalJobs: stats.totalJobs || stats.activeJobs + stats.completedJobs,
      // };
      // setAdminStats(completeStats);

      const generateMockTimeSeriesData = (
        baseValue: number,
        label: string
      ): TimeSeriesData => {
        const labels: string[] = [];
        const data: number[] = [];
        const mockData: ChartDataPoint[] = [];
        const dataPoints =
          timeFrame === "daily" ? 30 : timeFrame === "weekly" ? 8 : 6;
        const today = new Date();
        for (let i = 0; i < dataPoints; i++) {
          const date = new Date();
          if (timeFrame === "daily") {
            date.setDate(today.getDate() - i);
          } else if (timeFrame === "weekly") {
            date.setDate(today.getDate() - i * 7);
          } else {
            date.setMonth(today.getMonth() - i);
          }
          const dateStr = date.toISOString().split("T")[0];
          labels.unshift(dateStr);
          const variation = Math.random() * 0.4 + 0.8;
          const amount = Math.round(baseValue * variation);
          data.unshift(amount);
          mockData.unshift({
            date: dateStr,
            amount,
            toString: () => `$${amount}`,
          });
        }
        return {
          labels,
          datasets: [{ label, data }],
          [timeFrame]: mockData,
        };
      };

      const monthlyRevenue = stats.monthlyRevenue || 9000;
      setRevenueData(
        generateMockTimeSeriesData(monthlyRevenue / 30, "Revenue")
      );

      const videoGenerations = stats.videoGenerations || 1000;
      const imageGenerations = stats.imageGenerations || 2000;
      const backgroundRemovals = stats.backgroundRemovals || 500;
      const totalUsage =
        videoGenerations + imageGenerations + backgroundRemovals;
      setUsageData({
        ...generateMockTimeSeriesData(totalUsage / 30, "Usage"),
        videoGeneration: generateMockTimeSeriesData(
          videoGenerations / 30,
          "Video"
        )[timeFrame],
        imageGeneration: generateMockTimeSeriesData(
          imageGenerations / 24,
          "Image"
        )[timeFrame],
        backgroundRemoval: generateMockTimeSeriesData(
          backgroundRemovals / 30,
          "Background"
        )[timeFrame],
      });

      const totalUsers = stats.totalUsers || 2000;
      setUserGrowthData(generateMockTimeSeriesData(totalUsers / 60, "Users"));

      setPlanDistribution([
        { name: "Active", count: Math.round(totalUsers * 0.5), percentage: 50 },
        {
          name: "Inactive",
          count: Math.round(totalUsers * 0.5),
          percentage: 50,
        },
      ]);

      setUserRetention([
        { period: "1 day", rate: 95 },
        { period: "7 days", rate: 80 },
        { period: "30 days", rate: 60 },
        { period: "90 days", rate: 40 },
      ]);

      setUserActivity({
        dailyActiveUsers: Math.round(totalUsers * 0.2),
        monthlyActiveUsers: totalUsers,
        averageSessionDuration: 120,
        averageActionsPerSession: 5,
      });
    } catch (error) {
      console.error("Error fetching analytics data:", error);
      toast.error("Failed to fetch analytics data");
      setDefaultData();
    } finally {
      setLoading(false);
    }
  };

  // Default mock data
  const setDefaultData = () => {
    setAdminStats({
      totalUsers: 1500,
      activeUsers: 300,
      totalRevenue: 10000,
      monthlyRevenue: 2000,
      totalCredits: 5000,
      creditsUsed: 2500,
      videoGenerations: 500,
      imageGenerations: 100,
      backgroundRemoval: 250,
      imageGeneration: 1000,
      backgroundRemovals: 100,
      activeSubscriptions: 50,
      activeJobs: 100,
      completedJobs: 200,
      blogPosts: 10,
      conversionRate: 15,
      totalJobs: 300,
    });
    setRevenueData({
      ...defaultTimeSeriesData,
      monthly: Array.from({ length: 6 }, (_, i) => ({
        date: new Date(2025, 5 - i, 1).toISOString().split("T")[0],
        amount: 2000 + i * 100,
        toString: () => `$2000`,
      })),
    });
    setUsageData({
      ...defaultTimeSeriesData,
      videoGeneration: Array.from({ length: 6 }, (_, i) => ({
        date: new Date(2025, 5 - i, 1).toISOString().split("T")[0],
        amount: 100 + i * 10,
        toString: () => `100`,
      })),
      imageGeneration: Array.from({ length: 6 }, (_, i) => ({
        date: new Date(2025, 5 - i, 1).toISOString().split("T")[0],
        amount: 200 + i * 20,
        toString: () => `200`,
      })),
      backgroundRemoval: Array.from({ length: 6 }, (_, i) => ({
        date: new Date(2025, 5 - i, 1).toISOString().split("T")[0],
        amount: 50 + i * 5,
        toString: () => `50`,
      })),
    });
    setUserGrowthData({
      ...defaultTimeSeriesData,
      monthly: Array.from({ length: 6 }, (_, i) => ({
        date: new Date(2025, 5 - i, 1).toISOString().split("T")[0],
        amount: 300 + i * 50,
        toString: () => `300`,
      })),
    });
    setPlanDistribution([
      { name: "Active", count: 750, percentage: 50 },
      { name: "Inactive", count: 750, percentage: 50 },
    ]);
    setUserRetention([
      { period: "1 day", rate: 90 },
      { period: "7 days", rate: 75 },
      { period: "30 days", rate: 50 },
      { period: "90 days", rate: 30 },
    ]);
    setUserActivity({
      dailyActiveUsers: 300,
      monthlyActiveUsers: 1500,
      averageSessionDuration: 120,
      averageActionsPerSession: 5,
    });
  };

  // Fetch data on mount and timeFrame change
  useEffect(() => {
    fetchAnalyticsData();
  }, [timeFrame]);

  // Set header
  useEffect(() => {
    setHeaderContent({
      title: "Analytics",
      description:
        "Detailed analytics and insights about platform usage and revenue",
      content: null,
    });
    return () => setHeaderContent({});
  }, []);

  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return (
          <div className="space-y-6">
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
              {[
                {
                  title: "Total Revenue",
                  value: `$${adminStats?.totalRevenue.toFixed(2) || "0.00"}`,
                  change: "+15.2% from last month",
                },
                {
                  title: "Active Subscriptions",
                  value: adminStats?.activeSubscriptions || 0,
                  change: "+10.5% from last month",
                },
                {
                  title: "Total Jobs",
                  value: adminStats?.totalJobs || 0,
                  change: "+18.7% from last month",
                },
                {
                  title: "Conversion Rate",
                  value: `${adminStats?.conversionRate || 0}%`,
                  change: "+2.1% from last month",
                },
              ].map((stat, index) => (
                <Card
                  key={index}
                  className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow"
                >
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-white">
                      {stat.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-300">
                      {loading ? "Loading..." : stat.value}
                    </div>
                    <p className="text-xs text-gray-300">{stat.change}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="grid gap-4 sm:grid-cols-2">
              <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-white">Revenue Trend</CardTitle>
                  <CardDescription className="text-gray-300">
                    Monthly revenue over the last 6 months
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-end gap-2">
                    {revenueData.monthly?.map((item, index) => (
                      <div
                        key={index}
                        className="relative flex-1 flex flex-col items-center group"
                      >
                        <div
                          className="w-full bg-gradient-to-t from-purple-500 to-purple-300 rounded-t"
                          style={{ height: `${(item.amount / 10000) * 100}%` }}
                        />
                        <span className="text-xs text-gray-300 mt-2">
                          {item.date}
                        </span>
                        <span className="text-xs text-gray-300">
                          ${item.amount}
                        </span>
                        <div className="absolute -top-8 hidden group-hover:block bg-gray-800 text-white text-xs p-1 rounded">
                          ${item.amount}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-white">
                    Usage Distribution
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Distribution of platform usage by feature
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex flex-col justify-center space-y-6">
                    {[
                      {
                        label: "Video Generation",
                        percentage: 45,
                        color: "bg-blue-500",
                      },
                      {
                        label: "Image Generation",
                        percentage: 35,
                        color: "bg-green-500",
                      },
                      {
                        label: "Background Removal",
                        percentage: 20,
                        color: "bg-purple-500",
                      },
                    ].map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center text-gray-300">
                          <span>{item.label}</span>
                          <span className="ml-auto">{item.percentage}%</span>
                        </div>
                        <div className="h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                          <div
                            className={`h-full ${item.color} rounded-full transition-all duration-500`}
                            style={{ width: `${item.percentage}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="grid gap-4 sm:grid-cols-2">
              <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-white">User Growth</CardTitle>
                  <CardDescription className="text-gray-300">
                    New user registrations over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-end gap-2">
                    {userGrowthData.monthly?.map((item, index) => (
                      <div
                        key={index}
                        className="relative flex-1 flex flex-col items-center group"
                      >
                        <div
                          className="w-full bg-gradient-to-t from-green-500 to-green-300 rounded-t"
                          style={{ height: `${(item.amount / 500) * 100}%` }}
                        />
                        <span className="text-xs text-gray-300 mt-2">
                          {item.date}
                        </span>
                        <span className="text-xs text-gray-300">
                          {item.amount} users
                        </span>
                        <div className="absolute -top-8 hidden group-hover:block bg-gray-800 text-white text-xs p-1 rounded">
                          {item.amount} users
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-white">
                    Plan Distribution
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Distribution of users by subscription plan
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex flex-col justify-center space-y-6">
                    {loading ? (
                      <div className="text-center text-gray-300 py-4">
                        Loading plan distribution...
                      </div>
                    ) : planDistribution.length === 0 ? (
                      <div className="text-center text-gray-300 py-4">
                        No plan distribution data available
                      </div>
                    ) : (
                      planDistribution.map((item, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-center text-gray-300">
                            <span>{item.name}</span>
                            <span className="ml-auto">{item.percentage}%</span>
                          </div>
                          <div className="h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-purple-500 rounded-full transition-all duration-500"
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-300">
                            {item.count} users
                          </span>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        );
      case "revenue":
        return (
          <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-white">Revenue Details</CardTitle>
              <CardDescription className="text-gray-300">
                Detailed breakdown of revenue sources and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div>
                  <h3 className="text-lg font-medium text-gray-300 mb-4">
                    Revenue by Plan
                  </h3>
                  <div className="space-y-4">
                    {[
                      {
                        name: "Starter Plan",
                        amount: 12540,
                        percentage: 29.5,
                        color: "bg-blue-500",
                      },
                      {
                        name: "Pro Plan",
                        amount: 18320,
                        percentage: 43.0,
                        color: "bg-purple-500",
                      },
                      {
                        name: "Business Plan",
                        amount: 9870,
                        percentage: 23.2,
                        color: "bg-green-500",
                      },
                      {
                        name: "Credit Purchases",
                        amount: 1850,
                        percentage: 4.3,
                        color: "bg-yellow-500",
                      },
                    ].map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between text-gray-300"
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className={`w-3 h-3 rounded-full ${item.color}`}
                          />
                          <span>{item.name}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="font-medium">${item.amount}</span>
                          <span className="text-sm">{item.percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-300 mb-4">
                    Revenue Trend ({timeFrame})
                  </h3>
                  <div className="h-80 flex items-end gap-2">
                    {revenueData[timeFrame]?.map((item, index) => (
                      <div
                        key={index}
                        className="relative flex-1 flex flex-col items-center group"
                      >
                        <div
                          className="w-full bg-gradient-to-t from-purple-500 to-purple-300 rounded-t"
                          style={{
                            height: `${
                              (item.amount /
                                (timeFrame === "daily"
                                  ? 2500
                                  : timeFrame === "weekly"
                                  ? 12000
                                  : 50000)) *
                              100
                            }%`,
                          }}
                        />
                        <span className="text-xs text-gray-300 mt-2">
                          {item.date}
                        </span>
                        <span className="text-xs text-gray-300">
                          ${item.amount}
                        </span>
                        <div className="absolute -top-8 hidden group-hover:block bg-gray-800 text-white text-xs p-1 rounded">
                          ${item.amount}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case "usage":
        return (
          <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-white">Feature Usage</CardTitle>
              <CardDescription className="text-gray-300">
                Detailed breakdown of platform feature usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {[
                  {
                    title: "Video Generation",
                    data: usageData.videoGeneration,
                    color: "bg-blue-500",
                    max:
                      timeFrame === "daily"
                        ? 400
                        : timeFrame === "weekly"
                        ? 1500
                        : 8000,
                  },
                  {
                    title: "Image Generation",
                    data: usageData.imageGeneration,
                    color: "bg-green-500",
                    max:
                      timeFrame === "daily"
                        ? 400
                        : timeFrame === "weekly"
                        ? 2000
                        : 8000,
                  },
                  {
                    title: "Background Removal",
                    data: usageData.backgroundRemoval,
                    color: "bg-purple-500",
                    max:
                      timeFrame === "daily"
                        ? 200
                        : timeFrame === "weekly"
                        ? 1000
                        : 4000,
                  },
                ].map((item, index) => (
                  <div key={index}>
                    <h3 className="text-lg font-medium text-gray-300 mb-4">
                      {item.title} ({timeFrame})
                    </h3>
                    <div className="h-60 flex items-end gap-2">
                      {item.data?.map((value, i) => (
                        <div
                          key={i}
                          className="relative flex-1 flex flex-col items-center group"
                        >
                          <div
                            className={`w-full ${item.color} rounded-t`}
                            style={{
                              height: `${(value.amount / item.max) * 100}%`,
                            }}
                          />
                          <span className="text-xs text-gray-300 mt-2">
                            {timeFrame === "daily"
                              ? `Day ${i + 1}`
                              : timeFrame === "weekly"
                              ? `Week ${i + 1}`
                              : `Month ${i + 1}`}
                          </span>
                          <span className="text-xs text-gray-300">
                            {value.amount}
                          </span>
                          <div className="absolute -top-8 hidden group-hover:block bg-gray-800 text-white text-xs p-1 rounded">
                            {value.amount}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );
      case "users":
        return (
          <div className="space-y-6">
            <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-white">User Analytics</CardTitle>
                <CardDescription className="text-gray-300">
                  Detailed breakdown of user metrics and behavior
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-medium text-gray-300 mb-4">
                      New User Registrations ({timeFrame})
                    </h3>
                    <div className="h-60 flex items-end gap-2">
                      {userGrowthData[timeFrame]?.map((item, index) => (
                        <div
                          key={index}
                          className="relative flex-1 flex flex-col items-center group"
                        >
                          <div
                            className="w-full bg-gradient-to-t from-green-500 to-green-300 rounded-t"
                            style={{
                              height: `${
                                (item.amount /
                                  (timeFrame === "daily"
                                    ? 40
                                    : timeFrame === "weekly"
                                    ? 150
                                    : 500)) *
                                100
                              }%`,
                            }}
                          />
                          <span className="text-xs text-gray-300 mt-2">
                            {timeFrame === "daily"
                              ? `Day ${index + 1}`
                              : timeFrame === "weekly"
                              ? `Week ${index + 1}`
                              : `Month ${index + 1}`}
                          </span>
                          <span className="text-xs text-gray-300">
                            {item.amount} users
                          </span>
                          <div className="absolute -top-8 hidden group-hover:block bg-gray-800 text-white text-xs p-1 rounded">
                            {item.amount} users
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-300 mb-4">
                      User Retention
                    </h3>
                    <div className="space-y-4">
                      {loading ? (
                        <div className="text-center text-gray-300 py-4">
                          Loading retention data...
                        </div>
                      ) : userRetention.length === 0 ? (
                        <div className="text-center text-gray-300 py-4">
                          No retention data available
                        </div>
                      ) : (
                        userRetention.map((item, index) => (
                          <div key={index} className="space-y-2">
                            <div className="flex items-center text-gray-300">
                              <span>After {item.period}</span>
                              <span className="ml-auto">{item.rate}%</span>
                            </div>
                            <div className="h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className={`h-full rounded-full ${
                                  item.rate > 80
                                    ? "bg-green-500"
                                    : item.rate > 60
                                    ? "bg-yellow-500"
                                    : "bg-orange-500"
                                } transition-all duration-500`}
                                style={{ width: `${item.rate}%` }}
                              />
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-300 mb-4">
                      User Activity
                    </h3>
                    <div className="space-y-4 text-gray-300">
                      {loading ? (
                        <div className="text-center py-4">
                          Loading activity data...
                        </div>
                      ) : !userActivity ? (
                        <div className="text-center py-4">
                          No activity data available
                        </div>
                      ) : (
                        [
                          {
                            label: "Daily Active Users (DAU)",
                            value: userActivity.dailyActiveUsers,
                          },
                          {
                            label: "Monthly Active Users (MAU)",
                            value: userActivity.monthlyActiveUsers,
                          },
                          {
                            label: "Average Session Duration",
                            value: `${userActivity.averageSessionDuration} minutes`,
                          },
                          {
                            label: "Average Actions Per Session",
                            value: userActivity.averageActionsPerSession,
                          },
                        ].map((item, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between"
                          >
                            <span>{item.label}</span>
                            <span className="font-medium">{item.value}</span>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white/5 border-gray-600 rounded-2xl hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-white">User Table</CardTitle>
                <CardDescription className="text-gray-300">
                  List of users with their subscription status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr>
                        {[
                          "Name",
                          "Price",
                          "Interval",
                          "Video Quota",
                          "Image Quota",
                          "BG Removal",
                          "Status",
                          "Actions",
                        ].map((head) => (
                          <th
                            key={head}
                            className="h-12 px-4 text-left align-middle font-semibold text-gray-300 whitespace-nowrap"
                          >
                            {head}
                          </th>
                        ))}
                      </tr>
                      <tr className="relative">
                        <td
                          colSpan={8}
                          className="absolute bottom-0 h-0 pointer-events-none"
                          style={{
                            left: "10%",
                            right: "10%",
                            background:
                              "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
                            height: "2px",
                          }}
                        />
                      </tr>
                    </thead>
                    <tbody>
                      {planDistribution.map((plan, index) => (
                        <tr
                          key={index}
                          className="transition-colors relative text-gray-300"
                        >
                          <td className="p-4 align-middle whitespace-nowrap font-medium">
                            {plan.name}
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            $0.00
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap capitalize">
                            Monthly
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            0
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            0
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            0
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            <span
                              className={`px-3 py-1 rounded-full text-xs font-medium ${
                                plan.name === "Active"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-gray-100 text-gray-600"
                              }`}
                            >
                              {plan.name}
                            </span>
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <Button className="bg-transparent border border-gray-500" variant="outline" size="sm">
                                <Edit />
                              </Button>
                              <Button className="bg-transparent border border-gray-500" variant="destructive" size="sm">
                                <Trash2 />
                              </Button>
                            </div>
                          </td>
                          {index !== planDistribution.length - 1 && (
                            <td
                              className="absolute bottom-0 h-0 pointer-events-none"
                              style={{
                                left: "10%",
                                right: "10%",
                                background:
                                  "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
                                height: "2px",
                              }}
                            />
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      default:
        return (
          <div className="flex-1 flex items-center justify-center text-gray-300">
            <p>No data available for this tab</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen text-white px-4 sm:px-6 lg:px-8 py-6">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex gap-10 space-x-1 pb-1 p-2 pr-28 border-b-2 border-gray-500">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`text-sm font-medium rounded-lg cursor-pointer transition-colors ${
                  activeTab === tab.id
                    ? "text-white"
                    : "text-gray-400 hover:text-purple-500"
                }`}
                type="button"
              >
                {tab.label}
              </button>
            ))}
          </div>
          {/* Time Frame Selector */}
          <div className="flex space-x-1">
            {timeFrames.map((frame) => {
              if (timeFrame === frame.id) {
                return (
                  <ShadowButton
                    key={frame.id}
                    onClick={() => setTimeFrame(frame.id)}
                    className={`px-4 py-2 text-sm font-medium rounded-lg`}
                  >
                    {frame.label}
                  </ShadowButton>
                );
              }
              return (
                <button
                  key={frame.id}
                  onClick={() => setTimeFrame(frame.id)}
                  className={`px-4 py-2 text-sm cursor-pointer font-medium rounded-lg transition-colors border border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white`}
                  type="button"
                >
                  {frame.label}
                </button>
              );
            })}
          </div>
        </div>
        <div className="min-h-[500px]">{renderTabContent()}</div>
      </div>
    </div>
  );
};

export default AdminAnalyticsPage;


// OLD
//
// import { useState, useEffect } from "react";
// import { Button } from "@/components/ui/button";
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardHeader,
//   CardTitle,
// } from "@/components/ui/card";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
// import AdminService from "@/services/admin.service";
// import { toast } from "sonner";
// import SpeedPaintingAnalytics from "@/components/Admin/SpeedPaintingAnalytics";

// interface AdminStats {
//   totalUsers: number;
//   activeUsers: number;
//   totalRevenue: number;
//   monthlyRevenue: number;
//   totalCredits: number;
//   creditsUsed: number;
//   videoGenerations: number;
//   imageGenerations: number;
//   backgroundRemovals: number;
//   activeSubscriptions: number;
//   activeJobs: number;
//   completedJobs: number;
//   blogPosts: number;
//   conversionRate: number;
//   totalJobs?: number;
//   [key: string]: string | number | undefined;
// }

// interface ChartDataPoint {
//   date: string;
//   amount: number;
//   toString(): string;
// }

// interface TimeSeriesData {
//   labels: string[];
//   datasets: { label: string; data: number[] }[];
//   daily?: ChartDataPoint[];
//   weekly?: ChartDataPoint[];
//   monthly?: ChartDataPoint[];
//   videoGeneration?: ChartDataPoint[];
//   imageGeneration?: ChartDataPoint[];
//   backgroundRemoval?: ChartDataPoint[];
// }

// const defaultTimeSeriesData: TimeSeriesData = {
//   labels: [],
//   datasets: [{ label: "", data: [] }],
//   daily: [],
//   weekly: [],
//   monthly: [],
//   videoGeneration: [],
//   imageGeneration: [],
//   backgroundRemoval: [],
// };

// const defaultPlanDistribution: Array<{
//   name: string;
//   count: number;
//   percentage: number;
// }> = [];

// const AdminAnalyticsPage = () => {
//   const [timeRange, setTimeRange] = useState<"daily" | "weekly" | "monthly">(
//     "monthly"
//   );

//   const [loading, setLoading] = useState(true);
//   const [adminStats, setAdminStats] = useState<AdminStats | null>(null);
//   const [revenueData, setRevenueData] = useState<TimeSeriesData>(
//     defaultTimeSeriesData
//   );
//   const [usageData, setUsageData] = useState<TimeSeriesData>(
//     defaultTimeSeriesData
//   );
//   const [userGrowthData, setUserGrowthData] = useState<TimeSeriesData>(
//     defaultTimeSeriesData
//   );
//   const [planDistribution, setPlanDistribution] = useState<
//     Array<{ name: string; count: number; percentage: number }>
//   >(defaultPlanDistribution);
//   const [userRetention, setUserRetention] = useState<
//     Array<{ period: string; rate: number }>
//   >([]);
//   const [userActivity, setUserActivity] = useState<{
//     dailyActiveUsers: number;
//     monthlyActiveUsers: number;
//     averageSessionDuration: number;
//     averageActionsPerSession: number;
//   } | null>(null);

//   const fetchAnalyticsData = async () => {
//     try {
//       setLoading(true);

//       // Fetch admin stats
//       const stats = await AdminService.getAdminStats();
//       // Add default values for any missing properties
//       const completeStats = {
//         ...stats,
//         totalJobs:
//           stats.totalJobs ||
//           (stats.activeJobs || 0) + (stats.completedJobs || 0),
//       };
//       setAdminStats(completeStats as unknown as AdminStats);

//       // Generate mock time series data based on admin stats
//       // This is a temporary solution until the backend endpoints are implemented
//       const generateMockTimeSeriesData = (
//         baseValue: number,
//         label: string
//       ): TimeSeriesData => {
//         const labels = [];
//         const data = [];
//         const mockData: ChartDataPoint[] = [];

//         // Generate data based on selected time range
//         const dataPoints =
//           timeRange === "daily" ? 30 : timeRange === "weekly" ? 12 : 6;
//         const today = new Date();

//         for (let i = 0; i < dataPoints; i++) {
//           const date = new Date();
//           if (timeRange === "daily") {
//             date.setDate(today.getDate() - i);
//           } else if (timeRange === "weekly") {
//             date.setDate(today.getDate() - i * 7);
//           } else {
//             date.setMonth(today.getMonth() - i);
//           }

//           const dateStr = date.toISOString().split("T")[0];
//           labels.unshift(dateStr);

//           // Create some variation in the data
//           const variation = Math.random() * 0.4 + 0.8; // 0.8 to 1.2
//           const amount = Math.round(baseValue * variation);
//           data.unshift(amount);

//           mockData.unshift({
//             date: dateStr,
//             amount: amount,
//           });
//         }

//         return {
//           labels,
//           datasets: [{ label, data }],
//           [timeRange]: mockData,
//         };
//       };

//       // Generate mock revenue data
//       const monthlyRevenue = stats.monthlyRevenue || 5000;
//       const revenueTimeSeries = generateMockTimeSeriesData(
//         monthlyRevenue / 30,
//         "Revenue"
//       );
//       setRevenueData(revenueTimeSeries);

//       // Generate mock usage data
//       const videoGenerations = stats.videoGenerations || 500;
//       const imageGenerations = stats.imageGenerations || 1200;
//       const backgroundRemovals = stats.backgroundRemovals || 800;
//       const totalUsage =
//         videoGenerations + imageGenerations + backgroundRemovals;

//       const usageTimeSeries = {
//         ...generateMockTimeSeriesData(totalUsage / 30, "Usage"),
//         videoGeneration: generateMockTimeSeriesData(
//           videoGenerations / 30,
//           "Video"
//         )[timeRange],
//         imageGeneration: generateMockTimeSeriesData(
//           imageGenerations / 30,
//           "Image"
//         )[timeRange],
//         backgroundRemoval: generateMockTimeSeriesData(
//           backgroundRemovals / 30,
//           "Background"
//         )[timeRange],
//       };
//       setUsageData(usageTimeSeries);

//       // Generate mock user growth data
//       const totalUsers = stats.totalUsers || 2500;
//       const userGrowthTimeSeries = generateMockTimeSeriesData(
//         totalUsers / 60,
//         "Users"
//       );
//       setUserGrowthData(userGrowthTimeSeries);

//       // Calculate total users from admin stats and create plan distribution
//       // Create realistic plan distribution based on total users
//       setPlanDistribution([
//         { name: "Free", count: Math.round(totalUsers * 0.3), percentage: 30 },
//         {
//           name: "Starter",
//           count: Math.round(totalUsers * 0.36),
//           percentage: 36,
//         },
//         { name: "Pro", count: Math.round(totalUsers * 0.24), percentage: 24 },
//         {
//           name: "Business",
//           count: Math.round(totalUsers * 0.1),
//           percentage: 10,
//         },
//       ]);

//       // Set user retention with realistic data
//       setUserRetention([
//         { period: "1 day", rate: 95 },
//         { period: "7 days", rate: 82 },
//         { period: "30 days", rate: 68 },
//         { period: "90 days", rate: 42 },
//       ]);

//       // Set user activity metrics based on admin stats
//       const activeUsers = stats.activeUsers || Math.round(totalUsers * 0.15);
//       setUserActivity({
//         dailyActiveUsers: activeUsers,
//         monthlyActiveUsers: totalUsers,
//         averageSessionDuration: 18,
//         averageActionsPerSession: 5.3,
//       });
//     } catch (error) {
//       console.error("Error fetching analytics data:", error);
//       toast.error("Failed to fetch analytics data");

//       // Set fallback data in case of error
//       setDefaultMockData();
//     } finally {
//       setLoading(false);
//     }
//   };

//   // Helper function to set default mock data if API fails
//   const setDefaultMockData = () => {
//     const generateDefaultTimeSeries = (
//       baseValue: number,
//       label: string
//     ): TimeSeriesData => {
//       const labels = [];
//       const data = [];
//       const mockData: ChartDataPoint[] = [];

//       // Generate 6 months of data
//       const today = new Date();
//       for (let i = 0; i < 6; i++) {
//         const date = new Date();
//         date.setMonth(today.getMonth() - i);
//         const dateStr = date.toISOString().split("T")[0];
//         labels.unshift(dateStr);

//         // Create some variation
//         const amount = Math.round(
//           baseValue * (1 + (Math.random() * 0.4 - 0.2))
//         );
//         data.unshift(amount);
//         mockData.unshift({ date: dateStr, amount });
//       }

//       return {
//         labels,
//         datasets: [{ label, data }],
//         monthly: mockData,
//       };
//     };

//     // Set default admin stats
//     setAdminStats({
//       totalUsers: 1200,
//       activeUsers: 375,
//       totalRevenue: 75000,
//       monthlyRevenue: 12500,
//       totalCredits: 10000,
//       creditsUsed: 5000,
//       videoGenerations: 500,
//       imageGenerations: 1200,
//       backgroundRemovals: 800,
//       activeSubscriptions: 900,
//       activeJobs: 150,
//       completedJobs: 2500,
//       totalJobs: 2650, // activeJobs + completedJobs
//       blogPosts: 24,
//       conversionRate: 25,
//     });

//     // Set default revenue data
//     setRevenueData(generateDefaultTimeSeries(400, "Revenue"));

//     // Set default usage data
//     setUsageData({
//       ...generateDefaultTimeSeries(80, "Usage"),
//       videoGeneration: generateDefaultTimeSeries(20, "Video").monthly,
//       imageGeneration: generateDefaultTimeSeries(40, "Image").monthly,
//       backgroundRemoval: generateDefaultTimeSeries(30, "Background").monthly,
//     });

//     // Set default user growth data
//     setUserGrowthData(generateDefaultTimeSeries(40, "Users"));

//     // Set default plan distribution
//     setPlanDistribution([
//       { name: "Free", count: 750, percentage: 30 },
//       { name: "Starter", count: 900, percentage: 36 },
//       { name: "Pro", count: 600, percentage: 24 },
//       { name: "Business", count: 250, percentage: 10 },
//     ]);

//     // Set default user retention
//     setUserRetention([
//       { period: "1 day", rate: 95 },
//       { period: "7 days", rate: 82 },
//       { period: "30 days", rate: 68 },
//       { period: "90 days", rate: 42 },
//     ]);

//     // Set default user activity
//     setUserActivity({
//       dailyActiveUsers: 375,
//       monthlyActiveUsers: 2500,
//       averageSessionDuration: 18,
//       averageActionsPerSession: 5.3,
//     });
//   };

//   // Fetch data on component mount and when timeRange changes
//   useEffect(() => {
//     fetchAnalyticsData();
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [timeRange]);

//   return (
//     <div className="space-y-8">
//       <div>
//         <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
//         <p className="text-muted-foreground">
//           Detailed analytics and insights about platform usage and revenue
//         </p>
//       </div>

//       <div className="flex justify-between items-center">
//         <Tabs defaultValue="overview" className="w-full">
//           <TabsList>
//             <TabsTrigger value="overview">Overview</TabsTrigger>
//             <TabsTrigger value="revenue">Revenue</TabsTrigger>
//             <TabsTrigger value="usage">Usage</TabsTrigger>
//             <TabsTrigger value="users">Users</TabsTrigger>
//             <TabsTrigger value="speedpainting">Speed Painting</TabsTrigger>
//           </TabsList>
//         </Tabs>

//         <div className="flex gap-2">
//           <Button
//             variant={timeRange === "daily" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setTimeRange("daily")}
//           >
//             Daily
//           </Button>
//           <Button
//             variant={timeRange === "weekly" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setTimeRange("weekly")}
//           >
//             Weekly
//           </Button>
//           <Button
//             variant={timeRange === "monthly" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setTimeRange("monthly")}
//           >
//             Monthly
//           </Button>
//         </div>
//       </div>

//       <Tabs defaultValue="overview">
//         <TabsContent value="overview" className="space-y-6">
//           <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
//             <Card>
//               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                 <CardTitle className="text-sm font-medium">
//                   Total Revenue
//                 </CardTitle>
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                   stroke="currentColor"
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth="2"
//                   className="h-4 w-4 text-muted-foreground"
//                 >
//                   <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
//                 </svg>
//               </CardHeader>
//               <CardContent>
//                 <div className="text-2xl font-bold">
//                   {loading
//                     ? "Loading..."
//                     : `$${
//                         typeof adminStats?.totalRevenue === "number"
//                           ? adminStats.totalRevenue.toFixed(2)
//                           : "0.00"
//                       }`}
//                 </div>
//                 <p className="text-xs text-muted-foreground">
//                   +15.2% from last month
//                 </p>
//               </CardContent>
//             </Card>
//             <Card>
//               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                 <CardTitle className="text-sm font-medium">
//                   Active Subscriptions
//                 </CardTitle>
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                   stroke="currentColor"
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth="2"
//                   className="h-4 w-4 text-muted-foreground"
//                 >
//                   <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
//                   <circle cx="9" cy="7" r="4" />
//                   <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
//                   <path d="M16 3.13a4 4 0 0 1 0 7.75" />
//                 </svg>
//               </CardHeader>
//               <CardContent>
//                 <div className="text-2xl font-bold">
//                   {loading
//                     ? "Loading..."
//                     : adminStats?.activeSubscriptions || 0}
//                 </div>
//                 <p className="text-xs text-muted-foreground">
//                   +10.5% from last month
//                 </p>
//               </CardContent>
//             </Card>
//             <Card>
//               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                 <CardTitle className="text-sm font-medium">
//                   Total Jobs
//                 </CardTitle>
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                   stroke="currentColor"
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth="2"
//                   className="h-4 w-4 text-muted-foreground"
//                 >
//                   <rect width="20" height="14" x="2" y="5" rx="2" />
//                   <path d="M2 10h20" />
//                 </svg>
//               </CardHeader>
//               <CardContent>
//                 <div className="text-2xl font-bold">
//                   {loading ? "Loading..." : adminStats?.totalJobs || 0}
//                 </div>
//                 <p className="text-xs text-muted-foreground">
//                   +18.7% from last month
//                 </p>
//               </CardContent>
//             </Card>
//             <Card>
//               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                 <CardTitle className="text-sm font-medium">
//                   Conversion Rate
//                 </CardTitle>
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                   stroke="currentColor"
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth="2"
//                   className="h-4 w-4 text-muted-foreground"
//                 >
//                   <path d="m16 18 2-2-2-2" />
//                   <path d="M18 16H5" />
//                   <path d="m8 6-2 2 2 2" />
//                   <path d="M6 8h13" />
//                 </svg>
//               </CardHeader>
//               <CardContent>
//                 <div className="text-2xl font-bold">
//                   {loading
//                     ? "Loading..."
//                     : `${adminStats?.conversionRate || 0}%`}
//                 </div>
//                 <p className="text-xs text-muted-foreground">
//                   +2.1% from last month
//                 </p>
//               </CardContent>
//             </Card>
//           </div>

//           <div className="grid gap-4 md:grid-cols-2">
//             <Card>
//               <CardHeader>
//                 <CardTitle>Revenue Trend</CardTitle>
//                 <CardDescription>
//                   Monthly revenue over the last 5 months
//                 </CardDescription>
//               </CardHeader>
//               <CardContent>
//                 <div className="h-80 flex items-end gap-2">
//                   {revenueData.monthly &&
//                     Array.isArray(revenueData.monthly) &&
//                     revenueData.monthly.map((item, index) => (
//                       <div
//                         key={index}
//                         className="relative flex-1 flex flex-col items-center"
//                       >
//                         <div
//                           className="w-full bg-primary rounded-t"
//                           style={{
//                             height: `${(item.amount / 50000) * 100}%`,
//                           }}
//                         ></div>
//                         <span className="text-xs mt-2">{item.date}</span>
//                         <span className="text-xs text-muted-foreground">
//                           ${item.amount}
//                         </span>
//                       </div>
//                     ))}
//                 </div>
//               </CardContent>
//             </Card>

//             <Card>
//               <CardHeader>
//                 <CardTitle>Usage Distribution</CardTitle>
//                 <CardDescription>
//                   Distribution of platform usage by feature
//                 </CardDescription>
//               </CardHeader>
//               <CardContent>
//                 <div className="h-80">
//                   <div className="h-full flex flex-col justify-center space-y-8">
//                     <div className="space-y-2">
//                       <div className="flex items-center">
//                         <span className="font-medium">Video Generation</span>
//                         <span className="ml-auto">45%</span>
//                       </div>
//                       <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
//                         <div
//                           className="h-full bg-blue-500 rounded-full"
//                           style={{ width: "45%" }}
//                         ></div>
//                       </div>
//                     </div>
//                     <div className="space-y-2">
//                       <div className="flex items-center">
//                         <span className="font-medium">Image Generation</span>
//                         <span className="ml-auto">35%</span>
//                       </div>
//                       <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
//                         <div
//                           className="h-full bg-green-500 rounded-full"
//                           style={{ width: "35%" }}
//                         ></div>
//                       </div>
//                     </div>
//                     <div className="space-y-2">
//                       <div className="flex items-center">
//                         <span className="font-medium">Background Removal</span>
//                         <span className="ml-auto">20%</span>
//                       </div>
//                       <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
//                         <div
//                           className="h-full bg-purple-500 rounded-full"
//                           style={{ width: "20%" }}
//                         ></div>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </CardContent>
//             </Card>
//           </div>

//           <div className="grid gap-4 md:grid-cols-2">
//             <Card>
//               <CardHeader>
//                 <CardTitle>User Growth</CardTitle>
//                 <CardDescription>
//                   New user registrations over time
//                 </CardDescription>
//               </CardHeader>
//               <CardContent>
//                 <div className="h-80 flex items-end gap-2">
//                   {userGrowthData.monthly &&
//                     Array.isArray(userGrowthData.monthly) &&
//                     userGrowthData.monthly.map(
//                       (dataPoint: ChartDataPoint, index) => (
//                         <div
//                           key={index}
//                           className="relative flex-1 flex flex-col items-center"
//                         >
//                           <div
//                             className="w-full bg-green-500 rounded-t"
//                             style={{
//                               height: `${
//                                 (Number(dataPoint.amount) / 500) * 100
//                               }%`,
//                             }}
//                           ></div>
//                           <span className="text-xs mt-2">
//                             Month {index + 1}
//                           </span>
//                           <span className="text-xs text-muted-foreground">
//                             {typeof dataPoint === "object" &&
//                             "amount" in dataPoint
//                               ? dataPoint.amount.toString()
//                               : String(dataPoint)}{" "}
//                             users
//                           </span>
//                         </div>
//                       )
//                     )}
//                 </div>
//               </CardContent>
//             </Card>

//             <Card>
//               <CardHeader>
//                 <CardTitle>Plan Distribution</CardTitle>
//                 <CardDescription>
//                   Distribution of users by subscription plan
//                 </CardDescription>
//               </CardHeader>
//               <CardContent>
//                 <div className="h-80">
//                   <div className="h-full flex flex-col justify-center space-y-8">
//                     {loading ? (
//                       <div className="text-center py-4">
//                         Loading plan distribution...
//                       </div>
//                     ) : planDistribution.length === 0 ? (
//                       <div className="text-center py-4">
//                         No plan distribution data available
//                       </div>
//                     ) : (
//                       planDistribution.map((item) => (
//                         <div key={item.name} className="space-y-2">
//                           <div className="flex items-center">
//                             <span className="font-medium">{item.name}</span>
//                             <span className="ml-auto">{item.percentage}%</span>
//                           </div>
//                           <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
//                             <div
//                               className="h-full bg-primary rounded-full"
//                               style={{ width: `${item.percentage}%` }}
//                             ></div>
//                           </div>
//                           <span className="text-xs text-muted-foreground">
//                             {item.count} users
//                           </span>
//                         </div>
//                       ))
//                     )}
//                     {planDistribution.length > 0 && (
//                       <div className="text-xs text-muted-foreground">
//                         {planDistribution.reduce(
//                           (total, plan) => total + plan.count,
//                           0
//                         )}{" "}
//                         total users
//                       </div>
//                     )}
//                   </div>
//                 </div>
//               </CardContent>
//             </Card>
//           </div>
//         </TabsContent>

//         <TabsContent value="revenue" className="space-y-6">
//           <Card>
//             <CardHeader>
//               <CardTitle>Revenue Details</CardTitle>
//               <CardDescription>
//                 Detailed breakdown of revenue sources and trends
//               </CardDescription>
//             </CardHeader>
//             <CardContent>
//               <div className="space-y-8">
//                 <div>
//                   <h3 className="text-lg font-medium mb-4">Revenue by Plan</h3>
//                   <div className="space-y-4">
//                     <div className="flex items-center justify-between">
//                       <div className="flex items-center gap-2">
//                         <div className="w-3 h-3 rounded-full bg-blue-500"></div>
//                         <span>Starter Plan</span>
//                       </div>
//                       <span className="font-medium">$12,540</span>
//                       <span className="text-muted-foreground text-sm">
//                         29.5%
//                       </span>
//                     </div>
//                     <div className="flex items-center justify-between">
//                       <div className="flex items-center gap-2">
//                         <div className="w-3 h-3 rounded-full bg-purple-500"></div>
//                         <span>Pro Plan</span>
//                       </div>
//                       <span className="font-medium">$18,320</span>
//                       <span className="text-muted-foreground text-sm">
//                         43.0%
//                       </span>
//                     </div>
//                     <div className="flex items-center justify-between">
//                       <div className="flex items-center gap-2">
//                         <div className="w-3 h-3 rounded-full bg-green-500"></div>
//                         <span>Business Plan</span>
//                       </div>
//                       <span className="font-medium">$9,870</span>
//                       <span className="text-muted-foreground text-sm">
//                         23.2%
//                       </span>
//                     </div>
//                     <div className="flex items-center justify-between">
//                       <div className="flex items-center gap-2">
//                         <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
//                         <span>Credit Purchases</span>
//                       </div>
//                       <span className="font-medium">$1,850</span>
//                       <span className="text-muted-foreground text-sm">
//                         4.3%
//                       </span>
//                     </div>
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">
//                     Revenue Trend ({timeRange})
//                   </h3>
//                   <div className="h-80 flex items-end gap-2">
//                     {revenueData[timeRange as keyof TimeSeriesData] &&
//                       Array.isArray(
//                         revenueData[timeRange as keyof TimeSeriesData]
//                       ) &&
//                       (
//                         revenueData[
//                           timeRange as keyof TimeSeriesData
//                         ] as ChartDataPoint[]
//                       ).map((value: ChartDataPoint, index) => (
//                         <div
//                           key={index}
//                           className="relative flex-1 flex flex-col items-center"
//                         >
//                           <div
//                             className="w-full bg-primary rounded-t"
//                             style={{
//                               height: `${
//                                 (Number(value.amount) /
//                                   (timeRange === "daily"
//                                     ? 2500
//                                     : timeRange === "weekly"
//                                     ? 12000
//                                     : 50000)) *
//                                 100
//                               }%`,
//                             }}
//                           ></div>
//                           <span className="text-xs mt-2">{value.date}</span>
//                           <span className="text-xs text-muted-foreground">
//                             ${value.amount || 0}
//                           </span>
//                         </div>
//                       ))}
//                   </div>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         </TabsContent>

//         <TabsContent value="usage" className="space-y-6">
//           <Card>
//             <CardHeader>
//               <CardTitle>Feature Usage</CardTitle>
//               <CardDescription>
//                 Detailed breakdown of platform feature usage
//               </CardDescription>
//             </CardHeader>
//             <CardContent>
//               <div className="space-y-8">
//                 <div>
//                   <h3 className="text-lg font-medium mb-4">
//                     Video Generation ({timeRange})
//                   </h3>
//                   <div className="h-60 flex items-end gap-2">
//                     {usageData.videoGeneration?.map(
//                       (value: ChartDataPoint, index: number) => (
//                         <div
//                           key={index}
//                           className="relative flex-1 flex flex-col items-center"
//                         >
//                           <div
//                             className="w-full bg-blue-500 rounded-t"
//                             style={{
//                               height: `${
//                                 (value.amount /
//                                   (timeRange === "daily"
//                                     ? 400
//                                     : timeRange === "weekly"
//                                     ? 1500
//                                     : 8000)) *
//                                 100
//                               }%`,
//                             }}
//                           ></div>
//                           <span className="text-xs mt-2">
//                             {timeRange === "daily"
//                               ? `Day ${index + 1}`
//                               : timeRange === "weekly"
//                               ? `Week ${index + 1}`
//                               : `Month ${index + 1}`}
//                           </span>
//                           <span className="text-xs text-muted-foreground">
//                             {value.amount}
//                           </span>
//                         </div>
//                       )
//                     )}
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">
//                     Image Generation ({timeRange})
//                   </h3>
//                   <div className="h-60 flex items-end gap-2">
//                     {usageData.imageGeneration?.map((value, index) => (
//                       <div
//                         key={index}
//                         className="relative flex-1 flex flex-col items-center"
//                       >
//                         <div
//                           className="w-full bg-green-500 rounded-t"
//                           style={{
//                             height: `${
//                               (value.amount /
//                                 (timeRange === "daily"
//                                   ? 400
//                                   : timeRange === "weekly"
//                                   ? 2000
//                                   : 8000)) *
//                               100
//                             }%`,
//                           }}
//                         ></div>
//                         <span className="text-xs mt-2">
//                           {timeRange === "daily"
//                             ? `Day ${index + 1}`
//                             : timeRange === "weekly"
//                             ? `Week ${index + 1}`
//                             : `Month ${index + 1}`}
//                         </span>
//                         <span className="text-xs text-muted-foreground">
//                           {value.amount}
//                         </span>
//                       </div>
//                     ))}
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">
//                     Background Removal ({timeRange})
//                   </h3>
//                   <div className="h-60 flex items-end gap-2">
//                     {usageData.backgroundRemoval?.map((value, index) => (
//                       <div
//                         key={index}
//                         className="relative flex-1 flex flex-col items-center"
//                       >
//                         <div
//                           className="w-full bg-purple-500 rounded-t"
//                           style={{
//                             height: `${
//                               (value.amount /
//                                 (timeRange === "daily"
//                                   ? 200
//                                   : timeRange === "weekly"
//                                   ? 1000
//                                   : 4000)) *
//                               100
//                             }%`,
//                           }}
//                         ></div>
//                         <span className="text-xs mt-2">
//                           {timeRange === "daily"
//                             ? `Day ${index + 1}`
//                             : timeRange === "weekly"
//                             ? `Week ${index + 1}`
//                             : `Month ${index + 1}`}
//                         </span>
//                         <span className="text-xs text-muted-foreground">
//                           {value.amount}
//                         </span>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         </TabsContent>

//         <TabsContent value="users" className="space-y-6">
//           <Card>
//             <CardHeader>
//               <CardTitle>User Analytics</CardTitle>
//               <CardDescription>
//                 Detailed breakdown of user metrics and behavior
//               </CardDescription>
//             </CardHeader>
//             <CardContent>
//               <div className="space-y-8">
//                 <div>
//                   <h3 className="text-lg font-medium mb-4">
//                     New User Registrations ({timeRange})
//                   </h3>
//                   <div className="h-60 flex items-end gap-2">
//                     {userGrowthData[timeRange as keyof TimeSeriesData] &&
//                       Array.isArray(
//                         userGrowthData[timeRange as keyof TimeSeriesData]
//                       ) &&
//                       (
//                         userGrowthData[
//                           timeRange as keyof TimeSeriesData
//                         ] as ChartDataPoint[]
//                       ).map((value: ChartDataPoint, index) => (
//                         <div
//                           key={index}
//                           className="relative flex-1 flex flex-col items-center"
//                         >
//                           <div
//                             className="w-full bg-green-500 rounded-t"
//                             style={{
//                               height: `${
//                                 (Number(value.amount) /
//                                   (timeRange === "daily"
//                                     ? 40
//                                     : timeRange === "weekly"
//                                     ? 150
//                                     : 500)) *
//                                 100
//                               }%`,
//                             }}
//                           ></div>
//                           <span className="text-xs mt-2">
//                             {timeRange === "daily"
//                               ? `Day ${index + 1}`
//                               : timeRange === "weekly"
//                               ? `Week ${index + 1}`
//                               : `Month ${index + 1}`}
//                           </span>
//                           <span className="text-xs text-muted-foreground">
//                             {typeof value === "object" && "amount" in value
//                               ? value.amount.toString()
//                               : String(value)}{" "}
//                             users
//                           </span>
//                         </div>
//                       ))}
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">User Retention</h3>
//                   <div className="space-y-4">
//                     {loading ? (
//                       <div className="text-center py-4">
//                         Loading retention data...
//                       </div>
//                     ) : userRetention.length === 0 ? (
//                       <div className="text-center py-4">
//                         No retention data available
//                       </div>
//                     ) : (
//                       userRetention.map((item) => (
//                         <div key={item.period} className="space-y-2">
//                           <div className="flex items-center">
//                             <span className="font-medium">
//                               After {item.period}
//                             </span>
//                             <span className="ml-auto">{item.rate}%</span>
//                           </div>
//                           <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
//                             <div
//                               className={`h-full rounded-full ${
//                                 item.rate > 80
//                                   ? "bg-green-500"
//                                   : item.rate > 60
//                                   ? "bg-yellow-500"
//                                   : "bg-orange-500"
//                               }`}
//                               style={{ width: `${item.rate}%` }}
//                             ></div>
//                           </div>
//                         </div>
//                       ))
//                     )}
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">User Activity</h3>
//                   <div className="space-y-4">
//                     {loading ? (
//                       <div className="text-center py-4">
//                         Loading activity data...
//                       </div>
//                     ) : !userActivity ? (
//                       <div className="text-center py-4">
//                         No activity data available
//                       </div>
//                     ) : (
//                       <>
//                         <div className="flex items-center justify-between">
//                           <span>Daily Active Users (DAU)</span>
//                           <span className="font-medium">
//                             {userActivity.dailyActiveUsers}
//                           </span>
//                         </div>
//                         <div className="flex items-center justify-between">
//                           <span>Monthly Active Users (MAU)</span>
//                           <span className="font-medium">
//                             {userActivity.monthlyActiveUsers}
//                           </span>
//                         </div>
//                         <div className="flex items-center justify-between">
//                           <span>Average Session Duration</span>
//                           <span className="font-medium">
//                             {userActivity.averageSessionDuration} minutes
//                           </span>
//                         </div>
//                         <div className="flex items-center justify-between">
//                           <span>Average Actions Per Session</span>
//                           <span className="font-medium">
//                             {userActivity.averageActionsPerSession}
//                           </span>
//                         </div>
//                       </>
//                     )}
//                   </div>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         </TabsContent>

//         <TabsContent value="speedpainting" className="space-y-6">
//           <SpeedPaintingAnalytics />
//         </TabsContent>
//       </Tabs>
//     </div>
//   );
// };

// export default AdminAnalyticsPage;
