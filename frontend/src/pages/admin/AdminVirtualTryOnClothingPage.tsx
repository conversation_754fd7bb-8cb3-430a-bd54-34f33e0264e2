import { useEffect, useMemo, useState } from "react";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import { Upload, Trash2, Plus } from "lucide-react";
import AdminService from "@/services/admin.service";

interface ClothingItem {
  id: string;
  imagePath: string;
  name: string;
  clothingType: string;
  category: string;
  style: string;
  color: string;
  season: string;
  description: string;
  isDefault: boolean;
  usageCount: number;
  createdAt: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const AdminVirtualTryOnClothingPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [filter, setFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [showUploadForm, setShowUploadForm] = useState<boolean>(false);

  // Upload form state
  const [uploadData, setUploadData] = useState({
    clothingImage: null as File | null,
    name: "",
    category: "TOP",
    style: "CASUAL",
    color: "",
    season: "",
    description: "",
  });

  const headerContent = useMemo(
    () => (
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          <Input
            placeholder="Search clothing..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Items</SelectItem>
              <SelectItem value="true">Admin Items</SelectItem>
              {/* <SelectItem value="false">User Items</SelectItem> */}
            </SelectContent>
          </Select>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="TOP">Tops</SelectItem>
              <SelectItem value="BOTTOM">Bottoms</SelectItem>
              <SelectItem value="FULL_SET">Full Set</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          onClick={() => setShowUploadForm(!showUploadForm)}
          className="bg-purple-600 hover:bg-purple-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Upload Clothing
        </Button>
      </div>
    ),
    [searchTerm, filter, typeFilter, showUploadForm]
  );

  useEffect(() => {
    setHeaderContent({
      title: "Virtual Try-On Clothing",
      description: "Manage clothing items for virtual try-on feature",
      content: headerContent,
    });

    return () => setHeaderContent({});
  }, [headerContent, setHeaderContent]);

  const fetchClothingItems = async () => {
    try {
      setIsLoading(true);
      const response = await AdminService.getVirtualTryOnClothing({
        page: pagination.page,
        limit: pagination.limit,
        isDefault: filter !== "all" ? filter : undefined,
        clothingType: typeFilter !== "all" ? typeFilter : undefined,
        search: searchTerm || undefined,
      });

      if (response.success) {
        setClothingItems(response?.data.clothingItems);
        setPagination(response?.data.pagination);
      } else {
        toast.error("Failed to fetch clothing items");
      }
    } catch (error) {
      console.error("Error fetching clothing items:", error);
      toast.error("Failed to fetch clothing items");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchClothingItems();
  }, [pagination.page, filter, typeFilter, searchTerm]);

  const handleUpload = async () => {
    if (!uploadData.clothingImage) {
      toast.error("Please select an image file");
      return;
    }

    try {
      setIsUploading(true);
      const response = await AdminService.uploadVirtualTryOnClothing(
        uploadData
      );

      if (response.success) {
        toast.success("Clothing item uploaded successfully");
        setShowUploadForm(false);
        setUploadData({
          clothingImage: null,
          name: "",
          category: "TOP",
          style: "CASUAL",
          color: "",
          season: "",
          description: "",
        });
        fetchClothingItems();
      } else {
        toast.error(response.message || "Failed to upload clothing item");
      }
    } catch (error) {
      console.error("Error uploading clothing item:", error);
      toast.error("Failed to upload clothing item");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async (clothingId: string) => {
    if (!confirm("Are you sure you want to delete this clothing item?")) return;

    try {
      const response = await AdminService.deleteVirtualTryOnClothing(
        clothingId
      );
      if (response.success) {
        toast.success("Clothing item deleted successfully");
        fetchClothingItems();
      } else {
        toast.error("Failed to delete clothing item");
      }
    } catch (error) {
      console.error("Error deleting clothing item:", error);
      toast.error("Failed to delete clothing item");
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "TOP":
        return "bg-blue-600";
      case "BOTTOM":
        return "bg-green-600";
      case "FULL_SET":
        return "bg-purple-600";
      default:
        return "bg-gray-600";
    }
  };

  return (
    <div className="space-y-6 px-4 sm:px-6 lg:px-8">
      {/* Upload Form */}
      {showUploadForm && (
        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader>
            <CardTitle>Upload New Clothing Item</CardTitle>
            <CardDescription>
              Add a new clothing item for virtual try-on
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clothingImage">Clothing Image</Label>
                <Input
                  className="bg-white/5"
                  id="clothingImage"
                  type="file"
                  accept="image/*"
                  onChange={(e) =>
                    setUploadData({
                      ...uploadData,
                      clothingImage: e.target.files?.[0] || null,
                    })
                  }
                />
              </div>
              <div>
                <Label htmlFor="name">Item Name</Label>
                <Input
                  id="name"
                  value={uploadData.name}
                  className="bg-white/5"
                  onChange={(e) =>
                    setUploadData({
                      ...uploadData,
                      name: e.target.value,
                    })
                  }
                  placeholder="Enter item name"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={uploadData.category}
                  onValueChange={(value) =>
                    setUploadData({
                      ...uploadData,
                      category: value,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="TOP">Top</SelectItem>
                    <SelectItem value="BOTTOM">Bottom</SelectItem>
                    <SelectItem value="FULL_SET">Dress/Full Set</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="style">Style</Label>
                <Select
                  value={uploadData.style}
                  onValueChange={(value) =>
                    setUploadData({
                      ...uploadData,
                      style: value,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CASUAL">Casual</SelectItem>
                    <SelectItem value="FORMAL">Formal</SelectItem>
                    <SelectItem value="SPORT">Sport</SelectItem>
                    <SelectItem value="PARTY">Party</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="color">Color</Label>
                <Input
                  id="color"
                  value={uploadData.color}
                  className="bg-white/5"
                  onChange={(e) =>
                    setUploadData({
                      ...uploadData,
                      color: e.target.value,
                    })
                  }
                  placeholder="e.g., Red, Blue, Black"
                />
              </div>
              <div>
                <Label htmlFor="season">Season</Label>
                <Input
                  id="season"
                  value={uploadData.season}
                  className="bg-white/5"
                  onChange={(e) =>
                    setUploadData({
                      ...uploadData,
                      season: e.target.value,
                    })
                  }
                  placeholder="e.g., Summer, Winter, All"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={uploadData.description}
                onChange={(e) =>
                  setUploadData({
                    ...uploadData,
                    description: e.target.value,
                  })
                }
                placeholder="Enter item description"
                rows={3}
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleUpload}
                disabled={isUploading}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isUploading ? (
                  <Spinner className="w-4 h-4 mr-2" />
                ) : (
                  <Upload className="w-4 h-4 mr-2" />
                )}
                Upload Item
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowUploadForm(false)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Clothing Items Grid */}
      <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
        <CardHeader>
          <CardTitle>Clothing Items ({pagination.total})</CardTitle>
          <CardDescription>
            Manage clothing items for the virtual try-on feature
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Spinner className="w-8 h-8" />
            </div>
          ) : clothingItems.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              No clothing items found
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {clothingItems.map((item) => (
                <div
                  key={item.id}
                  className="bg-white/10 rounded-lg overflow-hidden border border-gray-600"
                >
                  <div className="aspect-square relative">
                    <img
                      src={item.imagePath}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 left-2 flex gap-1">
                      {item.isDefault && (
                        <div className="bg-purple-600 text-white text-xs px-2 py-1 rounded">
                          Admin
                        </div>
                      )}
                      <div
                        className={`${getTypeColor(
                          item.clothingType
                        )} text-white text-xs px-2 py-1 rounded`}
                      >
                        {item.clothingType}
                      </div>
                    </div>
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium truncate">{item.name}</h3>
                    <p className="text-sm text-gray-400">{item.style}</p>
                    {item.color && (
                      <p className="text-xs text-gray-500">{item.color}</p>
                    )}
                    <p className="text-xs text-gray-500">
                      Used {item.usageCount} times
                    </p>
                    <div className="flex gap-1 mt-2">
                      {/* <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Edit className="w-3 h-3" />
                      </Button> */}
                      <Button
                        size="sm"
                        variant="destructive"
                        className="flex-1"
                        onClick={() => handleDelete(item.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                disabled={pagination.page === 1}
                onClick={() =>
                  setPagination({ ...pagination, page: pagination.page - 1 })
                }
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                variant="outline"
                disabled={pagination.page === pagination.totalPages}
                onClick={() =>
                  setPagination({ ...pagination, page: pagination.page + 1 })
                }
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminVirtualTryOnClothingPage;
