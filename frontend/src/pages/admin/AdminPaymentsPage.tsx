import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import { AlertCircle, ExternalLink } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import ApiService from "@/services/api.service";
import { Button } from "@/components/ui/button";
import type { ApiResponse } from "@/types/api.types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";

// Types
interface Payment {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  amount: number;
  currency: string;
  status: "COMPLETED" | "FAILED" | "PENDING" | "REFUNDED";
  provider: "STRIPE" | "PAYPAL";
  providerPaymentId: string;
  paymentType: "SUBSCRIPTION" | "CREDIT_PURCHASE";
  transactionId: string;
  metadata: {
    subscriptionId?: string;
    planName?: string;
    creditPackageName?: string;
    [key: string]: string | number | boolean | undefined | null;
  };
  createdAt: string;
}

// Filters
interface PaymentFilters {
  status: string;
  type: string;
  provider: string;
  search: string;
  page: number;
  limit: number;
}

const AdminPaymentsPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [filters, setFilters] = useState<PaymentFilters>({
    status: "all",
    type: "all",
    provider: "all",
    search: "",
    page: 1,
    limit: 10,
  });

  // Fetch data
  useEffect(() => {
    const fetchPayments = async () => {
      try {
        setLoading(true);

        // Build query params
        const queryParams = new URLSearchParams();
        if (filters.status && filters.status !== "all")
          queryParams.append("status", filters.status);
        if (filters.type && filters.type !== "all")
          queryParams.append("type", filters.type);
        if (filters.provider && filters.provider !== "all")
          queryParams.append("provider", filters.provider);
        if (filters.search) queryParams.append("search", filters.search);
        queryParams.append("page", filters.page.toString());
        queryParams.append("limit", filters.limit.toString());

        const response = await ApiService.get<
          ApiResponse<Payment[]> & {
            pagination: { total: number; page: number; limit: number };
          }
        >(`/admin/payment-management/payments?${queryParams.toString()}`);
        console.log("response", response);
        setPayments(response.data.data);
        setTotalCount(response.data.pagination.total || 0);
      } catch (error) {
        toast.error("Failed to fetch payments");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchPayments();
  }, [filters]);

  // Set header
  useEffect(() => {
    setHeaderContent({
      title: "Payment Transactions",
      description: "View and manage all payment transactions",
    });

    return () => setHeaderContent({});
  }, [setHeaderContent]);

  // Format currency
  const formatCurrency = (amount: number, currency: string) =>
    new Intl.NumberFormat("en-US", { style: "currency", currency }).format(
      amount
    );

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy h:mm a");
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return (
          <Badge
            variant="outline"
            className="bg-green-500/20 text-green-500 border-green-500"
          >
            Completed
          </Badge>
        );
      case "FAILED":
        return (
          <Badge
            variant="outline"
            className="bg-red-500/20 text-red-500 border-red-500"
          >
            Failed
          </Badge>
        );
      case "PENDING":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/20 text-yellow-500 border-yellow-500"
          >
            Pending
          </Badge>
        );
      case "REFUNDED":
        return (
          <Badge
            variant="outline"
            className="bg-blue-500/20 text-blue-500 border-blue-500"
          >
            Refunded
          </Badge>
        );
      // case "PARTIALLY_REFUNDED":
      //   return (
      //     <Badge
      //       variant="outline"
      //       className="bg-purple-500/20 text-purple-500 border-purple-500"
      //     >
      //       Partially Refunded
      //     </Badge>
      //   );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case "SUBSCRIPTION":
        return (
          <Badge
            variant="outline"
            className="bg-indigo-500/20 text-indigo-500 border-indigo-500"
          >
            Subscription
          </Badge>
        );
      case "ONE_TIME":
        return (
          <Badge
            variant="outline"
            className="bg-amber-500/20 text-amber-500 border-amber-500"
          >
            One-time
          </Badge>
        );
      case "CREDIT_PURCHASE":
        return (
          <Badge
            variant="outline"
            className="bg-cyan-500/20 text-cyan-500 border-cyan-500"
          >
            Credit Purchase
          </Badge>
        );
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  // Get provider badge
  const getProviderBadge = (provider: string) => {
    switch (provider) {
      case "STRIPE":
        return (
          <Badge
            variant="outline"
            className="bg-purple-500/20 text-purple-500 border-purple-500"
          >
            Stripe
          </Badge>
        );
      case "PAYPAL":
        return (
          <Badge
            variant="outline"
            className="bg-blue-500/20 text-blue-500 border-blue-500"
          >
            PayPal
          </Badge>
        );
      default:
        return <Badge variant="outline">{provider}</Badge>;
    }
  };

  // Handle filter change
  const handleFilterChange = (
    key: keyof PaymentFilters,
    value: string | number
  ) => {
    setFilters({
      ...filters,
      [key]: value,
      page: key === "page" ? Number(value) : 1,
    });
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters({ ...filters, page: 1 });
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      status: "all",
      type: "all",
      provider: "all",
      search: "",
      page: 1,
      limit: 10,
    });
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / filters.limit);

  return (
    <div className="space-y-6 px-4 sm:px-6 lg:px-8">
      <Card className="bg-white/5 text-white rounded-2xl border border-gray-500">
        <CardHeader>
          <CardTitle>Payment Transactions</CardTitle>
          <CardDescription className="text-gray-400">
            View all payment transactions in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="mb-6 space-y-4">
            <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
              <div className="w-full md:w-auto">
                <Input
                  placeholder="Search by user or transaction ID"
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="bg-gray-800 border-gray-700"
                />
              </div>
              <div className="w-full md:w-auto">
                <Select
                  value={filters.status}
                  onValueChange={(value) => handleFilterChange("status", value)}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-700 w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                    <SelectItem value="FAILED">Failed</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="REFUNDED">Refunded</SelectItem>
                    {/* <SelectItem value="PARTIALLY_REFUNDED">
                      Partially Refunded
                    </SelectItem> */}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-auto">
                <Select
                  value={filters.type}
                  onValueChange={(value) => handleFilterChange("type", value)}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-700 w-[180px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="SUBSCRIPTION">Subscription</SelectItem>
                    {/* <SelectItem value="ONE_TIME">One-time</SelectItem> */}
                    <SelectItem value="CREDIT_PURCHASE">
                      Credit Purchase
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-auto">
                <Select
                  value={filters.provider}
                  onValueChange={(value) =>
                    handleFilterChange("provider", value)
                  }
                >
                  <SelectTrigger className="bg-gray-800 border-gray-700 w-[180px]">
                    <SelectValue placeholder="Provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Providers</SelectItem>
                    <SelectItem value="STRIPE">Stripe</SelectItem>
                    <SelectItem value="PAYPAL">PayPal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2">
                <Button type="submit">Filter</Button>
                <Button type="button" variant="outline" onClick={resetFilters}>
                  Reset
                </Button>
              </div>
            </form>
          </div>

          {/* Payments Table */}
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : payments.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40">
              <AlertCircle className="w-8 h-8 text-gray-400 mb-2" />
              <p className="text-gray-400">No payment transactions found</p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead>
                    <tr>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        Transaction ID
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        User
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        Amount
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        Date
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        Status
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        Type
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        Provider
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                        Details
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {payments.map((payment) => (
                      <tr key={payment.id} className="border-b border-gray-700">
                        <td className="p-4 align-middle font-mono text-xs">
                          {payment.transactionId}
                        </td>
                        <td className="p-4 align-middle">
                          <div>
                            <div className="font-medium">
                              {payment.userName}
                            </div>
                            <div className="text-xs text-gray-400">
                              {payment.userEmail}
                            </div>
                          </div>
                        </td>
                        <td className="p-4 align-middle">
                          {formatCurrency(payment.amount, payment.currency)}
                        </td>
                        <td className="p-4 align-middle">
                          {formatDate(payment.createdAt)}
                        </td>
                        <td className="p-4 align-middle">
                          {getStatusBadge(payment.status)}
                        </td>
                        <td className="p-4 align-middle">
                          {getTypeBadge(payment.paymentType)}
                        </td>
                        <td className="p-4 align-middle">
                          {getProviderBadge(payment.provider)}
                        </td>
                        <td className="p-4 align-middle">
                          <div className="space-y-1">
                            {payment.paymentType === "SUBSCRIPTION" &&
                              payment.metadata.planName && (
                                <div className="text-xs">
                                  Plan: {payment.metadata.planName}
                                </div>
                              )}
                            {payment.paymentType === "CREDIT_PURCHASE" &&
                              payment.metadata.creditPackageName && (
                                <div className="text-xs">
                                  Package: {payment.metadata.creditPackageName}
                                </div>
                              )}
                            <a
                              href={
                                payment.provider === "STRIPE"
                                  ? `https://dashboard.stripe.com/payments/${payment.transactionId}`
                                  : `https://www.paypal.com/activity/payment/${payment.transactionId}`
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-xs flex items-center text-blue-400 hover:text-blue-300"
                            >
                              View on{" "}
                              {payment.provider === "STRIPE"
                                ? "Stripe"
                                : "PayPal"}
                              <ExternalLink className="ml-1 w-3 h-3" />
                            </a>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-400">
                  Showing {payments.length} of {totalCount} results
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      handleFilterChange("page", Math.max(1, filters.page - 1))
                    }
                    disabled={filters.page === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-400">
                    Page {filters.page} of {totalPages || 1}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      handleFilterChange(
                        "page",
                        Math.min(totalPages, filters.page + 1)
                      )
                    }
                    disabled={filters.page >= totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminPaymentsPage;
