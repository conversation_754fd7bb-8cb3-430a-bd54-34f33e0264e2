import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

// Mock data for a single blog post
const mockBlogPost = {
  id: "post-001",
  title: "Getting Started with AI Video Generation",
  slug: "getting-started-with-ai-video-generation",
  content: `
# Getting Started with AI Video Generation

## Introduction

AI video generation is revolutionizing how businesses create video content. With Miragic-AI's powerful tools, you can create professional-quality videos in minutes, not days.

## What You'll Need

- A Miragic-AI account
- A clear script for your video
- Basic understanding of your target audience

## Step 1: Write Your Script

The quality of your AI-generated video starts with a good script. Keep these tips in mind:

- Keep sentences short and clear
- Use natural language
- Include pauses with commas and periods
- Avoid complex technical jargon unless necessary

## Step 2: Choose Your Avatar

Miragic-AI offers a variety of avatars to represent your brand. Consider:

- Gender and appearance that matches your brand voice
- Clothing style appropriate for your industry
- Expression and tone that conveys your message

## Step 3: Select Background and Voice

The environment and voice of your video contribute significantly to its impact:

- Choose a background that complements your message
- Select a voice that sounds natural and engaging
- Ensure the voice matches the avatar's appearance

## Step 4: Generate and Review

After setting up your preferences:

1. Click "Generate Video"
2. Wait for processing (usually 2-5 minutes)
3. Review the result carefully
4. Make adjustments if necessary

## Tips for Success

- Test different avatar and voice combinations
- Keep videos under 2 minutes for better engagement
- Use bullet points in your script for clear transitions
- Include a call to action at the end

## Conclusion

AI video generation is a powerful tool in your content marketing arsenal. With practice, you'll be creating professional videos that engage your audience and drive results.

Start creating your first AI video today!
  `,
  excerpt: "Learn how to create professional videos with AI avatars using Miragic-AI's video generation tool.",
  author: "Emma Thompson",
  publishedAt: "May 16, 2025",
  status: "published",
  featuredImageUrl: "https://picsum.photos/seed/1/800/400",
  categories: ["Tutorials", "Video Generation"],
};

// Mock categories for selection
const availableCategories = [
  "Tutorials",
  "Video Generation",
  "Image Generation",
  "Background Removal",
  "Inspiration",
  "Insights",
  "AI Technology",
  "Tips & Tricks",
  "Case Studies",
  "Reviews",
];

interface AdminBlogEditPageProps {
  isNew?: boolean;
}

const AdminBlogEditPage = ({ isNew = false }: AdminBlogEditPageProps) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [discardModalOpen, setDiscardModalOpen] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    content: "",
    excerpt: "",
    featuredImageUrl: "",
    categories: [] as string[],
    status: "draft",
  });

  useEffect(() => {
    // If editing an existing post, load the data
    if (!isNew && id) {
      // In a real app, this would be an API call
      // For now, we'll just use our mock data
      setFormData({
        title: mockBlogPost.title,
        slug: mockBlogPost.slug,
        content: mockBlogPost.content,
        excerpt: mockBlogPost.excerpt,
        featuredImageUrl: mockBlogPost.featuredImageUrl,
        categories: mockBlogPost.categories,
        status: mockBlogPost.status,
      });
    }
  }, [isNew, id]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setUnsavedChanges(true);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    setFormData((prev) => ({
      ...prev,
      title,
      // Generate slug from title if user hasn't manually edited the slug
      slug: prev.slug === generateSlug(prev.title) ? generateSlug(title) : prev.slug,
    }));
    setUnsavedChanges(true);
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const slug = e.target.value;
    setFormData((prev) => ({
      ...prev,
      slug: generateSlug(slug),
    }));
    setUnsavedChanges(true);
  };

  const handleCategoryChange = (category: string) => {
    setFormData((prev) => {
      const newCategories = prev.categories.includes(category)
        ? prev.categories.filter((c) => c !== category)
        : [...prev.categories, category];
      return { ...prev, categories: newCategories };
    });
    setUnsavedChanges(true);
  };

  const generateSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .trim();
  };

  const handleSave = async (publish: boolean = false) => {
    if (publish) {
      setIsPublishing(true);
    } else {
      setIsSaving(true);
    }

    try {
      // In a real app, this would be an API call
      // For now, we'll just simulate a successful save
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const updatedPost = {
        ...formData,
        status: publish ? "published" : formData.status,
        publishedAt: publish ? new Date().toISOString() : formData.status === "published" ? mockBlogPost.publishedAt : null,
      };

      console.log("Saved post:", updatedPost);
      
      setUnsavedChanges(false);
      
      // Navigate back to blog list
      if (publish || !isNew) {
        navigate("/admin/blog");
      } else {
        // If it's a new post and just saving (not publishing), stay on the page
        // but update the URL to the edit URL
        navigate(`/admin/blog/edit/new-post-id`);
      }
    } catch (error) {
      console.error("Error saving post:", error);
    } finally {
      setIsSaving(false);
      setIsPublishing(false);
    }
  };

  const handleDiscard = () => {
    setDiscardModalOpen(false);
    navigate("/admin/blog");
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isNew ? "Create New Blog Post" : "Edit Blog Post"}
          </h1>
          <p className="text-muted-foreground">
            {isNew ? "Create a new blog post" : "Edit an existing blog post"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? "Edit" : "Preview"}
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              if (unsavedChanges) {
                setDiscardModalOpen(true);
              } else {
                navigate("/admin/blog");
              }
            }}
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            onClick={() => handleSave(false)}
            disabled={isSaving || isPublishing}
          >
            {isSaving ? "Saving..." : "Save Draft"}
          </Button>
          <Button
            onClick={() => handleSave(true)}
            disabled={isSaving || isPublishing}
          >
            {isPublishing ? "Publishing..." : "Publish"}
          </Button>
        </div>
      </div>

      {showPreview ? (
        // Preview Mode
        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>
              Preview how your blog post will appear to readers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.featuredImageUrl && (
              <div className="aspect-video rounded-md overflow-hidden">
                <img
                  src={formData.featuredImageUrl}
                  alt={formData.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold">{formData.title}</h1>
              <div className="flex flex-wrap gap-2">
                {formData.categories.map((category) => (
                  <span
                    key={category}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"
                  >
                    {category}
                  </span>
                ))}
              </div>
              <p className="text-muted-foreground">
                By {mockBlogPost.author} • {new Date().toLocaleDateString()}
              </p>
            </div>
            <div className="prose prose-sm sm:prose lg:prose-lg max-w-none">
              {/* In a real app, this would be a markdown renderer */}
              <div className="whitespace-pre-wrap">{formData.content}</div>
            </div>
          </CardContent>
        </Card>
      ) : (
        // Edit Mode
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Post Content</CardTitle>
                <CardDescription>
                  Write your blog post content
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="title" className="text-sm font-medium">
                    Title
                  </label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleTitleChange}
                    placeholder="Enter post title"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="slug" className="text-sm font-medium">
                    Slug
                  </label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleSlugChange}
                    placeholder="enter-post-slug"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    The slug is used in the URL of your blog post. It should be unique and contain only letters, numbers, and hyphens.
                  </p>
                </div>
                <div className="space-y-2">
                  <label htmlFor="content" className="text-sm font-medium">
                    Content
                  </label>
                  {/* In a real app, this would be a rich text editor or markdown editor */}
                  <textarea
                    id="content"
                    name="content"
                    value={formData.content}
                    onChange={handleChange}
                    placeholder="Write your blog post content here..."
                    className="w-full min-h-[400px] px-3 py-2 border rounded-md resize-y"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    You can use Markdown to format your content.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Post Settings</CardTitle>
                <CardDescription>
                  Configure your blog post settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="excerpt" className="text-sm font-medium">
                    Excerpt
                  </label>
                  <textarea
                    id="excerpt"
                    name="excerpt"
                    value={formData.excerpt}
                    onChange={handleChange}
                    placeholder="Brief summary of your post..."
                    className="w-full min-h-[100px] px-3 py-2 border rounded-md resize-y"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    A short summary that appears in blog listings.
                  </p>
                </div>
                <div className="space-y-2">
                  <label htmlFor="featuredImageUrl" className="text-sm font-medium">
                    Featured Image URL
                  </label>
                  <Input
                    id="featuredImageUrl"
                    name="featuredImageUrl"
                    value={formData.featuredImageUrl}
                    onChange={handleChange}
                    placeholder="https://example.com/image.jpg"
                  />
                  {formData.featuredImageUrl && (
                    <div className="mt-2 aspect-video rounded-md overflow-hidden">
                      <img
                        src={formData.featuredImageUrl}
                        alt="Featured"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Categories</label>
                  <div className="grid grid-cols-2 gap-2">
                    {availableCategories.map((category) => (
                      <div key={category} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`category-${category}`}
                          checked={formData.categories.includes(category)}
                          onChange={() => handleCategoryChange(category)}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label
                          htmlFor={`category-${category}`}
                          className="text-sm"
                        >
                          {category}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="status" className="text-sm font-medium">
                    Status
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border rounded-md"
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                  </select>
                </div>
              </CardContent>
              <CardFooter>
                <p className="text-xs text-muted-foreground">
                  Last updated: {new Date().toLocaleString()}
                </p>
              </CardFooter>
            </Card>
          </div>
        </div>
      )}

      {/* Discard Changes Modal */}
      {discardModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-lg shadow-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium mb-2">Discard Changes</h3>
            <p className="text-muted-foreground mb-4">
              You have unsaved changes. Are you sure you want to discard them?
            </p>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setDiscardModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDiscard}
              >
                Discard
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminBlogEditPage;
