import { useEffect, useMemo, useState } from "react";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import { Link } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import AdminService from "@/services/admin.service";
import { toast } from "sonner";
import { Spinner } from "@/components/ui/spinner";
import { AnalyticsIcon } from "@/lib/icons";

// Define TypeScript interfaces for data structures
interface AdminStats {
  totalUsers: number;
  activeSubscriptions: number;
  totalRevenue: number;
  activeJobs: number;
  completedJobs: number;
  blogPosts: number;
}

interface RecentUser {
  id: string;
  name: string;
  email: string;
  plan: string;
  joinDate: string;
}

interface RecentJob {
  id: string;
  type: "video" | "image" | "background-removal";
  user: string;
  status: "completed" | "processing" | "pending" | "failed";
  date: string;
}

const AdminDashboardPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentUsers, setRecentUsers] = useState<RecentUser[]>([
    {
      id: "1",
      name: "Test",
      email: "<EMAIL>",
      joinDate: "12-02-2025",
      plan: "Free",
    },
  ]);
  const [recentJobs] = useState<RecentJob[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");

  // Memoized header content (null as per requirement)
  const headerContent = useMemo(() => null, []);

  // Set header content with title and description
  useEffect(() => {
    setHeaderContent({
      title: "Home",
      description: "Overview of platform statistics and recent activity",
      content: headerContent,
    });

    // Cleanup to avoid stale content
    return () => setHeaderContent({});
  }, []);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        const [statsData, usersData] = await Promise.all([
          // _ = jobsData
          AdminService.getAdminStats(),
          AdminService.getRecentUsers(),
          AdminService.getRecentJobs(),
        ]);

        setStats(statsData);
        setRecentUsers(usersData);
        // setRecentJobs(jobsData);
        setError("");
      } catch (err) {
        console.error("Failed to fetch dashboard data:", err);
        setError("Failed to load dashboard data. Please try again.");
        toast.error("Failed to load dashboard data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <div className="space-y-8 px-4 sm:px-6 lg:px-8">
      {/* Error Message */}
      {error && (
        <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md">
          {error}
        </div>
      )}

      {/* Loading State */}
      {isLoading && !stats ? (
        <div className="flex justify-center items-center py-12">
          <Spinner className="h-8 w-8" />
        </div>
      ) : (
        <div>
          {/* Stats Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl py-0">
              <CardHeader className="space-y-0 pb-0 pt-3">
                <CardTitle className="text-sm font-normal pb-0">
                  Total Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-semibold font-inter">
                  {stats?.totalUsers || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  +12% from last month
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl py-0">
              <CardHeader className="space-y-0 pb-0 pt-3">
                <CardTitle className="text-sm font-normal pb-0">
                  Active Subscriptions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-semibold font-inter">
                  {stats?.activeSubscriptions || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {stats
                    ? Math.round(
                        (stats.activeSubscriptions / stats.totalUsers) * 100
                      )
                    : 0}
                  % of users
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl py-0">
              <CardHeader className="space-y-0 pb-0 pt-3">
                <CardTitle className="text-sm font-normal pb-0">
                  Total Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-semibold font-inter">
                  ${stats?.totalRevenue?.toLocaleString() || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  +8.2% from last month
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl py-0">
              <CardHeader className="space-y-0 pb-0 pt-3">
                <CardTitle className="text-sm font-normal pb-0">
                  Active Jobs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-semibold font-inter">
                  {stats?.activeJobs || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Currently processing
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl py-0">
              <CardHeader className="space-y-0 pb-0 pt-3">
                <CardTitle className="text-sm font-normal pb-0">
                  Completed Jobs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-semibold font-inter">
                  {stats?.completedJobs || 0}
                </div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl py-0">
              <CardHeader className="space-y-0 pb-0 pt-3">
                <CardTitle className="text-sm font-normal pb-0">
                  Blog Posts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-semibold font-inter">
                  {stats?.blogPosts || 0}
                </div>
                <p className="text-xs text-muted-foreground">+3 this month</p>
              </CardContent>
            </Card>
          </div>

          {/* Analytics Chart */}
          <Card className="bg-white/5 text-white rounded-2xl mt-8 border border-gray-500">
            <CardHeader>
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0">
                <CardTitle className="font-semibold">Analytics</CardTitle>
              </div>
              <CardDescription className="text-gray-400 text-base">
                Platform usage and revenue over time
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[170px] flex flex-col items-center justify-center gap-2">
              <AnalyticsIcon />
              <div className="text-center text-muted-foreground">
                <p>Analytics chart would be displayed here</p>
                <p className="text-sm">Showing data for the last month</p>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {/* Recent Users */}
            <Card className="bg-white/5 border border-gray-500 text-white rounded-2xl">
              <CardHeader className="pt-3">
                <div className="flex items-center justify-between">
                  <CardTitle>Recent Users</CardTitle>
                  <Link to="/admin/users">
                    <div className="cursor-pointer text-sm text-gray-400 underline hover:text-gray-200 bg-none">
                      View All
                    </div>
                  </Link>
                </div>
                <CardDescription>Recently registered users</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentUsers.length === 0 && !isLoading ? (
                    <div className="text-center py-4 text-muted-foreground">
                      No recent users found
                    </div>
                  ) : (
                    recentUsers.map((user) => (
                      <div
                        key={user.id}
                        className="flex flex-col sm:flex-row items-start sm:items-center justify-between border-b pb-4 last:border-0 last:pb-0 space-y-2 sm:space-y-0"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                            {/* {user.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")} */}
                            <img src="/png/avatar1.png" />
                          </div>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {user.email}
                            </p>
                          </div>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="text-sm font-medium">{user.plan}</p>
                          <p className="text-xs text-muted-foreground">
                            {user.joinDate}
                          </p>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Jobs */}
            <Card className="bg-white/5 bg-white/5 border border-gray-500 text-white rounded-2xl">
              <CardHeader className="pt-3">
                <div className="flex items-center justify-between">
                  <CardTitle>Recent Jobs</CardTitle>
                  <div className="cursor-pointer text-sm text-gray-400 underline hover:text-gray-200 bg-none">
                    View All
                  </div>
                </div>
                <CardDescription>Recently processed jobs</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentJobs.length === 0 && !isLoading ? (
                    <div className="text-center py-4 text-muted-foreground">
                      No recent jobs found
                    </div>
                  ) : (
                    recentJobs.map((job) => (
                      <div
                        key={job.id}
                        className="flex flex-col sm:flex-row items-start sm:items-center justify-between border-b pb-4 last:border-0 last:pb-0 space-y-2 sm:space-y-0"
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className={`h-10 w-10 rounded-full flex items-center justify-center 
                              ${
                                job.type === "video"
                                  ? "bg-blue-100"
                                  : job.type === "image"
                                  ? "bg-purple-100"
                                  : "bg-green-100"
                              }`}
                          >
                            <span className="text-sm font-medium">
                              {job.type.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">
                              {job.type === "video"
                                ? "Video Generation"
                                : job.type === "image"
                                ? "Image Generation"
                                : "Background Removal"}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {job.user}
                            </p>
                          </div>
                        </div>
                        <div className="text-left sm:text-right">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                              ${
                                job.status === "completed"
                                  ? "bg-green-100 text-green-800"
                                  : job.status === "processing" ||
                                    job.status === "pending"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                          >
                            {job.status.charAt(0).toUpperCase() +
                              job.status.slice(1)}
                          </span>
                          <p className="text-xs text-muted-foreground mt-1">
                            {job.date}
                          </p>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card className="bg-white/5 bg-white/5 border border-gray-500 text-white rounded-2xl mt-8">
            <CardHeader className="pt-3">
              <div className="flex justify-between">
                <div>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription className="mt-2">
                    Common administrative tasks
                  </CardDescription>
                </div>
                <div className="cursor-pointer text-sm text-gray-400 underline hover:text-gray-200 bg-none">
                  View All
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <Link to="/admin/users" className="bg-white/10 rounded-md">
                  <div className="w-full h-auto py-4 flex flex-col items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6 mb-2"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z"
                      />
                    </svg>
                    <span>Manage Users</span>
                  </div>
                </Link>
                <Link to="/admin/blog/new">
                  <div className="w-full h-auto py-4 flex flex-col items-center justify-center bg-white/10 rounded-md">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6 mb-2"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span>New Blog Post</span>
                  </div>
                </Link>
                <div className="cursor-pointer w-full h-auto py-4 flex flex-col items-center justify-center bg-white/10 rounded-md">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-6 h-6 mb-2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076-.124a6.57 6.57 0 01-.22-.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076.124.072.044.146.087.22.128.332.183.582.495.644.869l.214-1.28z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>System Settings</span>
                </div>
                <div className="cursor-pointer w-full h-auto py-4 flex flex-col items-center justify-center bg-white/10 rounded-md">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-6 h-6 mb-2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.25c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.25z"
                    />
                  </svg>
                  <span>View Reports</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AdminDashboardPage;
