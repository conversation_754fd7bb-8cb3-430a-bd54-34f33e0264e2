import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useApp } from '@/contexts/AppContext';
import { toast } from 'react-hot-toast';

const AuthCallbackPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useApp();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const userId = searchParams.get('user_id');
        const error = searchParams.get('error');
        const errorMessage = searchParams.get('message');

        if (error) {
          console.error('OAuth error:', error, errorMessage);
          toast.error(errorMessage || 'Authentication failed');
          navigate('/');
          return;
        }

        if (!accessToken || !refreshToken || !userId) {
          console.error('Missing OAuth tokens or user ID');
          toast.error('Authentication failed - missing credentials');
          navigate('/');
          return;
        }

        // Store tokens and log in user
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);

        // Fetch user data
        const response = await fetch('/api/v1/user/profile', {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user profile');
        }

        const userData = await response.json();
        
        if (userData.success) {
          // Update app context with user data
          login(userData.data, { accessToken, refreshToken });
          toast.success('Successfully signed in!');
          navigate('/dashboard');
        } else {
          throw new Error(userData.error?.message || 'Failed to fetch user data');
        }
      } catch (error) {
        console.error('OAuth callback error:', error);
        toast.error('Authentication failed');
        navigate('/');
      } finally {
        setIsProcessing(false);
      }
    };

    processOAuthCallback();
  }, [searchParams, navigate, login]);

  if (isProcessing) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Completing sign in...
          </h2>
          <p className="text-gray-600">
            Please wait while we set up your account.
          </p>
        </div>
      </div>
    );
  }

  return null;
};

export default AuthCallbackPage;
