import ShadowButton from "@/components/ui/shadowButton";
import BackgroundElements from "@/components/VirtualTryOn/BackgroundElements";
import ClothesModal from "@/components/VirtualTryOn/ClothesModal";
import ClothingUpload from "@/components/VirtualTryOn/ClothingUpload";
import ModelSelection from "@/components/VirtualTryOn/ModelSelection";
import RecentItems from "@/components/VirtualTryOn/RecentItems";
import ResultPanel from "@/components/VirtualTryOn/ResultPanel";
import {
  BOTTOM_CLOTH_IMAGES,
  DRESSES_JUMP_SUIT,
  MODELS_IMAGES,
  TOP_CLOTH_IMAGES,
} from "@/utils/DUMMY_DATA";
import React, { useState, useEffect, useCallback } from "react";
import VirtualTryOnService from "@/services/virtualTryOn.service";
import type {
  ModelImage,
  ClothingItem as BackendClothingItem,
  RecentTryOn,
} from "@/services/virtualTryOn.service";
import { useApp } from "@/contexts/useApp";
import { toast } from "sonner";
import { useCredits } from "@/hooks/useCredits";

// Define interfaces
interface ClothingItem {
  id: string;
  image: string;
  type: string;
}

const VirtualTryOnPage: React.FC = () => {
  // Authentication and user context
  const { user, isAuthenticated, userCredits } = useApp();
  const { refreshCredits, hasEnoughCredits } = useCredits();

  // Drag states
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [isDragOverTop, setIsDragOverTop] = useState<boolean>(false);
  const [isDragOverBottom, setIsDragOverBottom] = useState<boolean>(false);

  // Image display states
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedTopImage, setUploadedTopImage] = useState<string | null>(null);
  const [uploadedBottomImage, setUploadedBottomImage] = useState<string | null>(
    null
  );
  const [uploadedModelImage, setUploadedModelImage] = useState<string | null>(
    null
  );

  // File states for API calls
  const [uploadedImageFile, setUploadedImageFile] = useState<File | null>(null);
  const [uploadedTopImageFile, setUploadedTopImageFile] = useState<File | null>(
    null
  );
  const [uploadedBottomImageFile, setUploadedBottomImageFile] =
    useState<File | null>(null);
  const [uploadedModelFile, setUploadedModelFile] = useState<File | null>(null);

  // Selection states
  const [selectedClothingType, setSelectedClothingType] = useState<
    "Single clothes" | "Top & Bottom"
  >("Single clothes");
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [selectedRecentItem, setSelectedRecentItem] =
    useState<ClothingItem | null>(null);

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [modalTab, setModalTab] = useState<"all" | "top" | "bottom">("all");

  // Processing state
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  const [selectedTopItem, setSelectedTopItem] = useState<ClothingItem | null>(
    null
  );
  const [selectedBottomItem, setSelectedBottomItem] =
    useState<ClothingItem | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [selectedFit, setSelectedFit] = useState<string>("Regular Fit");
  const [selectedCategory, setSelectedCategory] = useState<string>("Top");

  // Backend data states
  const [modelImages, setModelImages] = useState<ModelImage[]>([]);
  const [recentTryOns, setRecentTryOns] = useState<RecentTryOn[]>([]);
  const [isLoadingData, setIsLoadingData] = useState<boolean>(true);

  // Processing states
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [processingProgress, setProcessingProgress] = useState<string>("");

  // Load data from backend on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoadingData(true);

        // Load model images
        const modelsResponse = await VirtualTryOnService.getModelImages();
        if (modelsResponse.success) {
          setModelImages(modelsResponse.data);
        }

        // Load recent try-ons
        const recentResponse = await VirtualTryOnService.getRecentTryOns();
        if (recentResponse.success) {
          setRecentTryOns(recentResponse.data);
        }
      } catch (error) {
        console.error("Error loading virtual try-on data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadData();
  }, []);

  const processFile = async (
    file: File,
    type: "single" | "top" | "bottom" | "model"
  ): Promise<void> => {
    const maxSize = 30 * 1024 * 1024; // 30MB
    if (file.size > maxSize) {
      toast.error("File size exceeds 30MB limit.");
      return;
    }
    if (
      !["image/png", "image/jpeg", "image/jpg", "image/webp"].includes(
        file.type
      )
    ) {
      toast.error("Invalid file format. Use PNG, JPG, JPEG, or WEBP.");
      return;
    }

    // Show immediate preview
    const reader: FileReader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>): void => {
      if (e.target?.result) {
        const result = e.target.result as string;
        switch (type) {
          case "single":
            setUploadedImage(result);
            setUploadedImageFile(file);
            setSelectedRecentItem(null);
            break;
          case "top":
            setUploadedTopImage(result);
            setUploadedTopImageFile(file);
            break;
          case "bottom":
            setUploadedBottomImage(result);
            setUploadedBottomImageFile(file);
            break;
          case "model":
            setUploadedModelImage(result);
            setUploadedModelFile(file);
            setSelectedModel(null);
            break;
        }
      }
    };
    reader.readAsDataURL(file);

    // Upload instantly to backend
    try {
      if (type === "model") {
        const uploadResponse = await VirtualTryOnService.uploadModelImage(file);
        if (uploadResponse.success) {
          toast.success("Model image uploaded successfully!");
          // Refresh model images list
          const modelsResponse = await VirtualTryOnService.getModelImages();
          if (modelsResponse.success) {
            setModelImages(modelsResponse.data);
          }
        } else {
          toast.error(uploadResponse.message || "Failed to upload model image");
        }
      } else if (type === "single" || type === "top" || type === "bottom") {
        // Determine category based on type
        let category = "TOP";
        if (type === "bottom") category = "BOTTOM";
        else if (selectedCategory === "Dress/Suit") category = "FULL_SET";

        const uploadResponse = await VirtualTryOnService.uploadClothingItem(
          file,
          `${type} clothing`,
          category,
          "CASUAL",
          `Uploaded ${type} clothing item`
        );

        if (uploadResponse.success) {
          const aiDetection = uploadResponse.data?.aiDetection;
          if (aiDetection && aiDetection.confidence > 0.7) {
            toast.success(
              `Clothing uploaded! AI detected: ${
                aiDetection.detectedType
              } (${Math.round(aiDetection.confidence * 100)}% confidence)`
            );
          } else if (aiDetection) {
            toast.success(
              `Clothing uploaded! AI detected: ${
                aiDetection.detectedType
              } (${Math.round(
                aiDetection.confidence * 100
              )}% confidence - please verify)`
            );
          } else {
            toast.success("Clothing item uploaded successfully!");
          }
          // Refresh clothing items list if needed
        } else {
          toast.error(
            uploadResponse.message || "Failed to upload clothing item"
          );
        }
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload image. Please try again.");
    }
  };

  const handleReset = (): void => {
    setUploadedImage(null);
    setUploadedImageFile(null);
    setUploadedTopImage(null);
    setUploadedTopImageFile(null);
    setUploadedBottomImage(null);
    setUploadedBottomImageFile(null);
    setUploadedModelImage(null);
    setUploadedModelFile(null);
    setSelectedModel(null);
    setSelectedRecentItem(null);
    setSelectedTopItem(null);
    setSelectedBottomItem(null);
    setGeneratedImage(null);
  };

  const handleDropdownChange = (fit: string, category: string): void => {
    setSelectedFit(fit);
    setSelectedCategory(category);
    console.log("Dropdown values changed:", { fit, category });
  };

  const handleDownload = (): void => {
    if (generatedImage) {
      // Fetch the image as a blob
      fetch(generatedImage)
        .then((response) => response.blob())
        .then((blob) => {
          // Create a temporary link to download the blob
          const link = document.createElement("a");
          const url = URL.createObjectURL(blob);
          link.href = url;
          link.download = `virtual-tryon-${Date.now()}.jpg`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url); // Clean up the object URL
        })
        .catch((error) => {
          console.error("Error fetching image:", error);
          alert("Failed to download the image.");
        });
    } else {
      console.log("No generated image to download");
      alert("No generated image available to download.");
    }
  };

  const urlToFile = async (url: string, filename: string): Promise<File> => {
    const response = await fetch(url);
    const blob = await response.blob();
    return new File([blob], filename, { type: blob.type });
  };

  const handleGenerate = async (): Promise<void> => {
    // Check authentication
    if (!isAuthenticated || !user) {
      toast.error("Please log in to use virtual try-on");
      return;
    }

    // Check credits
    if (!hasEnoughCredits("virtualTryOn")) {
      toast.error(
        "Insufficient credits. Please purchase more credits to continue."
      );
      return;
    }

    setIsProcessing(true);
    setProcessingProgress("Initializing...");
    console.log("🚀 Starting virtual try-on generation...");

    try {
      // Validate required inputs
      let hasRequiredClothing = false;
      let hasRequiredModel = false;

      // Check if we have required model
      if (uploadedModelFile || selectedModel) {
        hasRequiredModel = true;
      }

      // Check if we have required clothing
      if (selectedClothingType === "Single clothes") {
        if (uploadedImageFile || selectedRecentItem) {
          hasRequiredClothing = true;
        }
      } else {
        // Top & Bottom mode - need at least one piece
        if (
          uploadedTopImageFile ||
          selectedTopItem ||
          uploadedBottomImageFile ||
          selectedBottomItem
        ) {
          hasRequiredClothing = true;
        }
      }

      if (!hasRequiredModel) {
        alert("Please select or upload a model to continue.");
        return;
      }

      if (!hasRequiredClothing) {
        alert("Please select or upload clothing items to continue.");
        return;
      }

      console.log("✅ Validation passed");
      console.log("📋 Current state:", {
        selectedClothingType,
        uploadedModelFile: !!uploadedModelFile,
        selectedModel,
        uploadedImageFile: !!uploadedImageFile,
        selectedRecentItem: !!selectedRecentItem,
        uploadedTopImageFile: !!uploadedTopImageFile,
        selectedTopItem: !!selectedTopItem,
        uploadedBottomImageFile: !!uploadedBottomImageFile,
        selectedBottomItem: !!selectedBottomItem,
      });

      // Prepare parameters for the backend API service
      const params: {
        mode: "single" | "top_bottom";
        fit_type?: string;
        cloth_category?: string;
        human_image?: File;
        cloth_image?: File;
        low_cloth_image?: File;
        germent_type?: string;
        modelImageId?: string;
        saveAsModel?: boolean;
        modelName?: string;
        gender?: "MALE" | "FEMALE" | "UNISEX";
      } = {
        mode:
          selectedClothingType === "Single clothes" ? "single" : "top_bottom",
      };

      if (selectedClothingType === "Single clothes") {
        params.fit_type = selectedFit; // Add fit type
        params.cloth_category = selectedCategory; // Add category type
        console.log("📋 Dropdown values:", {
          fit_type: selectedFit,
          cloth_category: selectedCategory,
        });
      }

      console.log("🔧 Building params object...");

      // ALWAYS SET HUMAN IMAGE (model selection)
      if (uploadedModelFile) {
        console.log("👤 Using uploaded model file");
        params.human_image = uploadedModelFile;
      } else if (selectedModel) {
        console.log("👤 Converting selected model to file");
        try {
          const selectedModelData = MODELS_IMAGES.find(
            (m) => m.id === selectedModel
          );
          if (selectedModelData) {
            console.log(
              "📥 Converting model image URL to File:",
              selectedModelData.image
            );
            const modelFile = await urlToFile(
              selectedModelData.image,
              `model_${selectedModel.replace("Human_model/", "")}.jpg`
            );
            console.log(
              "✅ Model file created:",
              modelFile.name,
              modelFile.size,
              "bytes"
            );
            params.human_image = modelFile;
          }
          params.modelImageId = selectedModel.replace("Human_model/", "");
        } catch (error) {
          console.error("❌ Error converting model to file:", error);
          alert(
            "Error processing selected model. Please try uploading a model directly."
          );
          return;
        }
      }

      // Set cloth images based on selection
      if (selectedClothingType === "Single clothes") {
        console.log("👕 Processing single clothes mode");
        if (uploadedImageFile) {
          console.log("👕 Using uploaded cloth file");
          params.cloth_image = uploadedImageFile;

          // Determine garment type based on category
          if (selectedCategory === "Top") {
            params.germent_type = "upper";
          } else if (selectedCategory === "Bottom") {
            params.germent_type = "lower";
          } else {
            params.germent_type = "full_set"; // For dresses, suits, etc.
          }
        } else if (selectedRecentItem) {
          console.log("👕 Converting selected recent item to file");
          try {
            console.log(
              "📥 Converting cloth image URL to File:",
              selectedRecentItem.image
            );
            const clothFile = await urlToFile(
              selectedRecentItem.image,
              `cloth_${selectedRecentItem.id}.jpg`
            );
            console.log(
              "✅ Cloth file created:",
              clothFile.name,
              clothFile.size,
              "bytes"
            );
            params.cloth_image = clothFile;

            // Determine garment type based on item type
            if (selectedRecentItem.type === "top") {
              params.germent_type = "upper";
            } else if (selectedRecentItem.type === "bottom") {
              params.germent_type = "lower";
            } else {
              params.germent_type = "full_set"; // For dresses, suits, etc.
            }
          } catch (error) {
            console.error("❌ Error converting recent item to file:", error);
            alert(
              "Error processing selected clothing item. Please try uploading the image directly."
            );
            return;
          }
        }
      } else {
        console.log("👔 Processing top & bottom mode");
        let hasTopClothing = false;
        let hasBottomClothing = false;

        // Handle TOP clothing
        if (uploadedTopImageFile) {
          console.log("👔 Using uploaded top file");
          params.cloth_image = uploadedTopImageFile;
          hasTopClothing = true;
        } else if (selectedTopItem) {
          console.log("👔 Converting selected top item to file");
          try {
            console.log(
              "📥 Converting top image URL to File:",
              selectedTopItem.image
            );
            const topFile = await urlToFile(
              selectedTopItem.image,
              `top_${selectedTopItem.id}.jpg`
            );
            console.log(
              "✅ Top file created:",
              topFile.name,
              topFile.size,
              "bytes"
            );
            params.cloth_image = topFile;
            hasTopClothing = true;
          } catch (error) {
            console.error("❌ Error converting top item to file:", error);
            alert(
              "Error processing selected top item. Please try uploading the image directly."
            );
            return;
          }
        }

        // Handle BOTTOM clothing
        if (uploadedBottomImageFile) {
          console.log("👖 Using uploaded bottom file");
          params.low_cloth_image = uploadedBottomImageFile;
          hasBottomClothing = true;

          // For combination mode, if we only have bottom, use it as main cloth_image
          if (!hasTopClothing) {
            params.cloth_image = uploadedBottomImageFile;
          }
        } else if (selectedBottomItem) {
          console.log("👖 Converting selected bottom item to file");
          try {
            console.log(
              "📥 Converting bottom image URL to File:",
              selectedBottomItem.image
            );
            const bottomFile = await urlToFile(
              selectedBottomItem.image,
              `bottom_${selectedBottomItem.id}.jpg`
            );
            console.log(
              "✅ Bottom file created:",
              bottomFile.name,
              bottomFile.size,
              "bytes"
            );
            params.low_cloth_image = bottomFile;
            hasBottomClothing = true;

            // For combination mode, if we only have bottom, use it as main cloth_image
            if (!hasTopClothing) {
              params.cloth_image = bottomFile;
            }
          } catch (error) {
            console.error("❌ Error converting bottom item to file:", error);
            alert(
              "Error processing selected bottom item. Please try uploading the image directly."
            );
            return;
          }
        }

        // Set garment type for combination mode - this will be empty string in backend
        params.germent_type = ""; // Empty for combination mode

        console.log("📊 Top & Bottom summary:", {
          hasTopClothing,
          hasBottomClothing,
        });

        if (!hasTopClothing && !hasBottomClothing) {
          alert(
            "Please select at least one clothing item (top or bottom) to continue."
          );
          return;
        }
      }

      // Final validation
      if (!params.cloth_image) {
        console.error("❌ No cloth_image provided");
        toast.error("Please select at least one clothing item to continue.");
        setIsProcessing(false);
        return;
      }

      if (!params.human_image && !params.modelImageId) {
        console.error("❌ No human_image or modelImageId provided");
        toast.error("Please select or upload a model to continue.");
        setIsProcessing(false);
        return;
      }

      // Log the final params object
      console.log("🎯 Final params object:", params);
      console.log("📊 Params summary:", {
        mode: params.mode,
        hasHumanImage: !!params.human_image,
        humanImageName: params.human_image?.name,
        humanImageSize: params.human_image?.size,
        hasClothImage: !!params.cloth_image,
        clothImageName: params.cloth_image?.name,
        clothImageSize: params.cloth_image?.size,
        hasLowClothImage: !!params.low_cloth_image,
        lowClothImageName: params.low_cloth_image?.name,
        lowClothImageSize: params.low_cloth_image?.size,
        germent_type: params.germent_type,
        modelImageId: params.modelImageId,
      });

      // Call the backend API service
      console.log("🌐 Calling VirtualTryOnService.processVirtualTryOn...");
      const response = await VirtualTryOnService.processVirtualTryOn({
        ...params,
        cloth_image: params.cloth_image!, // We've validated this exists above
      });

      console.log("📡 Virtual try-on API response:", response);

      if (response.success && response.data?.jobId) {
        console.log(
          "🎉 Virtual try-on job created successfully:",
          response.data
        );

        // Poll for job completion
        const jobId = response.data.jobId;
        setCurrentJobId(jobId);
        setProcessingProgress("Processing your virtual try-on...");
        console.log("🔄 Polling for job completion:", jobId);

        const pollInterval = setInterval(async () => {
          try {
            const jobResponse = await VirtualTryOnService.getJob(jobId);
            console.log("📊 Job status:", jobResponse.data?.status);

            if (jobResponse.success && jobResponse.data) {
              const status = jobResponse.data.status;

              // Update progress message
              switch (status) {
                case "PENDING":
                  setProcessingProgress("Waiting in queue...");
                  break;
                case "PROCESSING":
                  setProcessingProgress("AI is generating your try-on...");
                  break;
                case "COMPLETED":
                  setProcessingProgress("Finalizing results...");
                  break;
              }

              if (status === "COMPLETED" && jobResponse.data.resultImagePath) {
                clearInterval(pollInterval);
                setGeneratedImage(jobResponse.data.resultImagePath);
                setProcessingProgress(
                  "✅ Virtual try-on completed successfully!"
                );
                console.log(
                  "🖼️ Generated image URL set:",
                  jobResponse.data.resultImagePath
                );

                // Refresh credits after successful completion
                await refreshCredits();
                toast.success("Virtual try-on completed successfully!");

                // Keep processing state true to show the result
                setCurrentJobId(null);

                // Clear progress after a short delay to show success message
                setTimeout(() => {
                  setProcessingProgress("");
                  setIsProcessing(false);
                }, 3000);
              } else if (status === "FAILED") {
                clearInterval(pollInterval);
                console.error(
                  "❌ Virtual try-on job failed:",
                  jobResponse.data.errorMessage
                );
                toast.error(
                  jobResponse.data.errorMessage ||
                    "Virtual try-on processing failed. Please try again."
                );
                setIsProcessing(false);
                setCurrentJobId(null);
                setProcessingProgress("");
              }
              // Continue polling if status is PENDING or PROCESSING
            }
          } catch (error) {
            console.error("❌ Error polling job status:", error);
            clearInterval(pollInterval);
            toast.error("Error checking job status. Please refresh the page.");
            setIsProcessing(false);
            setCurrentJobId(null);
            setProcessingProgress("");
          }
        }, 2000); // Poll every 2 seconds

        // Set a timeout to stop polling after 5 minutes
        setTimeout(() => {
          clearInterval(pollInterval);
          if (isProcessing) {
            toast.error(
              "Processing is taking longer than expected. Please check back later."
            );
            setIsProcessing(false);
            setCurrentJobId(null);
            setProcessingProgress("");
          }
        }, 300000); // 5 minutes
      } else {
        console.error("❌ Virtual try-on failed:", response.message);
        toast.error(
          response.message ||
            "Failed to start virtual try-on processing. Please try again."
        );
      }
    } catch (error) {
      console.error("💥 Error generating virtual try-on:", error);
      toast.error(
        "An error occurred while generating the virtual try-on. Please check your connection and try again."
      );
      setProcessingProgress("");
      setCurrentJobId(null);
    } finally {
      console.log("🏁 Generation process completed");
      setIsProcessing(false);
    }
  };

  const handleRecentItemClick = (item: ClothingItem): void => {
    setSelectedRecentItem(item);
    // Set the generated image from recent try-on
    setGeneratedImage(item.image);
    // Clear uploaded image when selecting from recent
    setUploadedImage(null);
    setUploadedImageFile(null);
  };

  const handleSeeAllClick = (): void => {
    setIsModalOpen(true);
    setModalTab("all");
  };

  // const handleModalItemClick = (item: ClothingItem): void => {
  //   setSelectedRecentItem(item);
  //   setUploadedImage(null);
  //   setUploadedImageFile(null);
  //   setIsModalOpen(false);
  // };

  // Update the modal item click handler
  const handleModalItemClick = (
    item: ClothingItem,
    targetType?: "single" | "top" | "bottom"
  ): void => {
    if (targetType === "top") {
      setSelectedTopItem(item);
      setUploadedTopImage(null);
      setUploadedTopImageFile(null);
    } else if (targetType === "bottom") {
      setSelectedBottomItem(item);
      setUploadedBottomImage(null);
      setUploadedBottomImageFile(null);
    } else {
      // Single clothes mode
      setSelectedRecentItem(item);
      setUploadedImage(null);
      setUploadedImageFile(null);
    }
    setIsModalOpen(false);
  };

  const handleModelSelect = (modelId: string): void => {
    setSelectedModel(modelId);
    setUploadedModelImage(null);
    setUploadedModelFile(null);
  };

  const handleModelFileSelect = (file: File): void => {
    processFile(file, "model");
  };

  const handleClearUploadedModel = (): void => {
    setUploadedModelImage(null);
    setUploadedModelFile(null);
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <BackgroundElements />

      <div className="relative z-10 w-full max-w-7xl px-6">
        {/* Header */}
        <div className="text-left mb-8">
          <h1 className="text-4xl font-inter font-semibold text-white mb-4">
            Virtual Try-On
          </h1>
        </div>

        <div className="flex gap-8 items-center">
          {/* Left Panel - Controls */}
          <div className="w-1/2 max-w-[400px]">
            <div className="bg-white/10 backdrop-blur-sm border border-gray-600 rounded-2xl p-6">
              {/* Clothing Selection */}
              <div className="mb-6">
                <h3 className="text-white text-lg font-medium mb-4">
                  Select Clothes
                </h3>

                {/* Clothing Type Buttons */}
                <div className="flex gap-8 mb-4 justify-between">
                  <ShadowButton
                    onClick={() => setSelectedClothingType("Single clothes")}
                    className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedClothingType === "Single clothes"
                        ? ""
                        : "!bg-gray-700 !text-gray-300 hover:!bg-gray-600"
                    }`}
                  >
                    Single Clothes
                  </ShadowButton>
                  <ShadowButton
                    onClick={() => setSelectedClothingType("Top & Bottom")}
                    className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedClothingType === "Top & Bottom"
                        ? ""
                        : "!bg-gray-700 !text-gray-300 hover:!bg-gray-600"
                    }`}
                  >
                    Top & Bottom
                  </ShadowButton>
                </div>

                {/* Clothing Upload Component */}
                <ClothingUpload
                  selectedClothingType={selectedClothingType}
                  isDragOver={isDragOver}
                  setIsDragOver={setIsDragOver}
                  uploadedImage={uploadedImage}
                  setUploadedImage={setUploadedImage}
                  selectedRecentItem={selectedRecentItem}
                  setSelectedRecentItem={setSelectedRecentItem}
                  isDragOverTop={isDragOverTop}
                  setIsDragOverTop={setIsDragOverTop}
                  isDragOverBottom={isDragOverBottom}
                  setIsDragOverBottom={setIsDragOverBottom}
                  uploadedTopImage={uploadedTopImage}
                  setUploadedTopImage={setUploadedTopImage}
                  uploadedBottomImage={uploadedBottomImage}
                  setUploadedBottomImage={setUploadedBottomImage}
                  onFileSelect={processFile}
                  selectedTopItem={selectedTopItem}
                  setSelectedTopItem={setSelectedTopItem}
                  selectedBottomItem={selectedBottomItem}
                  setSelectedBottomItem={setSelectedBottomItem}
                  onDropdownChange={handleDropdownChange}
                />
              </div>

              {/* Recent Items Component */}
              <RecentItems
                items={
                  isLoadingData
                    ? [] // Show empty while loading
                    : recentTryOns
                        .filter((tryOn) => tryOn.resultImagePath) // Only show completed try-ons
                        .map((tryOn) => ({
                          id: tryOn.id,
                          image: tryOn.resultImagePath || "",
                          type: "recent",
                        }))
                }
                selectedItem={selectedRecentItem}
                onItemClick={handleRecentItemClick}
                onSeeAllClick={handleSeeAllClick}
              />

              {/* Model Selection Component */}
              <ModelSelection
                models={
                  isLoadingData
                    ? MODELS_IMAGES
                    : modelImages.map((model) => ({
                        id: model.id,
                        image: model.imagePath || "",
                        type: model.isDefault ? "admin" : "user",
                      }))
                }
                selectedModel={selectedModel}
                uploadedModelImage={uploadedModelImage}
                onModelSelect={handleModelSelect}
                onFileSelect={handleModelFileSelect}
                onClearUploadedModel={handleClearUploadedModel}
              />

              {/* Generate Button */}
              <ShadowButton
                onClick={handleGenerate}
                disabled={isProcessing}
                className="w-full py-3 font-medium"
              >
                {isProcessing ? "Generating..." : "Generate"}
              </ShadowButton>
            </div>
          </div>

          {/* Right Panel - Result */}
          <div className="flex-1">
            <ResultPanel
              onReset={handleReset}
              onDownload={handleDownload}
              generatedImage={generatedImage}
              isProcessing={isProcessing}
              processingProgress={processingProgress}
              currentJobId={currentJobId}
            />
          </div>
        </div>
      </div>

      {/* Clothes Modal */}
      <ClothesModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        selectedTab={modalTab}
        onTabChange={setModalTab}
        allItems={DRESSES_JUMP_SUIT}
        topItems={TOP_CLOTH_IMAGES}
        bottomItems={BOTTOM_CLOTH_IMAGES}
        selectedItem={selectedRecentItem}
        onItemClick={handleModalItemClick}
        selectedTopItem={selectedTopItem}
        selectedBottomItem={selectedBottomItem}
        clothingMode={selectedClothingType}
      />
    </div>
  );
};

export default VirtualTryOnPage;
