import { useState } from "react";
import { Plus, Users } from "lucide-react";

const ResultLibraryNewPage = () => {
  const [activeTab, setActiveTab] = useState("Streaming Avatar");
  const [resultsFilter, setResultsFilter] = useState("Results");
  const [allFilter, setAllFilter] = useState("All");
//   const [hasData, setHasData] = useState(true);

  const tabs = [
    "Streaming Avatar",
    "Talking Avatar",
    "Face Swap",
    "Talking Photo",
    "Image Generator",
  ];

  // Sample data for when hasData is true
  const sampleResults = [
    {
      id: 1,
      type: "Streaming Avatar",
      title: "Professional Avatar 1",
      thumbnail: "/png/streaming_avatar_1.png",
      date: "2024-05-26",
    },
    {
      id: 2,
      type: "Streaming Avatar",
      title: "Professional Avatar 2",
      thumbnail: "/png/streaming_avatar_1.png",
      date: "2024-05-26",
    },
    {
      id: 2,
      type: "Talking Avatar",
      title: "Business Presentation",
      thumbnail:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face",
      date: "2024-05-25",
    },
    {
      id: 3,
      type: "Face Swap",
      title: "Creative Portrait",
      thumbnail:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop&crop=face",
      date: "2024-05-24",
    },
    {
      id: 4,
      type: "Image Generator",
      title: "AI Generated Art",
      thumbnail:
        "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=200&h=200&fit=crop&crop=face",
      date: "2024-05-23",
    },
  ];

  const filteredResults =
    activeTab === "All"
      ? sampleResults
      : sampleResults.filter((item) => item.type === activeTab);

  return (
    <div className="min-h-screen text-white px-5">
      {/* Header */}
      <div className="flex items-center justify-between p-6">
        <h1 className="text-3xl font-inter font-semibold">
          Hidilson Doho's Space
        </h1>
        <button className="cursor-pointer flex items-center gap-2 px-4 py-2 bg-transparent border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors">
          <Plus size={16} />
          Invite members
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="px-6 py-2 flex gap-2 justify-between border-b-2 border-gray-600">
        <div className="flex items-center gap-8">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`pb-3 px-1 text-sm font-medium transition-colors relative ${
                activeTab === tab
                  ? "text-white"
                  : "text-gray-400 hover:text-gray-300"
              }`}
            >
              {tab}
            </button>
          ))}
        </div>
        <div className="px-2 flex items-center gap-4">
          <select
            value={resultsFilter}
            onChange={(e) => setResultsFilter(e.target.value)}
            className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option>Results</option>
            <option>Drafts</option>
            <option>Favorites</option>
          </select>

          <select
            value={allFilter}
            onChange={(e) => setAllFilter(e.target.value)}
            className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option>All</option>
            <option>Recent</option>
            <option>Oldest</option>
          </select>

          {/* Toggle button for demo */}
          {/* <button
            onClick={() => setHasData(!hasData)}
            className="ml-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors"
          >
            {hasData ? "Empty" : "Show Data"}
          </button> */}
        </div>
      </div>

      {/* Content Area */}
      <div className="px-6 py-8">
        {filteredResults.length === 0 ? (
          /* Empty State */
          <div className="flex items-center justify-center min-h-96">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-6 bg-gray-800 rounded-full flex items-center justify-center">
                <Users size={32} className="text-gray-500" />
              </div>
              <p className="text-gray-400 text-lg">
                There are no items in here
              </p>
            </div>
          </div>
        ) : (
          /* Results Grid */
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {filteredResults.map((item) => (
              <div key={item.id} className="group cursor-pointer">
                <div className="aspect-square bg-gray-800 rounded-lg overflow-hidden mb-3 hover:ring-2 hover:ring-blue-500 transition-all">
                  <img
                    src={item.thumbnail}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                  />
                </div>
                <div className="space-y-1">
                  <h3 className="text-sm font-medium text-white truncate">
                    {item.title}
                  </h3>
                  <p className="text-xs text-gray-400">{item.type}</p>
                  <p className="text-xs text-gray-500">{item.date}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultLibraryNewPage;
