import { Text } from "@/components/ui/text";
import { FeatureItem } from "./FeatureItem";

const featuresData = [
  {
    id: "speedpainting",
    text: "Speedpainting",
    IconComponent: "/icons/speed.svg",
  },
  {
    id: "background-remover",
    text: "Background Remover",
    IconComponent: "/icons/remover.svg",
  },
  {
    id: "virtual-try-on",
    text: "Virtual Try-On",
    IconComponent: "/icons/cloth.svg",
  },
];

const AllPlansInclude = () => {
  return (
    <div className="mx-auto">
      <div className="relative text-center mb-10">
        <hr className="absolute top-1/2 left-0 w-full border-t border-gray-600" />
        <Text
          variant={"body"}
          className="relative inline-block px-6 text-[#D9D9D9] text-xl sm:text-2xl font-semibold bg-black"
        >
          All plans include
        </Text>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-[980px] mx-auto">
        {featuresData.map((feature) => (
          <FeatureItem
            key={feature.id}
            Icon={feature.IconComponent}
            text={feature.text}
          />
        ))}
      </div>
    </div>
  );
};

export default AllPlansInclude;
