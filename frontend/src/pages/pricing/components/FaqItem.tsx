import { But<PERSON> } from "@/components/ui/button";

interface FaqItemProps {
  question: string;
  answer: string;
  isOpen: boolean;
  onToggle: () => void;
}

export const FaqItem = ({
  question,
  answer,
  isOpen,
  onToggle,
}: FaqItemProps) => {
  return (
    <div className="border-b border-white/15 py-4">
      <Button
        className="cursor-pointer flex justify-between items-center w-full text-left text-white text-lg font-medium"
        onClick={onToggle}
        aria-expanded={isOpen}
      >
        <span>{question}</span>
        <span className="text-purple-400 transition-transform duration-300">
          {isOpen ? (
            <img src="/icons/up_arrow.svg" alt="Arrow Up" className="w-6 h-6" />
          ) : (
            <img
              src="/icons/down_arrow.svg"
              alt="Arrow Up"
              className="w-6 h-6"
            />
          )}
        </span>
      </Button>
      {isOpen && (
        <p className="mt-2 text-gray-400 text-base leading-relaxed px-4">{answer}</p>
      )}
    </div>
  );
};
