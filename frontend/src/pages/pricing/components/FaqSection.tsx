import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { useState } from "react";
import { FaqItem } from "./FaqItem";
import { faqData } from "@/components/common/variable";

const FaqSection = () => {
  const [openItemId, setOpenItemId] = useState<number | null>(null);
  const [showAll, setShowAll] = useState(false);

  const handleToggle = (id: number): void => {
    setOpenItemId(openItemId === id ? null : id);
  };

  const initialFaqs = faqData.slice(0, 3);
  const additionalFaqs = faqData.slice(3);

  return (
    <div className="px-4 sm:px-6 lg:px-8 font-inter">
      <div className="mx-auto">
        <Text
          variant={"body"}
          className="text-white xl:text-5xl text-4xl sm:text-4xl font-semibold text-center mb-10"
        >
          FAQ
        </Text>

        <div className="space-y-4">
          {initialFaqs.map((item) => (
            <FaqItem
              key={item.id}
              question={item.question}
              answer={item.answer}
              isOpen={openItemId === item.id}
              onToggle={() => handleToggle(item.id)}
            />
          ))}

          <div
            className={`
              overflow-hidden transition-all duration-700 ease-in-out
              ${showAll ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"}
            `}
          >
            <div className="pt-4 space-y-4">
              {additionalFaqs.map((item) => (
                <FaqItem
                  key={item.id}
                  question={item.question}
                  answer={item.answer}
                  isOpen={openItemId === item.id}
                  onToggle={() => handleToggle(item.id)}
                />
              ))}
            </div>
          </div>
        </div>

        {additionalFaqs.length > 0 && (
          <div className="text-center mt-10">
            <Button
              outline={true}
              variant={"animeShine"}
              onClick={() => setShowAll(!showAll)}
              className="px-9 py-3 text-base border border-white/15 rounded-full cursor-pointer bg-transparent hover:bg-transparent bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent h-[50px]"
            >
              {showAll ? "Show Less" : "See more FAQ"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FaqSection;
