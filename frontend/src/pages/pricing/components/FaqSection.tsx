import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { useState } from "react";
import { FaqItem } from "./FaqItem";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

const faqData: FaqItem[] = [
  {
    id: 1,
    question: "What is Miragic?",
    answer:
      "Miragic is a cutting-edge platform for face-swapping and generative AI-powered media tools.",
  },
  {
    id: 2,
    question: "How do I access Miragic through an API?",
    answer:
      "You can access Miragic functionalities through our robust API. Detailed documentation and API keys are available upon registration and plan selection.",
  },
  {
    id: 3,
    question:
      "Is it permissible for me to utilize the videos for the purpose of advertising services to my clients?",
    answer:
      "Yes, subject to our terms of service and licensing agreements, you are generally permitted to use generated media for advertising services to your clients. Please refer to our full terms for specific details.",
  },
];



const FaqSection = () => {
  // State to manage which FAQ items are open
  const [openItemId, setOpenItemId] = useState<number | null>(null);

  const handleToggle = (id: number): void => {
    setOpenItemId(openItemId === id ? null : id);
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8 font-inter">
      <div className="mx-auto">
        <Text
          variant={"body"}
          className="text-white xl:text-5xl text-4xl sm:text-4xl font-semibold text-center mb-10"
        >
          FAQ
        </Text>

        <div className="space-y-4">
          {faqData.map((item) => (
            <FaqItem
              key={item.id}
              question={item.question}
              answer={item.answer}
              isOpen={openItemId === item.id}
              onToggle={() => handleToggle(item.id)}
            />
          ))}
        </div>

        <div className="text-center mt-10">
          <Button
            outline={true}
            variant={"secondary"}
            className="px-9 py-3 text-base border border-white/15 rounded-full cursor-pointer bg-transparent hover:bg-transparent bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent"
          >
            See more FAQ
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FaqSection;
