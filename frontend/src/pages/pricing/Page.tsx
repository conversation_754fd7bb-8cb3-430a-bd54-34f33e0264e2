import { PricingCard } from "@/components/common/PricingCard";
import AllPlansInclude from "./components/AllPlansInclude";
import FaqSection from "./components/FaqSection";
import FutureSection from "./components/FutureSection";
import { CoreFeature } from "./components/CoreFeature";
import { GradientBackground } from "@/components/common/GradientBackground";

const Page = () => {
  return (
    <div className="relative overflow-hidden bg-black">
      <div className="relative">
        <GradientBackground
          imageUrl="/bg/price_bg.svg"
          startColor="rgba(18, 24, 40, 0.4)"
          endColor="rgba(18, 24, 40, 1)"
          direction="to bottom"
        />
        <PricingCard />
      </div>
      <div className="mx-auto max-w-[1440px] xl:space-y-[120px] md:space-y-20 space-y-16 px-4 sm:px-6 lg:px-8">
        <CoreFeature />
        <AllPlansInclude />
        <FaqSection />
        <FutureSection />
      </div>
    </div>
  );
};

export default Page;
