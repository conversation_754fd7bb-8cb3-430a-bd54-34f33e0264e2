import { PricingCard } from "@/components/common/PricingCard";
import AllPlansInclude from "./components/AllPlansInclude";
import FaqSection from "./components/FaqSection";
import FutureSection from "./components/FutureSection";
import { CoreFeature } from "./components/CoreFeature";

const Page = () => {
  return (
    <div className="relative overflow-hidden bg-black">
      {/* <img
        className="absolute top-0 left-0 opacity-25"
        src="/png/pattern_1.png"
      />
      <img className="absolute top-0 left-0 " src="/png/stars_1.png" />
      <img
        className="absolute top-1/2 transform -translate-y-1/2  right-0 w-4/5 "
        src="/png/shadow_hero_sec.png"
      />
      <img
        className="absolute transform scale-y-[-1]  translate-y-1/2  top-1/2  right-0 w-4/5 "
        src="/png/shadow_hero_sec.png"
      /> */}
      <div className="relative">
        <PricingCard />
      </div>
      <div className="mx-auto max-w-[1440px] xl:space-y-[120px] md:space-y-20 space-y-16">
        <CoreFeature />
        <AllPlansInclude />
        <FaqSection />
        <FutureSection />
      </div>
    </div>
  );
};



export default Page;
