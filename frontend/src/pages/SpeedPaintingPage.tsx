import {
  useState,
  useEffect,
  useContext,
  type ChangeEvent,
  type DragEvent,
} from "react";
import {
  Upload,
  Download,
  RefreshCw,
  Alert<PERSON>ircle,
  Loader2,
  Spark<PERSON>,
} from "lucide-react";
import { AppContext } from "@/contexts/AppContext";
import SpeedpaintService from "@/services/speedpaint.service";
import type { SpeedpaintJob } from "@/services/speedpaint.service";
import CreditService from "@/services/credit.service";
import type { ServiceCost } from "@/services/credit.service";
import AiCustomBgSelectionModal from "@/components/AllTools/AiCustomBgSelectionModal";
// import VideoPlayer from "@/components/SpeedPainting/VideoPlayer";

const SpeedPaintingPage = () => {
  const { isAuthenticated, setUserCredits, userCredits } =
    useContext(AppContext);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  // const [prompt, setPrompt] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processedVideo, setProcessedVideo] = useState<string | null>(null);
  const [currentJob, setCurrentJob] = useState<SpeedpaintJob | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [serviceCost, setServiceCost] = useState<number | null>(null);
  // const [userCredits, setUserCredits] = useState<number | null>(null);
  const [insufficientCredits, setInsufficientCredits] =
    useState<boolean>(false);
  const [isOpenBgSelectModal, setIsOpenBgSelectModal] =
    useState<boolean>(false);
  // const [useVideoFallback, setUseVideoFallback] = useState<boolean>(false);

  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
    const files: FileList = e.dataTransfer.files;
    if (files.length > 0) {
      const file: File = files[0];
      setUploadedFile(file);
      const reader: FileReader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>): void => {
        if (e.target?.result) {
          setUploadedImage(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
    const files: FileList | null = e.target.files;
    if (files && files.length > 0) {
      const file: File = files[0];
      setUploadedFile(file);
      const reader: FileReader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>): void => {
        if (e.target?.result) {
          setUploadedImage(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleReset = (): void => {
    setUploadedImage(null);
    setUploadedFile(null);
    setProcessedVideo(null);
    setCurrentJob(null);
    setError(null);
    setIsProcessing(false);
  };

  const handleDownload = (): void => {
    if (currentJob?.processedVideoUrl) {
      SpeedpaintService.handleDownload(
        currentJob.processedVideoUrl,
        `speedpaint-${new Date().getTime()}.mp4`
      );
    }
  };

  const handleProcessImage = async (): Promise<void> => {
    if (!uploadedFile || !isAuthenticated) return;

    try {
      setError(null);
      setIsProcessing(true);

      // Create speedpaint job
      const response = await SpeedpaintService.createJob(uploadedFile);
      const jobId = response.data.jobId;

      // Poll for job completion
      const checkJobStatus = async () => {
        try {
          const jobResponse = await SpeedpaintService.getJob(jobId);
          const job = jobResponse.data;
          const previousStatus = currentJob?.status;
          setCurrentJob(job);

          if (job.status === "COMPLETED" && job.processedVideoUrl) {
            setProcessedVideo(job.processedVideoUrl);
            setIsProcessing(false);

            // Explicitly refresh credit balance when job transitions to COMPLETED
            if (isAuthenticated && previousStatus !== "COMPLETED") {
              console.log("Job completed, refreshing credit balance...");
              try {
                // Add a small delay to ensure the backend has processed the credit deduction
                setTimeout(async () => {
                  const balanceResponse =
                    await CreditService.getUserCreditBalance();
                  console.log(
                    "New credit balance:",
                    balanceResponse.data.balance
                  );
                  setUserCredits(balanceResponse.data);
                }, 1000);
              } catch (error) {
                console.error("Error refreshing credit balance:", error);
              }
            }
          } else if (job.status === "FAILED") {
            setError(job.error || "Speed painting generation failed");
            setIsProcessing(false);
          } else if (job.status === "PENDING" || job.status === "PROCESSING") {
            // Continue polling
            setTimeout(checkJobStatus, 5000);
          }
        } catch (error) {
          console.error("Error checking job status:", error);
          setError("Failed to check job status");
          setIsProcessing(false);
        }
      };

      // Start polling
      setTimeout(checkJobStatus, 5000);
    } catch (error: unknown) {
      console.error("Error processing speedpaint:", error);

      // Type guard for error object with response property
      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as {
          response?: { data?: { error?: { code?: string; message?: string } } };
        };

        if (apiError.response?.data?.error?.code === "INSUFFICIENT_CREDITS") {
          setInsufficientCredits(true);
          setError(
            "You don't have enough credits for this operation. Please purchase more credits."
          );
        } else {
          setError(
            "Failed to generate speedpaint: " +
              (apiError.response?.data?.error?.message || "Unknown error")
          );
        }
      } else if (error instanceof Error) {
        setError("Failed to generate speedpaint: " + error.message);
      } else {
        setError("Failed to generate speedpaint: Unknown error");
      }

      setIsProcessing(false);
    }
  };

  // Function to fetch user's credit balance
  const fetchUserCreditBalance = async () => {
    if (!isAuthenticated) return;

    try {
      const balanceResponse = await CreditService.getUserCreditBalance();
      setUserCredits(balanceResponse.data);
    } catch (error) {
      console.error("Error fetching credit balance:", error);
    }
  };

  // Fetch service cost and user credits on component mount
  useEffect(() => {
    const fetchCreditInfo = async () => {
      try {
        // Get service costs
        const costsResponse = await CreditService.getServiceCosts();
        const costs: ServiceCost = costsResponse.data;
        setServiceCost(costs.speedpaint);

        // Get user's credit balance
        await fetchUserCreditBalance();
      } catch (error) {
        console.error("Error fetching credit information:", error);
      }
    };

    fetchCreditInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  // Refresh credit balance when job status changes to COMPLETED
  useEffect(() => {
    if (currentJob?.status === "COMPLETED") {
      fetchUserCreditBalance();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentJob?.status]);

  return (
    <div className="min-h-[86vh] flex items-center justify-center relative">
      <img
        src="/png/image_gene_shadow_1.png"
        className="absolute bottom-[-24px] right-[-24px]"
        alt="Decorative shadow"
      />

      {/* Background Grid Pattern with fade effect */}
      <div className="absolute inset-0">
        <div
          className="grid grid-cols-12 h-full opacity-10"
          style={{
            maskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
            WebkitMaskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
          }}
        >
          {Array.from({ length: 144 }).map((_, i: number) => (
            <div key={i} className="border border-white/20"></div>
          ))}
        </div>
      </div>

      {/* Floating Dots */}
      <div className="absolute top-20 right-20 w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
      <div className="absolute top-40 right-40 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-1000"></div>
      <div className="absolute bottom-32 left-32 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-500"></div>
      <div className="absolute bottom-20 right-60 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-700"></div>

      <div className="relative z-10 w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">Speedpainting</h1>
          <p className="text-gray-500 text-base">
            Generate a speedpainting video from reference image.
          </p>
        </div>

        {/* Prompt Input */}
        {/* <div className="mb-6">
          <div className="relative">
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe what you want to see in the speedpaint video..."
              className="w-full p-4 pr-12 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none h-24"
            />
            <Sparkles
              className="absolute right-3 top-3 text-gray-500"
              size={20}
            />
          </div>
        </div> */}

        {/* Upload Area */}
        <div
          className={`relative bg-gray-800/50 backdrop-blur-sm border-2 min-h-[400px] border-dashed rounded-2xl text-center flex flex-col justify-center transition-all duration-300 ${
            isDragOver
              ? "border-purple-400 bg-purple-900/30"
              : "border-gray-600 hover:border-gray-500"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {!processedVideo ? (
            <div className="p-12">
              {/* Upload Button */}
              {!uploadedImage && (
                <div className="mb-6">
                  <label className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg cursor-pointer transition-colors">
                    <Upload size={20} />
                    Choose a Reference Image
                    <input
                      type="file"
                      className="hidden"
                      accept=".png,.jpg,.jpeg,.webp"
                      onChange={handleFileSelect}
                    />
                  </label>
                </div>
              )}

              {uploadedImage && (
                <div className="mb-6">
                  <img
                    src={uploadedImage}
                    alt="Reference"
                    className="w-full max-h-40 object-contain mx-auto rounded-lg"
                    style={{
                      background:
                        "url(\"data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' width='20' height='20' patternUnits='userSpaceOnUse'%3e%3crect width='10' height='10' fill='%23f3f4f6'/%3e%3crect x='10' y='10' width='10' height='10' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e\")",
                    }}
                  />
                </div>
              )}

              {/* Generate Button */}
              {uploadedImage && (
                <div className="mt-6">
                  {/* <button
                    onClick={handleReset}
                    className="flex items-center gap-2 px-3 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    <RefreshCw />
                  </button> */}
                  <button
                    onClick={handleProcessImage}
                    disabled={
                      isProcessing || insufficientCredits || !uploadedImage
                    }
                    className={`w-full flex items-center justify-center gap-2 px-6 py-3 cursor-pointer ${
                      isProcessing || insufficientCredits || !uploadedImage
                        ? "bg-gray-600 cursor-not-allowed"
                        : "bg-purple-600 hover:bg-purple-700"
                    } text-white font-medium rounded-lg transition-colors`}
                  >
                    {isProcessing ? (
                      <Loader2 className="animate-spin" size={20} />
                    ) : (
                      <Sparkles size={20} />
                    )}
                    {isProcessing
                      ? "Generating Speedpaint..."
                      : "Generate Speedpaint"}
                  </button>
                </div>
              )}

              {/* File Format Info */}
              <p className="text-gray-400 text-sm mt-6">
                Reference image must be .png, .jpg, .jpeg or .webp format and
                10mb max size.
              </p>
            </div>
          ) : (
            // <div className="relative">
            //   {/* Video Display */}
            //   <div className="relative p-8">
            //     {/* <VideoPlayer
            //       videoUrl={processedVideo || null}
            //       onError={(errorMsg) => {
            //         console.log("Video player error:", errorMsg);
            //         setError(errorMsg);
            //         // Switch to fallback after error
            //         setUseVideoFallback(true);
            //       }}
            //       className="w-full"
            //     /> */}
            //   </div>

            //   {/* Control Buttons */}
            //   <div className="flex justify-center space-x-4 mt-4">
            //     <button
            //       onClick={handleDownload}
            //       className="flex items-center justify-center space-x-2 bg-primary hover:bg-primary/80 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            //     >
            //       <Download size={20} />
            //       <span>Download</span>
            //     </button>
            //     <button
            //       onClick={() => setUseVideoFallback(!useVideoFallback)}
            //       className="flex items-center justify-center space-x-2 bg-gray-600 hover:bg-gray-500 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            //     >
            //       {useVideoFallback ? (
            //         <>
            //           <span>Try Video Player</span>
            //         </>
            //       ) : (
            //         <>
            //           <span>Direct Download</span>
            //         </>
            //       )}
            //     </button>
            //     <button
            //       onClick={handleReset}
            //       className="flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            //     >
            //       <RefreshCw size={20} />
            //       <span>Create New</span>
            //     </button>
            //   </div>
            // </div>
            <div className="relative">
              {/* Video Display */}
              <div className="relative p-8">
                <video
                  src={processedVideo || undefined}
                  controls
                  className="w-full h-80 object-contain mx-auto rounded-lg bg-gray-800/50"
                />
              </div>

              {/* Control Buttons */}
              <div className="absolute bottom-4 left-4 right-4 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center justify-between">
                <button
                  onClick={handleReset}
                  className="flex items-center gap-2 px-3 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  <RefreshCw />
                </button>

                <button
                  onClick={handleDownload}
                  className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  <Download size={18} />
                  Download
                </button>
              </div>
            </div>
          )}

          {/* Drag Overlay */}
          {isDragOver && (
            <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-2xl flex items-center justify-center">
              <div className="text-center">
                <Upload size={32} className="text-purple-300 mx-auto mb-2" />
                <p className="text-purple-200 font-medium">
                  Drop your files here
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Credit Information */}
        <div className="mt-8 text-center">
          {serviceCost !== null && (
            <p className="text-gray-400 text-sm">
              This operation costs{" "}
              <span className="text-purple-400 font-semibold">
                {serviceCost} credit{serviceCost !== 1 ? "s" : ""}
              </span>
              {isAuthenticated && userCredits?.balance && (
                <span>
                  {" "}
                  • You have{" "}
                  <span className="text-purple-400 font-semibold">
                    {userCredits?.balance} credit
                    {userCredits?.balance !== 1 ? "s" : ""}
                  </span>
                </span>
              )}
            </p>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-4 p-3 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
            <AlertCircle className="text-red-500" size={18} />
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Not Authenticated Warning */}
        {!isAuthenticated && (
          <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-700 rounded-lg flex items-center gap-2">
            <AlertCircle className="text-yellow-500" size={18} />
            <p className="text-yellow-400 text-sm">
              You need to{" "}
              <a href="/auth/login" className="underline hover:text-yellow-300">
                log in
              </a>{" "}
              to use this feature.
            </p>
          </div>
        )}
      </div>

      <AiCustomBgSelectionModal
        isOpen={isOpenBgSelectModal}
        onClose={() => setIsOpenBgSelectModal(false)}
        onSelect={() => {}}
      />
    </div>
  );
};

export default SpeedPaintingPage;
