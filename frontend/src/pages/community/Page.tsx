import Section from "@/components/layout/Section";
import { Text } from "@/components/ui/text";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";

const Page = () => {
  return (
    <div className="bg-[#000000] relative">
      <img
        className="absolute top-0 left-0  "
        src="/png/about_hero_shadow.png"
      />
      <Hero />
      <Community />
      {/* <BlogList /> */}
    </div>
  );
};

const Hero = () => {
  return (
    <Section className="py-[106px] z-[999]">
      <div className="flex gap-16 justify-end items-center ">
        {/* <div className="flex-1">
        <img className="max-w-[250px]" src="/png/about_logo.png" />
      </div> */}

        <div className="flex flex-col gap-6 justify-end items-end ">
          <Text
            font="Inter"
            variant={"page_title"}
            className="bg-clip-text text-transparent bg-gradient-to-l from-white via-white to-gray-500"
          >
            MiragicAI Community
          </Text>
          <Text font="Inter" variant={"sub_title"} className="text-right">
            Discover the results by Generative AI tools from MiragicAI
          </Text>
        </div>
      </div>
    </Section>
  );
};

const imagesData = [
  { id: 1, src: "/png/result_demo_1.png", categories: ["Virtual Try On"] },
  { id: 2, src: "/png/result_demo_2.png", categories: ["Speed Painting"] },
  { id: 3, src: "/png/result_demo_3.png", categories: ["Background Remover"] },
  { id: 4, src: "/png/result_demo_4.png", categories: ["Virtual Try On"] },
  { id: 5, src: "/png/result_demo_5.png", categories: ["Speed Painting"] },
  { id: 6, src: "/png/result_demo_6.png", categories: ["Background Remover"] },
  { id: 7, src: "/png/result_demo_7.png", categories: ["Virtual Try On"] },
  { id: 8, src: "/png/result_demo_8.png", categories: ["Speed Painting"] },
  { id: 9, src: "/png/result_demo_9.png", categories: ["Background Remover"] },
  { id: 10, src: "/png/result_demo_10.png", categories: ["Virtual Try On"] },
  { id: 11, src: "/png/result_demo_11.png", categories: ["Speed Painting"] },
  {
    id: 12,
    src: "/png/result_demo_12.png",
    categories: ["Background Remover"],
  },
];
function shuffleArray<T>(array: T[]): T[] {
  const arr = [...array];
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr;
}

export const Community = () => {
  const [activeFeature, setActiveFeature] = useState("All");
  const [shuffledImages, setShuffledImages] = useState(imagesData);

  // Shuffle images only once on component mount
  useEffect(() => {
    setShuffledImages(shuffleArray(imagesData));
  }, []);

  // Filter images by activeFeature (category)
  const filteredImages =
    activeFeature === "All"
      ? shuffledImages
      : shuffledImages.filter((img) => img.categories.includes(activeFeature));

  const communityCtg = [
    "All",
    "Virtual Try On",
    "Speed Painting",
    "Background Remover",
  ];
  // result_demo_1
  return (
    <Section className="pb-[106px]">
      <div className="flex flex-col gap-8">
        <div className="flex flex-col gap-8">
          <Text variant={"body"} className="text-white z-10">
            Community
          </Text>
        </div>
        <div className="pb-3 flex flex-wrap border-b border-border justify-start gap-8">
          {communityCtg.map((title) => (
            <Text
              variant="body"
              onClick={() => setActiveFeature(title)}
              className={`z-20 text-lg cursor-pointer ${
                activeFeature === title ? "text-white" : "text-gray-400"
              }`}
              key={title}
            >
              {title}
            </Text>
          ))}
        </div>
        <div className="bg-gradient-to-b gap-x-4 gap-y-8 p-5 pb-10 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 from-transparent to-[#131619]">
          {filteredImages.map((img) => (
            <img
              key={img.id}
              src={img.src}
              alt={`result demo ${img.id}`}
              className="w-full h-full z-10 rounded-xl object-cover"
              loading="lazy"
            />
          ))}
        </div>
        <div className="flex mt-10 w-full justify-between items-center">
          <div></div>
          <div className="flex items-center gap-8">
            <ChevronLeft className="text-[32px] text-white" />
            <div className="h-10 w-10 bg-white flex items-center text-center justify-center rounded-2xl">
              <Text variant={"body"} className="text-center text-gray-950">
                1
              </Text>
            </div>
            <ChevronRight className="text-[32px] text-white" />
          </div>
          <div className="flex items-center gap-2">
            <div className="h-10 w-10  border-border border-2 flex items-center text-center justify-center rounded-2xl">
              <Text variant={"body"} className="text-center text-white">
                1
              </Text>
            </div>{" "}
            <Text variant={"body"} className="text-center text-gray-400">
              / 1
            </Text>
          </div>{" "}
        </div>
      </div>
    </Section>
  );
};

export default Page;
