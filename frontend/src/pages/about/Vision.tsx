import Section from "@/components/layout/Section";
import { Text } from "@/components/ui/text";

const Vision = () => {
  return (
    <Section className="py-[40px] bg-[url('/png/vision_bg.png')] bg-cover bg-center bg-no-repeat">
      <div className="flex flex-col gap-5">
        <Text
          variant={"page_title"}
          className="bg-clip-text text-transparent bg-gradient-to-l from-white via-white to-gray-500"
        >
          Our Vision
        </Text>

        <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-x-12 gap-y-8">
          <div className="flex flex-col overflow-hidden relative bg-[#FFFFFF0F]  items-start gap-5 border border-[#FFFFFF45] rounded-[25px] px-6 py-[40px]">
            <Text variant={"card_body"} className="text-white">
              AI-Driven Content personalization
            </Text>
            <Text variant={"body"} className="">
              Deliver highly targeted, engaging marketing compaigns with
              AI-powered content personalization.
            </Text>
          </div>
          <div className="flex flex-col overflow-hidden relative bg-[#FFFFFF0F]  items-start gap-5 border border-[#FFFFFF45] rounded-[25px] px-6 py-[40px]">
            <Text variant={"card_body"} className="text-white">
              AI-Powered Storytelling
            </Text>
            <Text variant={"body"} className="">
              Whether developing AI-generated influencers, interactive short
              -form videos, border immersive entertainment experiences.
            </Text>
          </div>
          <div className="flex flex-col overflow-hidden relative bg-[#FFFFFF0F]  items-start gap-5 border border-[#FFFFFF45] rounded-[25px] px-6 py-[40px]">
            <Text variant={"card_body"} className="text-white">
              AI-Powered Digital Assistant
            </Text>
            <Text variant={"body"} className="">
              Transform user experiences with AI-driven avatars that see, hear,
              and respond like real humans, customer support applications.
            </Text>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default Vision;
