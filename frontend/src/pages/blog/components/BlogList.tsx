import Section from "@/components/layout/Section";
// import BlogCard250227 from "./20250227";
// import BlogCard250215 from "./20250215";
// import BlogCard250202 from "./20250202";
// import BlogCard250127 from "./20250127";
// import BlogCard250117 from "./20250117";
// import BlogCard250105 from "./20250105";
// import BlogCard241227 from "./20241227";
// import BlogCard241215 from "./20241215";
// import BlogCard241130 from "./20241130";
// import BlogCard241115 from "./20241115";
// import BlogCard241109 from "./20241109";
// import BlogCard241101 from "./20241101";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Text } from "@/components/ui/text";
import type { BlogPost } from "@/services/blog.service";
import BlogCard from "@/components/Blogs/BlogCard";

interface BlogListProps {
  blogs: BlogPost[];
  totalPages: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
}

const BlogList: React.FC<BlogListProps> = ({
  blogs,
  totalPages,
  currentPage,
  setCurrentPage,
}) => {

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageClick = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than or equal to max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(totalPages, currentPage + 2);

      // Adjust if we're near the beginning or end
      if (currentPage <= 3) {
        endPage = Math.min(totalPages, 5);
      } else if (currentPage >= totalPages - 2) {
        startPage = Math.max(1, totalPages - 4);
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  return (
    <Section className=" pb-[130px]">
      <div className="flex items-start flex-col gap-14">
        <select
          // value={resultsFilter}
          // onChange={(e) => setResultsFilter(e.target.value)}
          className="bg-transparent z-10 min-w-[140px] border border-gray-600 rounded-lg px-4 py-2.5 text-sm font-normal text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          style={{ fontSize: "14px", fontWeight: 400 }}
        >
          <option>Topic</option>
          <option>Drafts</option>
          <option>Favorites</option>
        </select>
        
        <div className="w-full items-center justify-center pb-20 grid grid-cols-1 sm:grid-cols-2 gap-y-8 gap-x-6 md:grid-cols-3">
          {blogs.map((blog) => (
            <BlogCard key={blog.id} blog={blog} />
          ))}
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex w-full justify-between items-center">
            <div></div>
            
            {/* Navigation Controls */}
            <div className="flex items-center gap-4">
              <button
                onClick={handlePreviousPage}
                disabled={currentPage === 1}
                className={`${
                  currentPage === 1 
                    ? 'text-gray-500 cursor-not-allowed' 
                    : 'text-white hover:text-gray-300 cursor-pointer'
                }`}
              >
                <ChevronLeft className="text-[32px]" />
              </button>
              
              {/* Page Numbers */}
              <div className="flex items-center gap-2">
                {getPageNumbers().map((pageNumber) => (
                  <button
                    key={pageNumber}
                    onClick={() => handlePageClick(pageNumber)}
                    className={`h-10 w-10 flex items-center text-center justify-center rounded-2xl transition-colors ${
                      pageNumber === currentPage
                        ? 'bg-white text-gray-950'
                        : 'border-2 border-gray-600 text-white hover:bg-gray-700'
                    }`}
                  >
                    <Text variant={"body"} className={pageNumber === currentPage ? "text-gray-950" : "text-white"}>
                      {pageNumber}
                    </Text>
                  </button>
                ))}
              </div>
              
              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
                className={`${
                  currentPage === totalPages 
                    ? 'text-gray-500 cursor-not-allowed' 
                    : 'text-white hover:text-gray-300 cursor-pointer'
                }`}
              >
                <ChevronRight className="text-[32px]" />
              </button>
            </div>
            
            {/* Page Info */}
            <div className="flex items-center gap-2">
              <div className="h-10 w-10 border-gray-600 border-2 flex items-center text-center justify-center rounded-2xl">
                <Text variant={"body"} className="text-center text-white">
                  {currentPage}
                </Text>
              </div>
              <Text variant={"body"} className="text-center text-gray-400">
                / {totalPages}
              </Text>
            </div>
          </div>
        )}
      </div>
    </Section>
  );

  // return (
  //   <Section className=" pb-[130px]">
  //     <div className="flex items-start flex-col gap-14">
  //       <select
  //         // value={resultsFilter}
  //         // onChange={(e) => setResultsFilter(e.target.value)}
  //         className="bg-transparent z-10 min-w-[140px] border border-gray-600 rounded-lg px-4 py-2.5 text-sm font-normal text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
  //         style={{ fontSize: "14px", fontWeight: 400 }}
  //       >
  //         <option>Topic</option>
  //         <option>Drafts</option>
  //         <option>Favorites</option>
  //       </select>
  //       <div className="w-full  items-center justify-center pb-20 grid grid-cols-1 sm:grid-cols-2 gap-y-8 gap-x-6 md:grid-cols-3">
  //         {/* <BlogCard250227 />
  //         <BlogCard250215 />
  //         <BlogCard250202 />
  //         <BlogCard250127 />
  //         <BlogCard250117 />
  //         <BlogCard250105 />
  //         <BlogCard241227 />
  //         <BlogCard241215 />
  //         <BlogCard241130 />
  //         <BlogCard241115 />
  //         <BlogCard241109 />
  //         <BlogCard241101 /> */}
  //         {blogs.map((blog) => (
  //           <BlogCard key={blog.id} blog={blog} />
  //         ))}
  //       </div>

  //       <div className="flex w-full justify-between items-center">
  //         <div></div>
  //         <div className="flex items-center gap-8">
  //           <ChevronLeft className="text-[32px] text-white" />
  //           <div className="h-10 w-10 bg-white flex items-center text-center justify-center rounded-2xl">
  //             <Text variant={"body"} className="text-center text-gray-950">
  //               1
  //             </Text>
  //           </div>
  //           <ChevronRight className="text-[32px] text-white" />
  //         </div>
  //         <div className="flex items-center gap-2">
  //           <div className="h-10 w-10  border-border border-2 flex items-center text-center justify-center rounded-2xl">
  //             <Text variant={"body"} className="text-center text-white">
  //               1
  //             </Text>
  //           </div>{" "}
  //           <Text variant={"body"} className="text-center text-gray-400">
  //             / 1
  //           </Text>
  //         </div>{" "}
  //       </div>
  //     </div>
  //   </Section>
  // );
};

export default BlogList;
