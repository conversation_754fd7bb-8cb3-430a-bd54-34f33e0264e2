import Section from "@/components/layout/Section";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import { useState, useEffect, useCallback } from "react";

interface BlogHeroProps {
  searchTerm: string;
  setSearchTerm: (val: string) => void;
}

const Hero: React.FC<BlogHeroProps> = ({ searchTerm, setSearchTerm }) => {
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // Debounce function
  const debounce = useCallback((func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(null, args), delay);
    };
  }, []);

  // Debounced version of setSearchTerm
  const debouncedSetSearchTerm = useCallback(
    debounce((value: string) => {
      setSearchTerm(value);
    }, 300), // 300ms delay
    [setSearchTerm, debounce]
  );

  // Handle input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalSearchTerm(value);
    debouncedSetSearchTerm(value);
  };

  // Sync local state with prop when it changes externally
  useEffect(() => {
    setLocalSearchTerm(searchTerm);
  }, [searchTerm]);

  return (
    <Section className="py-[106px] z-[999]">
      <div className="flex gap-16 justify-end items-center ">
        {/* <div className="flex-1">
          <img className="max-w-[250px]" src="/png/about_logo.png" />
        </div> */}

        <div className="flex flex-col gap-6 justify-end items-end max-w-[460px]">
          <Text
            font="Inter"
            variant={"page_title"}
            className="bg-clip-text text-transparent bg-gradient-to-l from-white via-white to-gray-500"
          >
            MiragicAI Blog
          </Text>
          <Text font="Inter" variant={"sub_title"} className="text-right">
            We share expert advice, product updates, behind-the-scenes stories.
          </Text>
          <div className="flex items-center gap-5 z-20">
            <Input
              type="text"
              placeholder="Search for anything"
              value={localSearchTerm}
              onChange={handleSearchChange}
              className="min-w-[340px] flex-auto bg-white/10 border border-gray-700 rounded-lg px-4 h-[60px] text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
              required
            />
            <Button variant={"gradient"} className="min-w-[235px] h-[60px]">
              Search
            </Button>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default Hero;

// import Section from "@/components/layout/Section";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Text } from "@/components/ui/text";
// // import { useState } from "react";

// interface BlogHeroProps {
//   searchTerm: string,
//   setSearchTerm: (val: string) => void,
// }

// const Hero: React.FC<BlogHeroProps> = ({ searchTerm, setSearchTerm, }) => {
//   // const [searchTerm, setSearchTerm] = useState("");
//   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchTerm(e.target.value);
//   };
//   return (
//     <Section className="py-[106px] z-[999]">
//       <div className="flex gap-16 justify-end items-center ">
//         {/* <div className="flex-1">
//           <img className="max-w-[250px]" src="/png/about_logo.png" />
//         </div> */}

//         <div className="flex flex-col gap-6 justify-end items-end max-w-[460px]">
//           <Text
//             font="Inter"
//             variant={"page_title"}
//             className="bg-clip-text text-transparent bg-gradient-to-l from-white via-white to-gray-500"
//           >
//             MiragicAI Blog
//           </Text>
//           <Text font="Inter" variant={"sub_title"} className="text-right">
//             We share expert advice, product updates, behind-the-scenes stories.
//           </Text>
//           <div className="flex  items-center gap-5 z-20">
//             <Input
//               type="email"
//               placeholder="Search for anything"
//               value={searchTerm}
//               onChange={(e) => handleSearchChange(e)}
//               className="min-w-[340px] flex-auto bg-white/10 border border-gray-700 rounded-lg px-4 h-[60px] text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
//               required
//             />
//             <Button variant={"gradient"} className="min-w-[235px] h-[60px]">
//               Search
//             </Button>
//           </div>
//         </div>
//       </div>
//     </Section>
//   );
// };

// export default Hero;
