import LandingPageFAQ from "@/components/home/<USER>";
// import Client from "./components/Client";
import Hero from "./components/Hero";
import Services from "./components/Services";
import Section from "@/components/layout/Section";
import { Text } from "@/components/ui/text";
import Links from "./components/Links";

const Page = () => {
  return (
    <div className="bg-[#000000]">
      <Hero />
      <Services />
      {/* <Client /> */}

      <BottomArea />
      {/* Page */}
    </div>
  );
};

export const BottomArea = () => {
  return (
    <Section className="relative pb-24">
      <div className="z-10">
        <LandingPageFAQ />
      </div>
      <div className="flex z-10 pt-34 flex-col justify-center items-center gap-12">
        <div className="flex flex-col justify-center items-center">
          <Text
            variant={"page_title"}
            className="text-white text-[60px] z-10 text-center"
          >
            Increase the Range of Possibilities <br />
            by Integration the{" "}
            <span className="text-secondary">MiragicAI API</span>
            <br />
            with Your Product
          </Text>
        </div>
        <Links />
      </div>
      <img
        className="absolute z-1 top-0 left-0 opacity-25 w-full h-full object-cover pointer-events-none"
        src="/png/pattern_1.png"
        alt="Background pattern"
      />

      <img
        className="absolute bottom-0 z-1 left-[0.5px] w-1/2 h-auto object-contain pointer-events-none"
        src="/png/step_intoFut_shadow_1.png"
        alt="Shadow 1"
      />
      <img
        className="absolute bottom-0 z-1 right-[0.5px] transform scale-x-[-1] w-1/2 h-auto object-contain pointer-events-none"
        src="/png/step_intoFut_shadow_1.png"
        alt="Shadow 1"
      />
      {/* <img
        className="absolute bottom-0 left-1/2 w-1/2 h-auto object-contain"
        src="/png/step_intoFut_shadow_2.png"
        alt="Shadow 2"
      /> */}
    </Section>
  );
};

export default Page;
