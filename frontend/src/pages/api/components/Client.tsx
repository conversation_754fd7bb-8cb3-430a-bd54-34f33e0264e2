import Section from "@/components/layout/Section";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import { useRef } from "react";
import { Text } from "@/components/ui/text";
import SliderBtnArrow from "@/components/common/SliderBtnArrow";
const SLIDER_DATA = [
  {
    title: (
      <>
        It is a long established fact that a reader will be distracted by the
        readable content of a page when looking at its layout.{" "}
        <span className="text-[#AE84EF]">
          The point of using Lorem Ipsum is that it has a more-or-less normal
          distribution of letters.
        </span>{" "}
        As opposed to using 'Content here, content here', making it look like
        readable English.
      </>
    ),
    personName: "Hd<PERSON><PERSON>",
    designation: "CEO of Miragic AI",
  },
  {
    title: (
      <>
        It is a long established fact that a reader will be distracted by the
        readable content of a page when looking at its layout.{" "}
        <span className="text-[#AE84EF]">
          The point of using Lorem Ipsum is that it has a more-or-less normal
          distribution of letters.
        </span>{" "}
        As opposed to using 'Content here, content here', making it look like
        readable English.
      </>
    ),
    personName: "Hdfison <PERSON>ha",
    designation: "CEO of <PERSON>inTech",
  },
  {
    title: (
      <>
        It is a long established fact that a reader will be distracted by the
        readable content of a page when looking at its layout.{" "}
        <span className="text-[#AE84EF]">
          The point of using Lorem Ipsum is that it has a more-or-less normal
          distribution of letters.
        </span>{" "}
        As opposed to using 'Content here, content here', making it look like
        readable English.
      </>
    ),
    personName: "Hdfison Doha",
    designation: "CEO of ZinTech",
  },
];
const Client = () => {
  const swiperRef = useRef<SwiperType | null>(null);
  return (
    <Section>
      <div className="flex relative flex-col pb-20">
        <Text variant={"section_title"} className="text-white z-10 text-center">
          MIRAGIC AI Platform APIs
        </Text>

        <div className="relative pt-20">
          <SliderBtnArrow
            className="absolute top-0 right-0 z-10"
            swiperRef={swiperRef}
          />
          <Swiper
            modules={[Navigation]}
            onSwiper={(swiper: SwiperType) => {
              swiperRef.current = swiper;
            }}
            spaceBetween={20}
            slidesPerView={1.3}
            centeredSlides={false}
          >
            {SLIDER_DATA.map(({ title, designation, personName }, index) => (
              <SwiperSlide key={index}>
                <div className="grid grid-cols-[32px_auto] min-h-[480px] gap-3 bg-white/5 border border-white/25 text-white p-6 rounded-xl shadow-md mr-5">
                  <div className="flex-auto min-w-8 h-8 bg-[#AE84EF] text-4xl flex justify-center items-baseline rounded-full">
                    "
                  </div>
                  <div className="flex flex-col gap-8 justify-between h-full">
                    <p className="text-lg">{title}</p>
                    <div className="flex justify-between items-center">
                      <div className="mt-auto flex items-center space-x-3">
                        <img
                          src="/png/avatar1.png"
                          className="w-10 h-10 rounded-full"
                          alt="User"
                        />
                        <div>
                          <p className="font-semibold text-white text-lg">
                            {personName}
                          </p>
                          <p className="text-xs text-gray-400">{designation}</p>
                        </div>
                      </div>
                      <img
                        src="/icons/company_logo.svg"
                        className="w-12 h-auto"
                      />
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </Section>
  );
};

export default Client;
