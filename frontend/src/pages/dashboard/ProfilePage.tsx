import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import AuthService from "@/services/auth.service";
import { toast } from "sonner";
import { Spinner } from "@/components/ui/spinner";

const ProfilePage = () => {
  // State to store user data from API
  const [userData, setUserData] = useState({
    id: "",
    name: "",
    email: "",
    role: "USER" as "USER" | "ADMIN",
    plan: "Free" as "Free" | "Starter" | "Pro" | "Business",
    credit: {
      balance: 0,
    },
    profile: {
      id: "",
      userId: "",
      firstName: "",
      lastName: "",
      avatarUrl: "",
      company: "",
      jobTitle: "",
    },
    createdAt: "",
    updatedAt: "",
  });

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
    jobTitle: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
    avatarUrl: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [profileSuccess, setProfileSuccess] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  const [profileError, setProfileError] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AuthService.getCurrentUser();

        // Add missing profile fields if they don't exist
        const enhancedUser = {
          ...user,
          profile: {
            ...user.profile,
            company: user.profile?.company || "",
            jobTitle: user.profile?.jobTitle || "",
          },
          credits: user.credit?.balance || 0,
        };

        setUserData(enhancedUser);

        // Initialize form data
        setFormData({
          firstName: user.profile?.firstName || "",
          lastName: user.profile?.lastName || "",
          email: user.email,
          company: user.profile?.company || "",
          jobTitle: user.profile?.jobTitle || "",
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
          avatarUrl: user.profile?.avatarUrl || "",
        });
      } catch (error) {
        console.error("Failed to fetch user data:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsInitialLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setProfileError("");
    setProfileSuccess(false);
    setIsLoading(true);

    try {
      // Create profile update data
      const profileData = {
        profile: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          // Preserve existing profile fields
          id: userData.profile?.id,
          userId: userData.profile?.userId,
          avatarUrl: userData.profile?.avatarUrl,
          // Add updated fields
          company: formData.company,
          jobTitle: formData.jobTitle,
        },
        email: formData.email,
      };

      // Call the API to update the profile
      const updatedUser = await AuthService.updateProfile(profileData);

      // Add missing profile fields if they don't exist in the response
      const enhancedUser = {
        ...updatedUser,
        profile: {
          ...updatedUser.profile,
          company: updatedUser.profile?.company || formData.company,
          jobTitle: updatedUser.profile?.jobTitle || formData.jobTitle,
          avatarUrl: updatedUser.profile?.avatarUrl || formData.avatarUrl,
        },
        credit: {
          balance: updatedUser.credit?.balance || 0,
        },
      };

      // Update the user data in state
      setUserData(enhancedUser);

      setProfileSuccess(true);
      toast.success("Profile updated successfully");
    } catch (err) {
      console.error("Profile update error:", err);
      setProfileError("Failed to update profile. Please try again.");
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError("");
    setPasswordSuccess(false);

    // Basic validation
    if (formData.newPassword.length < 8) {
      setPasswordError("New password must be at least 8 characters long");
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setPasswordError("Passwords do not match");
      return;
    }

    setIsLoading(true);

    try {
      // In a real implementation, we would call the API to update the password
      // For now, we'll simulate this since we don't have a password update endpoint
      // This should be replaced with a real API call in production

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Reset password fields
      setFormData((prev) => ({
        ...prev,
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      }));

      setPasswordSuccess(true);
      toast.success("Password updated successfully");
    } catch (err) {
      console.error("Password update error:", err);
      setPasswordError("Failed to update password. Please try again.");
      toast.error("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Profile Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      {isInitialLoading && (
        <div className="flex justify-center items-center py-12">
          <Spinner className="h-8 w-8" />
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Sidebar */}
        <div className="md:col-span-1">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center">
                <div className="relative mb-4">
                  <img
                    src={
                      userData.profile?.avatarUrl ||
                      `https://ui-avatars.com/api/?name=${userData.profile?.firstName}+${userData.profile?.lastName}&background=random`
                    }
                    alt={`${userData.profile?.firstName} ${userData.profile?.lastName}`}
                    className="h-24 w-24 rounded-full object-cover border"
                  />
                  <Button
                    size="icon"
                    variant="outline"
                    className="absolute bottom-0 right-0 rounded-full h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="M4 16l4.586-4.586a2 2 0 0 1 2.828 0L16 16" />
                      <path d="M4 16v1a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-1" />
                      <circle cx="12" cy="9" r="2" />
                    </svg>
                  </Button>
                </div>
                <h3 className="text-lg font-medium">{`${
                  userData.profile?.firstName || ""
                } ${userData.profile?.lastName || ""}`}</h3>
                <p className="text-sm text-muted-foreground">
                  {userData.email}
                </p>
                <div className="mt-2 flex flex-col items-center">
                  <span className="text-sm font-medium">
                    Plan: {userData.plan}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    Credits: {userData.credit.balance}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="md:col-span-2 space-y-6">
          {/* Profile Information */}
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information
              </CardDescription>
            </CardHeader>
            <CardContent>
              {profileSuccess && (
                <div className="bg-green-100 text-green-800 text-sm p-3 rounded-md mb-4">
                  Profile updated successfully.
                </div>
              )}
              {profileError && (
                <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md mb-4">
                  {profileError}
                </div>
              )}
              <form onSubmit={handleProfileSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="firstName" className="text-sm font-medium">
                      First Name
                    </label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      disabled={isLoading}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="lastName" className="text-sm font-medium">
                      Last Name
                    </label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      disabled={isLoading}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">
                    Email Address
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    disabled={isLoading}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="company" className="text-sm font-medium">
                    Company
                  </label>
                  <Input
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleChange}
                    disabled={isLoading}
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="jobTitle" className="text-sm font-medium">
                    Job Title
                  </label>
                  <Input
                    id="jobTitle"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleChange}
                    disabled={isLoading}
                  />
                </div>
                <div className="flex justify-end">
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Saving..." : "Save Changes"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Password */}
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <CardContent>
              {passwordSuccess && (
                <div className="bg-green-100 text-green-800 text-sm p-3 rounded-md mb-4">
                  Password updated successfully.
                </div>
              )}
              {passwordError && (
                <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md mb-4">
                  {passwordError}
                </div>
              )}
              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                <div className="space-y-2">
                  <label
                    htmlFor="currentPassword"
                    className="text-sm font-medium"
                  >
                    Current Password
                  </label>
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={formData.currentPassword}
                    onChange={handleChange}
                    disabled={isLoading}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="newPassword" className="text-sm font-medium">
                    New Password
                  </label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={formData.newPassword}
                    onChange={handleChange}
                    disabled={isLoading}
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Password must be at least 8 characters long
                  </p>
                </div>
                <div className="space-y-2">
                  <label
                    htmlFor="confirmPassword"
                    className="text-sm font-medium"
                  >
                    Confirm New Password
                  </label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    disabled={isLoading}
                    required
                  />
                </div>
                <div className="flex justify-end">
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Updating..." : "Update Password"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="border-destructive/50">
            <CardHeader>
              <CardTitle className="text-destructive">Danger Zone</CardTitle>
              <CardDescription>
                Irreversible actions for your account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Delete Account</h3>
                  <p className="text-sm text-muted-foreground">
                    Permanently delete your account and all associated data
                  </p>
                </div>
                <Button variant="destructive">Delete Account</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
