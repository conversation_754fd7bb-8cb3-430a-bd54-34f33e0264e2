import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import axios from "axios";

const VirtualTryOnPage = () => {
  // Get user context for credits display
  const { user } = useApp();
  const [activeTab, setActiveTab] = useState("single");
  const [isLoading, setIsLoading] = useState(false);
  const [garmentImage, setGarmentImage] = useState<File | null>(null);
  const [humanImage, setHumanImage] = useState<File | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);

  // Define the type for virtual try-on items
  interface VirtualTryOnItem {
    id: string;
    userId: string;
    garmentImagePath: string;
    humanImagePath: string;
    resultImagePath: string;
    status: string;
    errorMessage: string | null;
    createdAt: string;
    updatedAt: string;
  }

  const [recentItems, setRecentItems] = useState<VirtualTryOnItem[]>([]);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const garmentInputRef = useRef<HTMLInputElement>(null);
  const humanInputRef = useRef<HTMLInputElement>(null);

  // State for image previews
  const [garmentPreview, setGarmentPreview] = useState<string | null>(null);
  const [customModelPreview, setCustomModelPreview] = useState<string | null>(
    null
  );

  console.log("recentItems", recentItems);

  // Model images state
  const modelImages = [
    "/models/model1.jpg",
    "/models/model2.jpg",
    "/models/model3.jpg",
    "/models/model4.jpg",
  ];

  // Handle file selection for garment image
  const handleGarmentImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setGarmentImage(file);

      // Create a preview URL for the uploaded garment
      const imageUrl = URL.createObjectURL(file);
      setGarmentPreview(imageUrl);
      console.log("Garment image selected:", file.name, imageUrl);
    }
  };

  // Handle human/model image change
  const handleHumanImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setHumanImage(file);

      // Clear any previously selected model
      setSelectedModel(null);

      // Create a preview URL for the uploaded image
      const imageUrl = URL.createObjectURL(file);
      setCustomModelPreview(imageUrl);
      console.log("Human image selected:", file.name, imageUrl);
    }
  };

  // Handle drag and drop for garment image
  const handleGarmentDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      setGarmentImage(file);

      // Create a preview URL for the uploaded garment
      const imageUrl = URL.createObjectURL(file);
      setGarmentPreview(imageUrl);
    }
  };

  // Handle drag over for garment image
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  // Handle model selection
  const handleModelSelect = (modelPath: string) => {
    setSelectedModel(modelPath);
    setHumanImage(null); // Clear any uploaded human image when selecting a model
    setCustomModelPreview(null); // Clear custom model preview
  };

  // Handle selecting a garment from recent items
  const handleSelectRecentGarment = async (item: VirtualTryOnItem) => {
    try {
      // Fetch the garment image from the server
      const garmentUrl = `${import.meta.env.VITE_API_URL}${
        item.garmentImagePath
      }`;
      const response = await fetch(garmentUrl);
      const blob = await response.blob();

      // Create a File object from the blob
      const filename = item.garmentImagePath.split("/").pop() || "garment.jpg";
      const file = new File([blob], filename, {
        type: blob.type || "image/jpeg",
      });

      // Set the garment image and preview
      setGarmentImage(file);
      setGarmentPreview(garmentUrl);

      toast.success("Garment selected from history");
    } catch (error) {
      console.error("Error selecting recent garment:", error);
      toast.error("Failed to load garment from history");
    }
  };

  // Handle selecting a model from recent items
  const handleSelectRecentModel = async (item: VirtualTryOnItem) => {
    try {
      // Fetch the human image from the server
      const humanUrl = `${import.meta.env.VITE_API_URL}${item.humanImagePath}`;

      // Check if it's a predefined model
      if (item.humanImagePath.startsWith("/models/")) {
        // It's a predefined model, just set the selected model
        setSelectedModel(item.humanImagePath);
        setHumanImage(null);
        setCustomModelPreview(null);
      } else {
        // It's a custom uploaded model, fetch it
        const response = await fetch(humanUrl);
        const blob = await response.blob();

        // Create a File object from the blob
        const filename = item.humanImagePath.split("/").pop() || "model.jpg";
        const file = new File([blob], filename, {
          type: blob.type || "image/jpeg",
        });

        // Set the human image and preview
        setHumanImage(file);
        setSelectedModel(null);
        setCustomModelPreview(humanUrl);
      }

      toast.success("Model selected from history");
    } catch (error) {
      console.error("Error selecting recent model:", error);
      toast.error("Failed to load model from history");
    }
  };

  // Fetch recent items on component mount
  useEffect(() => {
    fetchRecentItems();
  }, []);

  // Handle generate button click
  const handleGenerate = async () => {
    if (!garmentImage) {
      toast.error("Please select a garment image");
      return;
    }

    if (!humanImage && !selectedModel) {
      toast.error("Please select a human image or choose a model");
      return;
    }

    try {
      setIsLoading(true);

      // Create form data
      const formData = new FormData();
      formData.append("garm_image", garmentImage);

      // Use selected model or uploaded human image
      if (selectedModel) {
        // For selected model, we need to fetch it as a blob first
        try {
          const modelResponse = await fetch(selectedModel);
          const modelBlob = await modelResponse.blob();
          const modelFile = new File([modelBlob], `model-${Date.now()}.jpg`, {
            type: "image/jpeg",
          });
          formData.append("human_image", modelFile);
          console.log("Using model image:", selectedModel);
        } catch (error) {
          console.error("Error fetching model image:", error);
          toast.error("Failed to load model image");
          setIsLoading(false);
          return;
        }
      } else if (humanImage) {
        formData.append("human_image", humanImage);
        console.log("Using uploaded human image:", humanImage.name);
      }

      // Add empty prompt
      formData.append("prompt", "");

      console.log("Uploading images...");

      // Upload images first
      const uploadResponse = await axios.post(
        `${import.meta.env.VITE_API_URL}/upload/multiple-fields`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      console.log("Upload response:", uploadResponse.data);

      if (!uploadResponse.data.success) {
        throw new Error("Failed to upload images");
      }

      // Get the uploaded image paths
      const { garmentImagePath, humanImagePath } = uploadResponse.data.data;

      console.log("Processing virtual try-on with paths:", {
        garmentImagePath,
        humanImagePath,
      });

      // Process virtual try-on
      const tryOnResponse = await axios.post(
        `${import.meta.env.VITE_API_URL}/virtual-try-on`,
        {
          garmentImagePath,
          humanImagePath,
          prompt: "",
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      console.log("Try-on response:", tryOnResponse.data);

      if (!tryOnResponse.data.success) {
        throw new Error("Failed to process virtual try-on");
      }

      // Set the result image
      const resultPath = tryOnResponse.data.data.resultImagePath;
      setResultImage(`${import.meta.env.VITE_API_URL}${resultPath}`);

      // Fetch recent items after successful generation
      fetchRecentItems();
    } catch (error) {
      console.error("Error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An error occurred during virtual try-on"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch recent virtual try-on items
  const fetchRecentItems = async () => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/virtual-try-on/history`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (response.data.success) {
        setRecentItems(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching recent items:", error);
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Virtual Try-On</h1>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            Credits: {user?.credit?.balance || 0}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-6">
          <Card>
            <CardContent className="p-6 space-y-6">
              <Tabs
                defaultValue="single"
                value={activeTab}
                onValueChange={setActiveTab}
              >
                <TabsList className="grid grid-cols-2">
                  <TabsTrigger value="single">Single Garment</TabsTrigger>
                  <TabsTrigger value="outfit">Full Outfit</TabsTrigger>
                </TabsList>

                <TabsContent value="single" className="space-y-6">
                  <div className="space-y-4">
                    <h2 className="text-xl font-semibold">Upload garment</h2>
                    <p className="text-sm text-muted-foreground">
                      Upload the garment you want to try on
                    </p>
                    <div className="grid grid-cols-1 gap-2">
                      {garmentPreview ? (
                        <div
                          className="aspect-square border rounded-md overflow-hidden relative cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => garmentInputRef.current?.click()}
                          onDragOver={handleDragOver}
                          onDrop={handleGarmentDrop}
                        >
                          <img
                            src={garmentPreview}
                            alt="Garment preview"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-1 text-xs text-center">
                            Click to change
                          </div>
                          <div className="absolute top-0 right-0 bg-primary text-white p-1 text-xs">
                            Your Garment
                          </div>
                          <input
                            type="file"
                            ref={garmentInputRef}
                            onChange={handleGarmentImageChange}
                            accept="image/*"
                            className="hidden"
                          />
                        </div>
                      ) : (
                        <div
                          className="aspect-square border-2 border-dashed rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-muted/50 transition-colors p-2"
                          onClick={() => garmentInputRef.current?.click()}
                          onDragOver={handleDragOver}
                          onDrop={handleGarmentDrop}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="w-8 h-8 text-muted-foreground"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M12 4.5v15m7.5-7.5h-15"
                            />
                          </svg>
                          <span className="text-xs text-muted-foreground mt-1">
                            Upload garment
                          </span>
                          <input
                            type="file"
                            ref={garmentInputRef}
                            onChange={handleGarmentImageChange}
                            accept="image/*"
                            className="hidden"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="outfit">
                  <div className="text-center p-6 border rounded-md bg-muted/10">
                    <p className="text-muted-foreground">
                      Coming soon! This feature will allow you to try on
                      complete outfits.
                    </p>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Select a model</h2>
                <p className="text-sm text-muted-foreground">
                  Select our model to try on
                </p>
                <div className="grid grid-cols-4 gap-2">
                  {/* Predefined model images */}
                  {modelImages.map((model, index) => (
                    <div
                      key={index}
                      className={`aspect-square border rounded-md overflow-hidden cursor-pointer hover:opacity-80 transition-opacity ${
                        selectedModel === model ? "ring-2 ring-primary" : ""
                      }`}
                      onClick={() => handleModelSelect(model)}
                    >
                      <img
                        src={model}
                        alt={`Model ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}

                  {/* Custom model upload button */}
                  {customModelPreview ? (
                    <div
                      className="aspect-square border rounded-md overflow-hidden relative cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => humanInputRef.current?.click()}
                    >
                      <img
                        src={customModelPreview}
                        alt="Your model"
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-0 right-0 bg-primary text-white p-1 text-xs">
                        Your Model
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-1 text-xs text-center">
                        Click to change
                      </div>
                      <input
                        type="file"
                        ref={humanInputRef}
                        onChange={handleHumanImageChange}
                        accept="image/*"
                        className="hidden"
                      />
                    </div>
                  ) : (
                    <div
                      className="aspect-square border-2 border-dashed rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-muted/50 transition-colors p-2"
                      onClick={() => humanInputRef.current?.click()}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-8 h-8 text-muted-foreground"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M12 4.5v15m7.5-7.5h-15"
                        />
                      </svg>
                      <span className="text-xs text-muted-foreground mt-1">
                        Upload your model
                      </span>
                      <input
                        type="file"
                        ref={humanInputRef}
                        onChange={handleHumanImageChange}
                        accept="image/*"
                        className="hidden"
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Recent items</h2>
                <p className="text-sm text-muted-foreground">
                  Click on an item to use its garment or model
                </p>
                <div className="grid grid-cols-4 gap-2">
                  {recentItems.slice(0, 8).map((item) => (
                    <div
                      key={item.id}
                      className="aspect-square border rounded-md overflow-hidden cursor-pointer hover:opacity-80 transition-opacity relative group"
                    >
                      <img
                        src={`${import.meta.env.VITE_API_URL}${
                          item.resultImagePath
                        }`}
                        alt="Recent item"
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/70 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col justify-center items-center gap-2 p-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full text-xs"
                          onClick={() => handleSelectRecentGarment(item)}
                        >
                          Use Garment
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full text-xs"
                          onClick={() => handleSelectRecentModel(item)}
                        >
                          Use Model
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Button
                className="w-full py-6 text-lg"
                onClick={handleGenerate}
                disabled={
                  isLoading || !garmentImage || (!humanImage && !selectedModel)
                }
              >
                {isLoading ? (
                  <>
                    <Spinner className="mr-2" /> Processing...
                  </>
                ) : (
                  "Generate Try-On"
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Result Section */}
        <div className="space-y-6">
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-4">Result</h2>
              {resultImage ? (
                <div className="space-y-4">
                  <div className="border rounded-md overflow-hidden">
                    <img
                      src={resultImage}
                      alt="Virtual try-on result"
                      className="w-full h-auto"
                    />
                  </div>
                  <div className="flex flex-col gap-2">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        const link = document.createElement("a");
                        link.href = resultImage;
                        link.download = "virtual-try-on-result.jpg";
                        link.click();
                      }}
                    >
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        navigator.clipboard.writeText(resultImage);
                        toast.success("Image URL copied to clipboard");
                      }}
                    >
                      Copy URL
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-12 border rounded-md bg-muted/10">
                  <p className="text-muted-foreground text-center">
                    Your virtual try-on result will appear here after
                    generation.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-4">How it works</h2>
              <ol className="list-decimal list-inside space-y-2 text-muted-foreground">
                <li>Upload a garment image you want to try on</li>
                <li>Select a model or upload your own image</li>
                <li>Click "Generate Try-On" to see the result</li>
                <li>Download or share your virtual try-on image</li>
              </ol>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default VirtualTryOnPage;
