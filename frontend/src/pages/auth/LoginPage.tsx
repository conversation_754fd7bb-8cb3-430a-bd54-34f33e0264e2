import { useState } from "react";
import { AppleIcon, GoogleIcon } from "@/lib/icons";
import { Mail, Lock, Eye, EyeOff } from "lucide-react";
import { Link, useNavigate, useLocation } from "react-router-dom";

import { useApp } from "@/contexts/useApp";
import { toast } from "sonner";
const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const navigate = useNavigate();
  const location = useLocation();
  const { login, isLoading } = useApp();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [error, setError] = useState("");

  // Get the redirect path from location state or default to dashboard
  const from = location.state?.from?.pathname || "/dashboard";

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    try {
      // Use the login function from AppContext
      const response = await login(formData.email, formData.password);

      console.log(response);
      if (response?.user?.id) {
        navigate(from, { replace: true });
        toast.success("Welcome back!");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("Invalid email or password. Please try again.");
    }
  };
  return (
    <div className="min-h-screen bg-[#1A1A1A] flex flex-col md:flex-row">
      {/* Left Section: Login UI (Visible on all screen sizes) */}
      <div className="flex-1 flex flex-col xl:ml-10 justify-center p-6">
        <div className="max-w-xl w-full">
          {/* Logo */}
          <div className="flex items-center space-x-2 mb-8">
            <Link to="/">
              <img src="/png/new_miragic_logo.png" className="w-[170px]" />
            </Link>
          </div>
          <div className="ml-10">
            {/* Title and Subtitle */}
            <h1 className="text-4xl text-white font-bold mb-4">
              Let's get <span className="text-[#9855FF]">creative!</span>
            </h1>
            <p className="text-gray-500 text-lg mb-12">
              Log in to start creating magic
            </p>

            {/* Form */}
            {error && (
              <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md mb-4">
                {error}
              </div>
            )}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Email Input with Mail Icon */}
              <div className="relative">
                <Mail className="w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="email"
                  placeholder="Email"
                  className="w-full pl-10 p-3 bg-[#2A2A2A] text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  value={formData.email}
                  onChange={handleChange}
                  name="email"
                  disabled={isLoading}
                />
              </div>

              {/* Password Input with Lock and Eye Icons */}
              <div className="relative">
                <Lock className="w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type={showPassword ? "text" : "password"}
                  placeholder="Password"
                  className="w-full pl-10 pr-10 p-3 bg-[#2A2A2A] text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  value={formData.password}
                  onChange={handleChange}
                  name="password"
                  disabled={isLoading}
                />
                <div
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeOff className="text-gray-400 w-6" />
                  ) : (
                    <Eye className="text-gray-400 w-6" />
                  )}
                </div>
              </div>

              {/* Remember Me and Forgot Password */}
              <div className="flex justify-between items-center my-10">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-purple-500 bg-[#2A2A2A] border-gray-600 rounded focus:ring-purple-500"
                  />
                  <span className="text-gray-400">Remember me</span>
                </label>
                <Link
                  to="/auth/forgot-password"
                  className="text-gray-400 hover:text-purple-400"
                >
                  Forgot Password?
                </Link>
              </div>

              {/* Log In Button */}
              <button
                type="submit"
                className="w-full py-3 bg-[#6B46C1] text-white rounded-md hover:bg-purple-700 transition"
              >
                {isLoading ? "Logging in..." : "Log in"}
              </button>

              {/* Or Continue With Section */}
              <div className="relative flex items-center my-8">
                <div className="flex-grow border-t border-gray-600"></div>
                <span className="flex-shrink mx-4 text-gray-400 text-sm">
                  or continue with
                </span>
                <div className="flex-grow border-t border-gray-600"></div>
              </div>
            </form>

            {/* Social Login Buttons */}
            <div className="flex space-x-4 mt-6">
              <button className="flex-1 flex items-center justify-center gap-3 cursor-pointer py-3 bg-[#2A2A2A] border border-gray-600 rounded-md hover:bg-gray-700 transition">
                <GoogleIcon />
                <span className="text-white">Google Account</span>
              </button>
              <button className="flex-1 flex items-center justify-center gap-3 cursor-pointer py-3 bg-[#2A2A2A] border border-gray-600 rounded-md hover:bg-gray-700 transition">
                <AppleIcon />
                <span className="text-white">Apple Account</span>
              </button>
            </div>

            {/* Sign Up Link */}
          </div>
        </div>
        <p className="text-gray-400 mt-16">
          Don't have an account?{" "}
          <Link to="/auth/register" className="text-purple-400 hover:underline">
            Sign Up
          </Link>
        </p>
      </div>

      {/* Right Section: Image (Visible on md screens and above) */}
      <div className="hidden md:block flex-1">
        <img
          src="/png/login_right_image.png"
          alt="Decorative background"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
};

export default LoginPage;
