import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

import { useApp } from "@/contexts/useApp";
import { toast } from "sonner";
import { User, Mail, Lock, Eye, EyeOff } from "lucide-react";
import type { AxiosError } from "axios";

const RegisterPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showRepeatPassword, setShowRepeatPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleRepeatPasswordVisibility = () => {
    setShowRepeatPassword(!showRepeatPassword);
  };

  const navigate = useNavigate();
  const { register, isLoading } = useApp();
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [error, setError] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Basic validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (formData.password.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    try {
      // Use the register function from AppContext
      const registerResponse = await register(
        formData.firstName,
        formData.lastName,
        formData.email,
        formData.password
      );
      if (registerResponse.user.emailVerified) {
        // Redirect to dashboard
        navigate("/dashboard");
        toast.success("Account created successfully! Welcome to Miragic-AI.");
      }
    } catch (error) {
      console.error("Registration error:", error);
      const axiosError = error as AxiosError<{
        error?: {
          message?: string;
        };
      }>;
      setError(
        axiosError.response?.data?.error?.message ||
          "Registration failed. Please try again."
      );
    }
  };

  return (
    <div className="min-h-screen bg-[#1A1A1A] flex flex-col md:flex-row">
      {/* Left Section: Register UI (Visible on all screen sizes) */}
      <div className="md:flex-[2] flex flex-col xl:ml-10 justify-center p-6">
        <div className="max-w-2xl w-full">
          {/* Logo */}
          <div className="flex items-center space-x-2 mb-8">
            <Link to="/">
              <img src="/png/new_miragic_logo.png" className="w-[170px]" />
            </Link>
          </div>
          <div className="ml-10">
            {/* Title and Subtitle */}
            <h1 className="text-3xl text-white font-inter mt-20 mb-14">
              Connect with your team and bring your creative ideas to life.
            </h1>
            {error && (
              <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md mb-4">
                {error}
              </div>
            )}
            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Email Input with Mail Icon (Full Width) */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-gray-400 text-sm mb-2"
                >
                  Email
                </label>
                <div className="relative">
                  <Mail className="w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    id="email"
                    type="email"
                    placeholder="Email"
                    className="w-full pl-10 p-3 bg-[#2A2A2A] text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* First Name and Last Name Inputs (Side by Side on md+) */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* First Name Input with User Icon */}
                <div>
                  <label
                    htmlFor="firstName"
                    className="block text-gray-400 text-sm mb-2"
                  >
                    First Name
                  </label>
                  <div className="relative">
                    <User className="w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      id="firstName"
                      type="text"
                      placeholder="First Name"
                      className="w-full pl-10 p-3 bg-[#2A2A2A] text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Last Name Input with User Icon */}
                <div>
                  <label
                    htmlFor="lastName"
                    className="block text-gray-400 text-sm mb-2"
                  >
                    Last Name
                  </label>
                  <div className="relative">
                    <User className="w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      id="lastName"
                      type="text"
                      placeholder="Last Name"
                      className="w-full pl-10 p-3 bg-[#2A2A2A] text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      disabled={isLoading}
                    />
                  </div>
                </div>
              </div>

              {/* Password and Repeat Password Inputs (Side by Side on md+) */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Password Input with Lock and Eye Icons */}
                <div>
                  <label
                    htmlFor="password"
                    className="block text-gray-400 text-sm mb-2"
                  >
                    Password
                  </label>
                  <div className="relative">
                    <Lock className="w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      className="w-full pl-10 pr-10 p-3 bg-[#2A2A2A] text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      disabled={isLoading}
                    />
                    <div
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={togglePasswordVisibility}
                    >
                      {showPassword ? (
                        <EyeOff className="text-gray-400 w-6" />
                      ) : (
                        <Eye className="text-gray-400 w-6" />
                      )}
                    </div>
                  </div>
                </div>

                {/* Repeat Password Input with Lock and Eye Icons */}
                <div>
                  <label
                    htmlFor="repeatPassword"
                    className="block text-gray-400 text-sm mb-2"
                  >
                    Repeat Password
                  </label>
                  <div className="relative">
                    <Lock className="w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      id="repeatPassword"
                      type={showRepeatPassword ? "text" : "password"}
                      placeholder="Repeat Password"
                      className="w-full pl-10 pr-10 p-3 bg-[#2A2A2A] text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      disabled={isLoading}
                    />
                    <div
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={toggleRepeatPasswordVisibility}
                    >
                      {showRepeatPassword ? (
                        <EyeOff className="text-gray-400 w-6" />
                      ) : (
                        <Eye className="text-gray-400 w-6" />
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Terms and Conditions Checkbox */}
              <div className="flex items-center space-x-2 mt-10">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    required
                    className="h-4 w-4 text-purple-500 bg-[#2A2A2A] border-gray-600 rounded focus:ring-purple-500"
                  />
                  <span className="text-gray-400">
                    I agree with{" "}
                    <span
                      onClick={() => navigate("/terms")}
                      className="text-[#9855FF] cursor-pointer"
                    >
                      Terms and conditions
                    </span>
                  </span>
                </label>
              </div>

              {/* Sign Up Button */}
              <button
                type="submit"
                className="w-full py-3 cursor-pointer bg-[#6B46C1] text-white rounded-md hover:bg-purple-700 transition mt-6"
              >
                {isLoading ? "Loading..." : "Sign Up"}
              </button>

              {/* Or Continue With Section */}
              {/* <div className="relative flex items-center my-8">
                <div className="flex-grow border-t border-gray-600"></div>
                <span className="flex-shrink mx-4 text-gray-400 text-sm">
                  or continue with
                </span>
                <div className="flex-grow border-t border-gray-600"></div>
              </div> */}
            </form>

            {/* Social Login Buttons */}
            {/* <div className="flex space-x-4 mt-6">
              <button className="flex-1 flex items-center justify-center gap-3 cursor-pointer py-3 bg-[#2A2A2A] border border-gray-600 rounded-md hover:bg-gray-700 transition">
                <GoogleIcon />
                <span className="text-white">Google Account</span>
              </button>
              <button className="flex-1 flex items-center justify-center gap-3 cursor-pointer py-3 bg-[#2A2A2A] border border-gray-600 rounded-md hover:bg-gray-700 transition">
                <AppleIcon />
                <span className="text-white">Apple Account</span>
              </button>
            </div> */}
          </div>
        </div>
        <p className="text-gray-400 mt-16">
          Already have an account?{" "}
          <Link to="/auth/login" className="text-purple-400 hover:underline">
            Log in
          </Link>
        </p>
      </div>

      {/* Right Section: Image (Visible on md screens and above) */}
      <div className="hidden md:block md:flex-[1]">
        <img
          src="/png/login_right_image.png"
          alt="Decorative background"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
};

export default RegisterPage;

// import { useState } from "react";
// import { Link, useNavigate } from "react-router-dom";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardFooter,
//   CardHeader,
//   CardTitle,
// } from "@/components/ui/card";
// import { useApp } from "@/contexts/useApp";
// import { toast } from "sonner";

// const RegisterPage = () => {
//   const navigate = useNavigate();
//   const { register, isLoading } = useApp();
//   const [formData, setFormData] = useState({
//     firstName: "",
//     lastName: "",
//     email: "",
//     password: "",
//     confirmPassword: "",
//   });
//   const [error, setError] = useState("");

//   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const { name, value } = e.target;
//     setFormData((prev) => ({ ...prev, [name]: value }));
//   };

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setError("");

//     // Basic validation
//     if (formData.password !== formData.confirmPassword) {
//       setError("Passwords do not match");
//       return;
//     }

//     if (formData.password.length < 8) {
//       setError("Password must be at least 8 characters long");
//       return;
//     }

//     try {
//       // Use the register function from AppContext
//       await register(
//         formData.firstName,
//         formData.lastName,
//         formData.email,
//         formData.password
//       );

//       // Redirect to dashboard
//       navigate("/dashboard");
//       toast.success("Account created successfully! Welcome to Miragic-AI.");
//     } catch (error) {
//       console.error("Registration error:", error);
//       setError("Registration failed. Please try again.");
//     }
//   };

//   return (
//     <Card className="w-full shadow-md">
//       <CardHeader className="space-y-1">
//         <CardTitle className="text-2xl font-bold text-center">
//           Create an account
//         </CardTitle>
//         <CardDescription className="text-center">
//           Enter your details to create your Miragic-AI account
//         </CardDescription>
//       </CardHeader>
//       <CardContent>
//         {error && (
//           <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md mb-4">
//             {error}
//           </div>
//         )}
//         <form onSubmit={handleSubmit} className="space-y-4">
//           <div className="space-y-2">
//             <label htmlFor="firstName" className="text-sm font-medium">
//               First Name
//             </label>
//             <Input
//               id="firstName"
//               name="firstName"
//               type="text"
//               placeholder="John Doe"
//               required
//               value={formData.firstName}
//               onChange={handleChange}
//               disabled={isLoading}
//             />
//           </div>
//           <div className="space-y-2">
//             <label htmlFor="lastName" className="text-sm font-medium">
//               Last Name
//             </label>
//             <Input
//               id="lastName"
//               name="lastName"
//               type="text"
//               placeholder="John Doe"
//               required
//               value={formData.lastName}
//               onChange={handleChange}
//               disabled={isLoading}
//             />
//           </div>
//           <div className="space-y-2">
//             <label htmlFor="email" className="text-sm font-medium">
//               Email
//             </label>
//             <Input
//               id="email"
//               name="email"
//               type="email"
//               placeholder="<EMAIL>"
//               required
//               value={formData.email}
//               onChange={handleChange}
//               disabled={isLoading}
//             />
//           </div>
//           <div className="space-y-2">
//             <label htmlFor="password" className="text-sm font-medium">
//               Password
//             </label>
//             <Input
//               id="password"
//               name="password"
//               type="password"
//               placeholder="••••••••"
//               required
//               value={formData.password}
//               onChange={handleChange}
//               disabled={isLoading}
//             />
//           </div>
//           <div className="space-y-2">
//             <label htmlFor="confirmPassword" className="text-sm font-medium">
//               Confirm Password
//             </label>
//             <Input
//               id="confirmPassword"
//               name="confirmPassword"
//               type="password"
//               placeholder="••••••••"
//               required
//               value={formData.confirmPassword}
//               onChange={handleChange}
//               disabled={isLoading}
//             />
//           </div>
//           <div className="flex items-center space-x-2">
//             <input
//               type="checkbox"
//               id="terms"
//               className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
//               required
//             />
//             <label htmlFor="terms" className="text-sm text-muted-foreground">
//               I agree to the{" "}
//               <Link to="/terms" className="text-primary hover:underline">
//                 Terms of Service
//               </Link>{" "}
//               and{" "}
//               <Link to="/privacy" className="text-primary hover:underline">
//                 Privacy Policy
//               </Link>
//             </label>
//           </div>
//           <Button type="submit" className="w-full" disabled={isLoading}>
//             {isLoading ? "Creating account..." : "Create account"}
//           </Button>
//         </form>

//         <div className="relative my-6">
//           <div className="absolute inset-0 flex items-center">
//             <div className="w-full border-t"></div>
//           </div>
//           <div className="relative flex justify-center text-xs uppercase">
//             <span className="bg-background px-2 text-muted-foreground">
//               Or continue with
//             </span>
//           </div>
//         </div>

//         <div className="grid grid-cols-2 gap-4">
//           <Button variant="outline" type="button" disabled={isLoading}>
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               viewBox="0 0 24 24"
//               className="h-5 w-5 mr-2"
//             >
//               <path
//                 d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
//                 fill="#4285F4"
//               />
//               <path
//                 d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
//                 fill="#34A853"
//               />
//               <path
//                 d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
//                 fill="#FBBC05"
//               />
//               <path
//                 d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
//                 fill="#EA4335"
//               />
//             </svg>
//             Google
//           </Button>
//           <Button variant="outline" type="button" disabled={isLoading}>
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               viewBox="0 0 24 24"
//               className="h-5 w-5 mr-2 fill-current"
//             >
//               <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
//             </svg>
//             Facebook
//           </Button>
//         </div>
//       </CardContent>
//       <CardFooter className="flex justify-center">
//         <p className="text-sm text-muted-foreground">
//           Already have an account?{" "}
//           <Link to="/auth/login" className="text-primary hover:underline">
//             Log in
//           </Link>
//         </p>
//       </CardFooter>
//     </Card>
//   );
// };

// export default RegisterPage;
