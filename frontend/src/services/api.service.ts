import axios from "axios";
import type { AxiosRequestConfig, AxiosResponse } from "axios";

// Generic type for request data
type RequestData = Record<string, unknown> | FormData;

// Generic type for API response with nested data structure
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

// Get API URL from environment variables
const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000/api/v1";
export const AI_URL = import.meta.env.VITE_AI_URL || "http://localhost:8000";

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

export const aiClient = axios.create({
  baseURL: AI_URL,
});

// Add request interceptor to include auth token in all requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && error.response.status === 401) {
      // Clear local storage and redirect to login
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/auth/login";
    }
    return Promise.reject(error);
  }
);

// Generic API service methods
const ApiService = {
  /**
   * Perform a GET request
   */
  get<T = unknown>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return apiClient.get<T>(url, config);
  },

  /**
   * Perform a POST request
   */
  post<T = unknown, D extends RequestData = RequestData>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return apiClient.post<T>(url, data, config);
  },

  /**
   * Perform a PUT request
   */
  put<T = unknown, D extends RequestData = RequestData>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return apiClient.put<T>(url, data, config);
  },

  /**
   * Perform a PATCH request
   */
  patch<T = unknown, D extends RequestData = RequestData>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return apiClient.patch<T>(url, data, config);
  },

  /**
   * Perform a DELETE request
   */
  delete<T = unknown>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return apiClient.delete<T>(url, config);
  },
};

export default ApiService;
