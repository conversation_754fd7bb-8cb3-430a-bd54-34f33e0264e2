import ApiService from "./api.service";

// Types
export interface CreditPackage {
  id: string;
  name: string;
  description: string | null;
  creditsAmount: number;
  price: number;
  currency: string;
  stripePriceId?: string;
  paypalPlanId?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  plan: string;
  joinDate: string;
  profile: {
    fullName: string;
    firstName: string;
    lastName: string;
    avatarUrl: string;
  };
  status: "active" | "inactive";
  lastActive: string;
  lastLogin: string;
  // credits: number;
  totalSpent: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
  credit: {
    balance: number;
    spent: number;
  };
}

export interface AdminStats {
  totalJobs: number;
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalCreditsUsed: number;
  videoGenerations: number;
  imageGenerations: number;
  backgroundRemovals: number;
  activeSubscriptions: number;
  activeJobs: number;
  completedJobs: number;
  blogPosts: number;
}

export interface PaymentSettings {
  stripeEnabled: boolean;
  stripePublicKey: string;
  stripeSecretKey: string;
  paypalEnabled: boolean;
  paypalClientId: string;
  paypalSecretKey: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string; // User-friendly display name
  description?: string | null;
  price: number;
  currency: string;
  interval: string; // 'month', 'year'
  features: {
    videoGenerationQuota: number;
    imageGenerationQuota: number;
    backgroundRemovalQuota: number;
    [key: string]: number | boolean | string;
  };
  // Credits granted with this subscription
  creditsAmount: number;
  // Highlighted features for marketing display
  featureHighlights: string[];
  stripePriceId?: string | null;
  paypalPlanId?: string | null;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Payment {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  amount: number;
  currency: string;
  status:
    | "SUCCEEDED"
    | "FAILED"
    | "PENDING"
    | "REFUNDED"
    | "PARTIALLY_REFUNDED";
  provider: "STRIPE" | "PAYPAL";
  providerPaymentId: string;
  type: "SUBSCRIPTION" | "ONE_TIME" | "CREDIT_PURCHASE";
  metadata: {
    subscriptionId?: string;
    planName?: string;
    creditPackageName?: string;
    [key: string]: string | number | boolean | undefined | null;
  };
  createdAt: string;
  updatedAt?: string;
}

export interface Refund {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  paymentId: string;
  subscriptionId: string | null;
  amount: number;
  currency: string;
  reason: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  processedDate: string | null;
  processedBy: string | null;
}

export interface CanceledSubscription {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  planName: string;
  startDate: string;
  endDate: string;
  canceledAt: string;
  cancelReason: string | null;
  refundStatus: "NONE" | "PENDING" | "APPROVED" | "REJECTED";
  refundIds: string[];
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  author: string;
  authorId: string;
  featuredImage: string;
  status: "published" | "draft";
  createdAt: string;
  updatedAt: string;
  tags: string[];
}

export interface RecentUser {
  id: string;
  name: string;
  email: string;
  plan: string;
  joinDate: string;
  status: "active" | "inactive";
}
export interface UserListResponse {
  users: User[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
export interface RecentJob {
  id: string;
  type: "video" | "image" | "bgRemoval";
  user: string;
  userId: string;
  date: string;
  status: "pending" | "processing" | "completed" | "failed";
}

// Define a generic API response type
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

class AdminService {
  // User management
  async getUsers(): Promise<UserListResponse> {
    const response = await ApiService.get<ApiResponse<UserListResponse>>(
      "/admin/users"
    );
    console.log("response", response.data);
    return response.data.data;
  }

  async getUserById(userId: string): Promise<User> {
    const response = await ApiService.get<ApiResponse<User>>(
      `/admin/users/${userId}`
    );
    return response.data.data;
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    const response = await ApiService.put<ApiResponse<User>>(
      `/admin/users/${userId}`,
      userData
    );
    return response.data.data;
  }

  async deleteUser(userId: string): Promise<{ success: boolean }> {
    const response = await ApiService.delete<ApiResponse<{ success: boolean }>>(
      `/admin/users/${userId}`
    );
    return response.data.data;
  }

  // Dashboard statistics
  async getAdminStats(): Promise<AdminStats> {
    const response = await ApiService.get<ApiResponse<AdminStats>>(
      "/admin/stats"
    );
    return response.data.data;
  }

  async getRecentUsers(limit: number = 5): Promise<RecentUser[]> {
    const response = await ApiService.get<ApiResponse<RecentUser[]>>(
      `/admin/recent-users?limit=${limit}`
    );
    return response.data.data;
  }

  async getRecentJobs(limit: number = 5): Promise<RecentJob[]> {
    const response = await ApiService.get<ApiResponse<RecentJob[]>>(
      `/admin/recent-jobs?limit=${limit}`
    );
    return response.data.data;
  }

  // Payment settings
  async getPaymentSettings(): Promise<PaymentSettings> {
    const response = await ApiService.get<ApiResponse<PaymentSettings>>(
      "/admin/payment-config"
    );
    return response.data.data;
  }

  async updatePaymentSettings(
    settings: Partial<PaymentSettings>
  ): Promise<PaymentSettings> {
    const response = await ApiService.put<ApiResponse<PaymentSettings>>(
      "/admin/payment-config",
      settings
    );
    return response.data.data;
  }

  // Payment Management
  async getPayments(params?: {
    status?: string;
    type?: string;
    provider?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    data: Payment[];
    meta: { total: number; page: number; limit: number };
  }> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await ApiService.get<
      ApiResponse<Payment[]> & {
        meta: { total: number; page: number; limit: number };
      }
    >(
      `/admin/payments${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`
    );
    return response.data;
  }

  async getRefundRequests(): Promise<Refund[]> {
    const response = await ApiService.get<ApiResponse<Refund[]>>(
      "/admin/refunds"
    );
    return response.data.data;
  }

  async processRefundRequest(
    refundId: string,
    action: "approve" | "reject",
    notes?: string
  ): Promise<Refund> {
    const response = await ApiService.patch<ApiResponse<Refund>>(
      `/admin/refunds/${refundId}/process`,
      { action, notes }
    );
    return response.data.data;
  }

  async getCanceledSubscriptions(): Promise<CanceledSubscription[]> {
    const response = await ApiService.get<ApiResponse<CanceledSubscription[]>>(
      "/admin/canceled-subscriptions"
    );
    return response.data.data;
  }

  // Blog management
  async getBlogPosts(): Promise<BlogPost[]> {
    const response = await ApiService.get<ApiResponse<BlogPost[]>>(
      "/admin/blog-posts"
    );
    return response.data.data;
  }

  async getBlogPostById(postId: string): Promise<BlogPost> {
    const response = await ApiService.get<ApiResponse<BlogPost>>(
      `/admin/blog-posts/${postId}`
    );
    return response.data.data;
  }

  async createBlogPost(blogData: FormData): Promise<BlogPost> {
    // Now that RequestData includes FormData, we can use the proper typing
    const response = await ApiService.post<ApiResponse<BlogPost>, FormData>(
      "/admin/blog-posts",
      blogData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data.data;
  }

  async updateBlogPost(
    postId: string,
    postData: Partial<BlogPost>
  ): Promise<BlogPost> {
    const response = await ApiService.put<ApiResponse<BlogPost>>(
      `/admin/blog-posts/${postId}`,
      postData
    );
    return response.data.data;
  }

  async deleteBlogPost(postId: string): Promise<{ success: boolean }> {
    const response = await ApiService.delete<ApiResponse<{ success: boolean }>>(
      `/admin/blog-posts/${postId}`
    );
    return response.data.data;
  }

  // Upload file for blog post
  async uploadBlogImage(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await ApiService.post<ApiResponse<{ url: string }>>(
      "/admin/upload/image",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    return response.data.data;
  }

  // Subscription Plan Management
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    const response = await ApiService.get<ApiResponse<SubscriptionPlan[]>>(
      "/admin/subscription-plans"
    );
    return response.data.data;
  }

  async getSubscriptionPlanById(planId: string): Promise<SubscriptionPlan> {
    const response = await ApiService.get<ApiResponse<SubscriptionPlan>>(
      `/admin/subscription-plans/${planId}`
    );
    return response.data.data;
  }

  async createSubscriptionPlan(
    planData: Partial<SubscriptionPlan>
  ): Promise<SubscriptionPlan> {
    const response = await ApiService.post<ApiResponse<SubscriptionPlan>>(
      "/admin/subscription-plans",
      planData
    );
    return response.data.data;
  }

  async updateSubscriptionPlan(
    planId: string,
    planData: Partial<SubscriptionPlan>
  ): Promise<SubscriptionPlan> {
    const response = await ApiService.put<ApiResponse<SubscriptionPlan>>(
      `/admin/subscription-plans/${planId}`,
      planData
    );
    return response.data.data;
  }

  async deleteSubscriptionPlan(planId: string): Promise<{ success: boolean }> {
    const response = await ApiService.delete<ApiResponse<{ success: boolean }>>(
      `/admin/subscription-plans/${planId}`
    );
    return response.data.data;
  }

  // Credit Package Management
  async getCreditPackages(): Promise<CreditPackage[]> {
    const response = await ApiService.get<ApiResponse<CreditPackage[]>>(
      "/admin/credit-packages"
    );
    return response.data.data;
  }

  async createCreditPackage(
    packageData: Partial<CreditPackage>
  ): Promise<CreditPackage> {
    const response = await ApiService.post<ApiResponse<CreditPackage>>(
      "/admin/credit-packages",
      packageData
    );
    return response.data.data;
  }

  async updateCreditPackage(
    packageId: string,
    packageData: Partial<CreditPackage>
  ): Promise<CreditPackage> {
    const response = await ApiService.put<ApiResponse<CreditPackage>>(
      `/admin/credit-packages/${packageId}`,
      packageData
    );
    return response.data.data;
  }

  async deleteCreditPackage(packageId: string): Promise<{ success: boolean }> {
    const response = await ApiService.delete<ApiResponse<{ success: boolean }>>(
      `/admin/credit-packages/${packageId}`
    );
    return response.data.data;
  }

  // Service Costs Management
  async getServiceCosts(): Promise<Record<string, number>> {
    const response = await ApiService.get<ApiResponse<Record<string, number>>>(
      "/admin/service-costs"
    );
    return response.data.data;
  }

  async updateServiceCosts(
    costs: Record<string, number>
  ): Promise<Record<string, number>> {
    const response = await ApiService.put<ApiResponse<Record<string, number>>>(
      "/admin/service-costs",
      costs
    );
    return response.data.data;
  }

  // Virtual Try-On Management
  async getVirtualTryOnModels(params?: {
    page?: number;
    limit?: number;
    isDefault?: string;
    search?: string;
  }): Promise<ApiResponse<any>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await ApiService.get<ApiResponse<any>>(
      `/admin/virtual-try-on/models${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`
    );
    return response.data;
  }

  async getVirtualTryOnClothing(params?: {
    page?: number;
    limit?: number;
    isDefault?: string;
    clothingType?: string;
    search?: string;
  }): Promise<ApiResponse<any>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await ApiService.get<ApiResponse<any>>(
      `/admin/virtual-try-on/clothing${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`
    );
    return response.data;
  }

  async getVirtualTryOnStats(): Promise<ApiResponse<any>> {
    const response = await ApiService.get<ApiResponse<any>>(
      "/admin/virtual-try-on/stats"
    );
    return response.data;
  }

  async uploadVirtualTryOnModel(data: {
    modelImage: File | null;
    modelName: string;
    gender: string;
    bodyType: string;
    poseType: string;
    ethnicity: string;
  }): Promise<ApiResponse<any>> {
    const formData = new FormData();
    if (data.modelImage) {
      formData.append("modelImage", data.modelImage);
    }
    formData.append("modelName", data.modelName);
    formData.append("gender", data.gender);
    formData.append("bodyType", data.bodyType);
    formData.append("poseType", data.poseType);
    formData.append("ethnicity", data.ethnicity);

    const response = await ApiService.post<ApiResponse<any>>(
      "/admin/virtual-try-on/models",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  }

  async uploadVirtualTryOnClothing(data: {
    clothingImage: File | null;
    name: string;
    category: string;
    style: string;
    color: string;
    season: string;
    description: string;
  }): Promise<ApiResponse<any>> {
    const formData = new FormData();
    if (data.clothingImage) {
      formData.append("clothingImage", data.clothingImage);
    }
    formData.append("name", data.name);
    formData.append("category", data.category);
    formData.append("style", data.style);
    formData.append("color", data.color);
    formData.append("season", data.season);
    formData.append("description", data.description);

    const response = await ApiService.post<ApiResponse<any>>(
      "/admin/virtual-try-on/clothing",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  }

  async deleteVirtualTryOnModel(modelId: string): Promise<ApiResponse<any>> {
    const response = await ApiService.delete<ApiResponse<any>>(
      `/admin/virtual-try-on/models/${modelId}`
    );
    return response.data;
  }

  async deleteVirtualTryOnClothing(
    clothingId: string
  ): Promise<ApiResponse<any>> {
    const response = await ApiService.delete<ApiResponse<any>>(
      `/admin/virtual-try-on/clothing/${clothingId}`
    );
    return response.data;
  }

  async updateVirtualTryOnModel(
    modelId: string,
    data: any
  ): Promise<ApiResponse<any>> {
    const response = await ApiService.patch<ApiResponse<any>>(
      `/admin/virtual-try-on/models/${modelId}`,
      data
    );
    return response.data;
  }

  async updateVirtualTryOnClothing(
    clothingId: string,
    data: any
  ): Promise<ApiResponse<any>> {
    const response = await ApiService.patch<ApiResponse<any>>(
      `/admin/virtual-try-on/clothing/${clothingId}`,
      data
    );
    return response.data;
  }
}

export default new AdminService();
