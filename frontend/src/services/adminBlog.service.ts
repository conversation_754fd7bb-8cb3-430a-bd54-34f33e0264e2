import ApiService from "./api.service";

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  postCount?: number; // Optional, depending on backend response
}

export interface PaginatedBlogCategories {
  data: BlogCategory[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    limit: number;
  };
}

const ADMIN_BLOG_API_BASE_URL = "/admin/blog";

export const getAllBlogCategoriesAdmin = async (
  page = 1,
  limit = 10,
  searchTerm = ""
): Promise<PaginatedBlogCategories> => {
  try {
    const response = await ApiService.get<PaginatedBlogCategories>(
      `${ADMIN_BLOG_API_BASE_URL}/categories/all`,
      {
        params: { page, limit, searchTerm },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching blog categories:", error);
    // Consider throwing a more specific error or returning a default structure
    throw error;
  }
};

export interface CreateBlogCategoryPayload {
  name: string;
  slug: string;
  description?: string;
}

export const createBlogCategoryAdmin = async (
  payload: CreateBlogCategoryPayload
): Promise<BlogCategory> => {
  try {
    // Assuming the backend endpoint for creating a category is POST /admin/blog/categories
    // And it returns the created category object, possibly nested under a 'data' key.
    const response = await ApiService.post<{ data: BlogCategory }>(
      `${ADMIN_BLOG_API_BASE_URL}/categories`,
      payload as unknown as Record<string, unknown> // Double cast to satisfy ApiService type
    );
    return response.data.data; // Adjust if backend response structure is different
  } catch (error) {
    console.error("Error creating blog category:", error);
    // It's good practice to handle specific API error responses here
    // For now, re-throwing the error to be caught by the component
    throw error;
  }
};
export const updateBlogCategoryAdmin = async (
  categoryId: string,
  payload: CreateBlogCategoryPayload
): Promise<BlogCategory> => {
  try {
    // Assuming the backend endpoint for creating a category is POST /admin/blog/categories
    // And it returns the created category object, possibly nested under a 'data' key.
    const response = await ApiService.put<{ data: BlogCategory }>(
      `${ADMIN_BLOG_API_BASE_URL}/categories/${categoryId}`,
      payload as unknown as Record<string, unknown> // Double cast to satisfy ApiService type
    );
    return response.data.data; // Adjust if backend response structure is different
  } catch (error) {
    console.error("Error creating blog category:", error);
    // It's good practice to handle specific API error responses here
    // For now, re-throwing the error to be caught by the component
    throw error;
  }
};
export const deleteBlogCategoryAdmin = async (
  categoryId: string
): Promise<BlogCategory> => {
  try {
    // Assuming the backend endpoint for creating a category is POST /admin/blog/categories
    // And it returns the created category object, possibly nested under a 'data' key.
    const response = await ApiService.delete<{ data: BlogCategory }>(
      `${ADMIN_BLOG_API_BASE_URL}/categories/${categoryId}`
    );
    return response.data.data; // Adjust if backend response structure is different
  } catch (error) {
    console.error("Error creating blog category:", error);
    // It's good practice to handle specific API error responses here
    // For now, re-throwing the error to be caught by the component
    throw error;
  }
};

// Blog Post Types
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string; // HTML content from rich text editor
  excerpt?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  authorId: string; // Assuming a link to a user ID
  // author?: User; // If backend populates author details - User type would need to be defined/imported
  categoryIds?: string[];
  // categories?: BlogCategory[]; // If backend populates categories
  tagIds?: string[]; // Assuming tags will also be managed - Tag type would need to be defined/imported
  // tags?: Tag[]; // If backend populates tags
  publishedAt?: string; // ISO date string
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  metaTitle?: string;
  metaDescription?: string;
  featuredImage?: string; // URL
}

export interface PaginatedBlogPosts {
  data: BlogPost[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    limit: number;
  };
}

export interface CreateBlogPostPayload {
  title: string;
  slug: string;
  content: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  authorId: string; // This might be handled by the backend based on the authenticated user
  excerpt?: string;
  categoryIds?: string[];
  tagIds?: string[];
  publishedAt?: string;
  metaTitle?: string;
  metaDescription?: string;
  featuredImage?: string;
}

export interface UpdateBlogPostPayload {
  title?: string;
  slug?: string;
  content?: string;
  status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  excerpt?: string;
  categoryIds?: string[];
  tagIds?: string[];
  publishedAt?: string; // Can be null to unpublish if backend supports
  metaTitle?: string;
  metaDescription?: string;
  featuredImage?: string;
}
// End Blog Post Types

// Blog Post Service Functions
export const getAllBlogPostsAdmin = async (
  page = 1,
  limit = 10,
  searchTerm = ""
): Promise<PaginatedBlogPosts> => {
  try {
    const response = await ApiService.get<PaginatedBlogPosts>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/all`,
      {
        params: { page, limit, searchTerm },
      }
    );
    return response.data; // Assuming PaginatedBlogPosts is the direct response data
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    throw error;
  }
};

export const getBlogPostByIdAdmin = async (
  postId: string
): Promise<BlogPost> => {
  try {
    const response = await ApiService.get<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`
    );
    return response.data.data; // Assuming BlogPost is nested under 'data'
  } catch (error) {
    console.error(`Error fetching blog post with ID ${postId}:`, error);
    throw error;
  }
};

export const createBlogPostAdmin = async (
  payload: CreateBlogPostPayload
): Promise<BlogPost> => {
  try {
    const response = await ApiService.post<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts`,
      payload as unknown as Record<string, unknown>
    );
    return response.data.data; // Assuming created BlogPost is nested under 'data'
  } catch (error) {
    console.error("Error creating blog post:", error);
    throw error;
  }
};

export const updateBlogPostAdmin = async (
  postId: string,
  payload: UpdateBlogPostPayload
): Promise<BlogPost> => {
  try {
    const response = await ApiService.put<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`,
      payload as unknown as Record<string, unknown>
    );
    return response.data.data; // Assuming updated BlogPost is nested under 'data'
  } catch (error) {
    console.error(`Error updating blog post with ID ${postId}:`, error);
    throw error;
  }
};

export const deleteBlogPostAdmin = async (
  postId: string
): Promise<BlogPost> => {
  // Assuming backend returns the deleted post, similar to category deletion
  try {
    const response = await ApiService.delete<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`
    );
    return response.data.data; // Adjust if backend response structure is different
  } catch (error) {
    console.error(`Error deleting blog post with ID ${postId}:`, error);
    throw error;
  }
};
// End Blog Post Service Functions
