import ApiService from "./api.service";
import type { ApiResponse } from "./api.service";

export interface BgRemovalJob {
  id: string;
  userId: string;
  originalImageUrl: string;
  resultImageUrl: string | null;
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
  createdAt: string;
  updatedAt: string;
  error?: string;
}

export interface BgRemovalJobResponse {
  jobId: string;
  status: string;
  originalImageUrl: string;
}

export interface BgRemovalJobsResponse {
  jobs: BgRemovalJob[];
}

/**
 * Service for handling background removal API calls
 */
const BgRemovalService = {
  /**
   * Upload an image for background removal
   * @param file Image file to upload
   * @returns Background removal job response
   */
  uploadImage(file: File): Promise<ApiResponse<BgRemovalJobResponse>> {
    const formData = new FormData();
    formData.append("image", file);

    return ApiService.post<ApiResponse<BgRemovalJobResponse>>(
      "/background-removal/upload",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    ).then((response) => response.data);
  },

  /**
   * Process an image URL for background removal
   * @param imageUrl URL of the image to process
   * @returns Background removal job response
   */
  processImageUrl(
    imageUrl: string
  ): Promise<ApiResponse<BgRemovalJobResponse>> {
    return ApiService.post<ApiResponse<BgRemovalJobResponse>>(
      "/background-removal/process-url",
      { imageUrl }
    ).then((response) => response.data);
  },

  /**
   * Get a background removal job by ID
   * @param jobId Job ID
   * @returns Background removal job
   */
  getJob(jobId: string): Promise<ApiResponse<BgRemovalJob>> {
    return ApiService.get<ApiResponse<BgRemovalJob>>(
      `/background-removal/jobs/${jobId}`
    ).then((response) => response.data);
  },

  /**
   * Get all background removal jobs for the current user
   * @returns List of background removal jobs
   */
  getUserJobs(): Promise<ApiResponse<BgRemovalJobsResponse>> {
    return ApiService.get<ApiResponse<BgRemovalJobsResponse>>(
      "/background-removal/jobs"
    ).then((response) => response.data);
  },

  /**
   * Download a processed image
   * @param imageUrl URL of the processed image
   * @param filename Filename to save the image as
   */
  /**
   * Direct background removal using the new API endpoint
   * @param file Image file to upload
   * @returns Direct API response with processed image URL
   */
  directRemoveBackground(
    file: File
  ): Promise<{
    success: boolean;
    data: { imageUrl: string; jobId?: string; cached?: boolean };
    message?: string;
  }> {
    const formData = new FormData();
    formData.append("image", file);

    // Define the response type structure
    type BgRemovalResponse = {
      success: boolean;
      data: { imageUrl: string; jobId?: string; cached?: boolean };
      message?: string;
    };

    return ApiService.post<BgRemovalResponse>(
      "/background-removal/direct-remove",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 60000, // 60 second timeout for larger images
      }
    ).then((response) => response.data);
  },

  /**
   * Download a processed image
   * @param imageUrl URL of the processed image
   * @param filename Filename to save the image as
   */
  downloadImage(imageUrl: string, filename: string): void {
    // Create a temporary anchor element
    const link = document.createElement("a");
    link.href = imageUrl;
    link.download = filename;

    // Append to the document
    document.body.appendChild(link);

    // Trigger download
    link.click();

    // Clean up
    document.body.removeChild(link);
  },
};

export default BgRemovalService;
