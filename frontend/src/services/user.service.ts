import ApiService from './api.service';
import type { ApiResponse } from './api.service';
import type { User } from './auth.service';
import type { Subscription } from './payment.service';

export interface UserListParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UserCredits {
  id: string;
  userId: string;
  amount: number;
  type: 'video' | 'image' | 'background-removal';
  remaining: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserCreditsResponse {
  credits: UserCredits[];
  total: {
    video: number;
    image: number;
    backgroundRemoval: number;
  };
}

const UserService = {
  /**
   * Get list of users (admin only)
   */
  async getUsers(params: UserListParams = {}): Promise<UserListResponse> {
    const response = await ApiService.get<ApiResponse<UserListResponse>>('/admin/users', { params });
    return response.data.data;
  },

  /**
   * Get user by ID (admin only)
   */
  async getUserById(userId: string): Promise<User> {
    const response = await ApiService.get<ApiResponse<User>>(`/admin/users/${userId}`);
    return response.data.data;
  },

  /**
   * Update user (admin only)
   */
  async updateUser(userId: string, data: Partial<User>): Promise<User> {
    const response = await ApiService.put<ApiResponse<User>>(`/admin/users/${userId}`, data);
    return response.data.data;
  },

  /**
   * Delete user (admin only)
   */
  async deleteUser(userId: string): Promise<void> {
    await ApiService.delete(`/admin/users/${userId}`);
  },

  /**
   * Get user credits
   */
  async getUserCredits(userId?: string): Promise<UserCreditsResponse> {
    const endpoint = userId ? `/admin/users/${userId}/credits` : '/user/credits';
    const response = await ApiService.get<ApiResponse<UserCreditsResponse>>(endpoint);
    return response.data.data;
  },

  /**
   * Add credits to user (admin only)
   */
  async addCredits(userId: string, amount: number, type: 'video' | 'image' | 'background-removal'): Promise<UserCredits> {
    const response = await ApiService.post<ApiResponse<UserCredits>>(`/admin/users/${userId}/credits`, { amount, type });
    return response.data.data;
  },

  /**
   * Get user subscription
   */
  async getUserSubscription(userId?: string): Promise<Subscription> {
    const endpoint = userId ? `/admin/users/${userId}/subscription` : '/user/subscription';
    const response = await ApiService.get<ApiResponse<Subscription>>(endpoint);
    return response.data.data;
  },

  /**
   * Update user subscription (admin only)
   */
  async updateUserSubscription(userId: string, planId: string): Promise<Subscription> {
    const response = await ApiService.put<ApiResponse<Subscription>>(`/admin/users/${userId}/subscription`, { planId });
    return response.data.data;
  }
};

export default UserService;
