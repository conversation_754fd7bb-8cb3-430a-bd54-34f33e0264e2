import ApiService from "./api.service";
import type { ApiResponse } from "./api.service";

export interface ModelImage {
  id: string;
  userId: string;
  imagePath: string;
  modelName: string;
  gender: "MALE" | "FEMALE" | "UNISEX";
  bodyType?: string;
  poseType?: string;
  ethnicity?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ClothingItem {
  id: string;
  userId: string;
  imagePath: string;
  clothingType: "TOP" | "BOTTOM" | "DRESS" | "FULL_OUTFIT";
  category?: string;
  color?: string;
  style?: string;
  createdAt: string;
  updatedAt: string;
}

export interface VirtualTryOnJob {
  id: string;
  userId: string;
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
  mode: "SINGLE" | "TOP_BOTTOM";
  humanImagePath: string | null;
  clothImagePath: string | null;
  bottomClothImagePath: string | null;
  resultImagePath: string | null;
  modelImageId?: string;
  garmentType?: string;
  createdAt: string;
  processingStartedAt?: string;
  processingCompletedAt?: string;
  errorMessage?: string;
}

export interface RecentTryOn {
  id: string;
  status: string;
  mode: "SINGLE" | "TOP_BOTTOM";
  humanImagePath: string;
  clothImagePath: string;
  bottomClothImagePath: string | null;
  resultImagePath: string;
  createdAt: string;
  accessedAt: string;
}

export interface VirtualTryOnJobResponse {
  jobId: string;
  status: string;
  mode: string;
  createdAt: string;
  link?: string;
}

export interface RecentTryOn {
  id: string;
  userId: string;
  tryonId: string;
  accessedAt: string;
  isFavorite: boolean;
  tryOn: VirtualTryOnJob;
}

/**
 * Service for handling virtual try-on API calls
 */
const VirtualTryOnService = {
  /**
   * Process a virtual try-on request
   * @param params Virtual try-on parameters
   * @returns Virtual try-on job response
   */
  // First, import axios at the top of your file

  /**
   * Process a virtual try-on request through the backend
   * @param params Virtual try-on parameters
   * @returns Virtual try-on job response
   */
  processVirtualTryOn(params: {
    human_image?: File;
    cloth_image: File;
    low_cloth_image?: File;
    mode: "single" | "top_bottom";
    modelImageId?: string;
    germent_type?: string;
    saveAsModel?: boolean;
    modelName?: string;
    gender?: "MALE" | "FEMALE" | "UNISEX";
    fit_type?: string;
    cloth_category?: string;
  }): Promise<ApiResponse<VirtualTryOnJobResponse>> {
    const formData = new FormData();

    // Add files to form data
    if (params.human_image) {
      formData.append(
        "humanImage",
        params.human_image,
        params.human_image.name
      );
    }

    if (params.cloth_image) {
      formData.append(
        "clothImage",
        params.cloth_image,
        params.cloth_image.name
      );
    }

    if (params.low_cloth_image && params.mode === "top_bottom") {
      formData.append(
        "bottomClothImage",
        params.low_cloth_image,
        params.low_cloth_image.name
      );
    }

    // Add other parameters
    formData.append("mode", params.mode === "single" ? "SINGLE" : "TOP_BOTTOM");

    if (params.germent_type) {
      formData.append("garmentType", params.germent_type);
    }

    if (params.modelImageId) {
      formData.append("modelImageId", params.modelImageId);
    }

    if (params.saveAsModel) {
      formData.append("saveAsModel", "true");
      formData.append(
        "modelName",
        params.modelName || `Model ${new Date().toLocaleDateString()}`
      );
      formData.append("gender", params.gender || "UNISEX");
    }

    // Add fit type and category for single mode
    if (params.fit_type) {
      formData.append("fitType", params.fit_type);
    }
    if (params.cloth_category) {
      formData.append("clothCategory", params.cloth_category);
    }

    console.log("🌐 Making backend API call to virtual try-on endpoint");

    return ApiService.post<ApiResponse<VirtualTryOnJobResponse>>(
      "/virtual-try-on",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 120000, // 2 minutes timeout for processing
      }
    )
      .then((response) => response.data)
      .catch((error) => {
        console.error("Virtual try-on processing error:", error);
        return {
          success: false,
          message:
            error.response?.data?.message ||
            error.message ||
            "Error processing virtual try-on request. Please try again.",
          data: {} as VirtualTryOnJobResponse,
        };
      });
  },

  /**
   * Get a virtual try-on job by ID
   * @param jobId Job ID
   * @returns Virtual try-on job
   */
  getJob(jobId: string): Promise<ApiResponse<VirtualTryOnJob>> {
    return ApiService.get<ApiResponse<VirtualTryOnJob>>(
      `/virtual-try-on/${jobId}`
    ).then((response) => response.data);
  },

  /**
   * Get all virtual try-on jobs for the current user
   * @returns List of virtual try-on jobs
   */
  getUserHistory(): Promise<ApiResponse<VirtualTryOnJob[]>> {
    return ApiService.get<ApiResponse<VirtualTryOnJob[]>>(
      "/virtual-try-on/history"
    ).then((response) => response.data);
  },

  /**
   * Get recent try-ons for the current user
   * @returns List of recent try-ons
   */
  getRecentTryOns(): Promise<ApiResponse<RecentTryOn[]>> {
    return ApiService.get<ApiResponse<RecentTryOn[]>>(
      "/virtual-try-on/recent"
    ).then((response) => response.data);
  },

  /**
   * Get model images for the current user
   * @returns List of model images
   */
  getModelImages(): Promise<ApiResponse<ModelImage[]>> {
    return ApiService.get<ApiResponse<ModelImage[]>>(
      "/virtual-try-on/models"
    ).then((response) => response.data);
  },

  /**
   * Get admin model images (our models)
   * @returns List of admin model images
   */
  getAdminModelImages(): Promise<ApiResponse<ModelImage[]>> {
    return ApiService.get<ApiResponse<ModelImage[]>>(
      "/virtual-try-on/models/admin"
    ).then((response) => response.data);
  },

  /**
   * Get user model images (your models)
   * @returns List of user model images
   */
  getUserModelImages(): Promise<ApiResponse<ModelImage[]>> {
    return ApiService.get<ApiResponse<ModelImage[]>>(
      "/virtual-try-on/models/user"
    ).then((response) => response.data);
  },

  /**
   * Upload a model image
   * @param file Model image file
   * @returns Uploaded model image
   */
  uploadModelImage(file: File): Promise<ApiResponse<ModelImage>> {
    const formData = new FormData();
    formData.append("modelImage", file);

    return ApiService.post<ApiResponse<ModelImage>>(
      "/virtual-try-on/models",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    )
      .then((response) => response.data)
      .catch((error) => {
        console.error("Error uploading model image:", error);
        return {
          success: false,
          message:
            error.response?.data?.message ||
            "Failed to upload model image. Please try again.",
          data: {} as ModelImage,
        };
      });
  },

  /**
   * Delete a model image
   * @param modelId ID of the model to delete
   * @returns Success status
   */
  deleteModelImage(
    modelId: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    return ApiService.delete<ApiResponse<{ success: boolean }>>(
      `/virtual-try-on/models/${modelId}`
    )
      .then((response) => response.data)
      .catch((error) => {
        console.error("Error deleting model image:", error);
        return {
          success: false,
          message:
            error.response?.data?.message ||
            "Failed to delete model image. Please try again.",
          data: { success: false },
        };
      });
  },

  /**
   * Download a processed image
   * @param imageUrl URL of the processed image
   * @param filename Filename to save the image as
   */
  downloadImage(imageUrl: string, filename: string): void {
    // Create a link element
    const link = document.createElement("a");
    link.href = imageUrl;
    link.download = filename || `virtual-tryon-${new Date().getTime()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },
};

export default VirtualTryOnService;
