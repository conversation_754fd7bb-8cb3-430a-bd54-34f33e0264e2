import ApiService from "./api.service";
import type { ApiResponse } from "./api.service";

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  imageUrl: string;
  category: string;
  tags: string[];
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  publishedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface BlogListParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  tag?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  status?: "draft" | "published" | "all";
}

export interface BlogListResponse {
  posts: BlogPost[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  count: number;
}

export interface CreateBlogPostData extends Record<string, unknown> {
  title: string;
  content: string;
  excerpt: string;
  imageUrl?: string;
  category: string;
  tags?: string[];
  status?: "DTAFT" | "PUBLISHED";
}

const BlogService = {
  /**
   * Get list of published blog posts
   */
  async getPosts(params: BlogListParams = {}): Promise<BlogListResponse> {
    const response = await ApiService.get<ApiResponse<BlogListResponse>>(
      "/blog/posts",
      { params }
    );
    return response.data.data;
  },

  /**
   * Get blog post by slug
   */
  async getPostBySlug(slug: string): Promise<BlogPost> {
    const response = await ApiService.get<ApiResponse<BlogPost>>(
      `/blog/posts/${slug}`
    );
    return response.data.data;
  },

  /**
   * Get blog post by ID (admin only)
   */
  async getPostById(id: string): Promise<BlogPost> {
    const response = await ApiService.get<ApiResponse<BlogPost>>(
      `/admin/blog/${id}`
    );
    return response.data.data;
  },

  /**
   * Get list of all blog posts (admin only)
   */
  async getAllPosts(params: BlogListParams = {}): Promise<BlogListResponse> {
    const response = await ApiService.get<ApiResponse<BlogListResponse>>(
      "/admin/blog",
      { params }
    );
    return response.data.data;
  },

  /**
   * Create blog post (admin only)
   */
  async createPost(data: CreateBlogPostData): Promise<BlogPost> {
    const response = await ApiService.post<ApiResponse<BlogPost>>(
      "/admin/blog",
      data
    );
    return response.data.data;
  },

  /**
   * Update blog post (admin only)
   */
  async updatePost(
    id: string,
    data: Partial<CreateBlogPostData>
  ): Promise<BlogPost> {
    const response = await ApiService.put<ApiResponse<BlogPost>>(
      `/admin/blog/${id}`,
      data
    );
    return response.data.data;
  },

  /**
   * Delete blog post (admin only)
   */
  async deletePost(id: string): Promise<void> {
    await ApiService.delete(`/admin/blog/${id}`);
  },

  /**
   * Get blog categories
   */
  async getCategories(): Promise<BlogCategory[]> {
    const response = await ApiService.get<ApiResponse<BlogCategory[]>>(
      "/blog/categories"
    );
    return response.data.data;
  },

  /**
   * Get related posts
   */
  async getRelatedPosts(
    postId: string,
    limit: number = 3
  ): Promise<BlogPost[]> {
    const response = await ApiService.get<ApiResponse<BlogPost[]>>(
      `/blog/${postId}/related`,
      {
        params: { limit },
      }
    );
    return response.data.data;
  },
};

export default BlogService;
