import ApiService from "./api.service";

// Define the API response interface
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}
export interface Credit {
  balance: number;
  spent: number;
}
// Types for authentication
export interface User {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  role: "USER" | "ADMIN";
  plan: "Free" | "Starter" | "Pro" | "Business";
  credit: Credit;
  profile: {
    firstName: string;
    lastName: string;
    avatarUrl: string;
    id: string;
    userId: string;
    company?: string;
    jobTitle?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Make these types compatible with RequestData
export interface LoginCredentials extends Record<string, unknown> {
  email: string;
  password: string;
}

export interface RegisterData extends Record<string, unknown> {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export interface ResetPasswordData extends Record<string, unknown> {
  token: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

const AuthService = {
  /**
   * Login user and return user data with token
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await ApiService.post<ApiResponse<AuthResponse>>(
      "/auth/login",
      credentials
    );
    console.log("response", response);
    return response.data.data;
  },

  /**
   * Register a new user
   */
  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await ApiService.post<ApiResponse<AuthResponse>>(
      "/auth/register",
      data
    );
    return response.data.data;
  },

  /**
   * Get the current user profile
   */
  async getCurrentUser(): Promise<User> {
    const response = await ApiService.get<ApiResponse<User>>("/user/me");
    return response.data.data;
  },

  /**
   * Update user profile
   */
  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await ApiService.put<ApiResponse<User>>("/user/me", data);
    return response.data.data;
  },

  /**
   * Upload profile image
   */
  async uploadProfileImage(file: File): Promise<{ avatarUrl: string }> {
    const formData = new FormData();
    formData.append("profileImage", file);

    // Use XMLHttpRequest to track upload progress if needed in the future
    const response = await ApiService.post<ApiResponse<{ avatarUrl: string }>>(
      "/user/me/avatar",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    return response.data.data;
  },

  /**
   * Send password reset email
   */
  async forgotPassword(email: string): Promise<{ message: string }> {
    const response = await ApiService.post<{ message: string }>(
      "/auth/forgot-password",
      { email }
    );
    return response.data;
  },

  /**
   * Reset password with token
   */
  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {
    const response = await ApiService.post<{ message: string }>(
      "/auth/reset-password",
      data
    );
    return response.data;
  },

  /**
   * Logout user (revoke token on server)
   */
  async logout(refreshToken: string): Promise<void> {
    await ApiService.post("/auth/logout", { refreshToken });
  },
};

export default AuthService;
