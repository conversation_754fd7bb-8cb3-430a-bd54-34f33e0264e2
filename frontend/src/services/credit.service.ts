import ApiService from "./api.service";
import type { ApiResponse } from "./api.service";

// Credit-related interfaces
export interface CreditBalance {
  id: string;
  userId: string;
  balance: number;
  spent: number;
  lastUpdatedAt: string;
}

export interface CreditTransaction {
  id: string;
  userId: string;
  amount: number;
  type: string;
  description: string;
  createdAt: string;
  jobId?: string;
}

export interface CreditPackage {
  id: string;
  name: string;
  description: string | null;
  creditsAmount: number;
  price: number;
  currency: string;
  isActive: boolean;
}

export interface ServiceCost {
  imageGeneration: number;
  videoGeneration: number;
  backgroundRemoval: number;
  virtualTryOn: number;
  speedpaint: number;
}

export interface TransactionHistoryResponse {
  transactions: CreditTransaction[];
}

export interface StripeCheckoutResponse {
  url: string;
}

export interface StripePaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  orderId: string;
}

export interface PayPalOrderResponse {
  id: string;
  status: string;
}

/**
 * Service for handling credit-related API calls
 */
const CreditService = {
  /**
   * Get the current user's credit balance
   */
  getUserCreditBalance(): Promise<ApiResponse<CreditBalance>> {
    return ApiService.get<ApiResponse<CreditBalance>>("/credits/balance").then(
      (response) => response.data
    );
  },

  /**
   * Get the user's transaction history
   */
  getTransactionHistory(): Promise<ApiResponse<TransactionHistoryResponse>> {
    return ApiService.get<ApiResponse<TransactionHistoryResponse>>(
      "/credits/history"
    ).then((response) => response.data);
  },

  /**
   * Get available credit packages
   */
  getCreditPackages(): Promise<ApiResponse<CreditPackage[]>> {
    return ApiService.get<ApiResponse<CreditPackage[]>>(
      "/credits/packages"
    ).then((response) => response.data);
  },

  /**
   * Get service costs in credits
   */
  getServiceCosts(): Promise<ApiResponse<ServiceCost>> {
    return ApiService.get<ApiResponse<ServiceCost>>("/credits/costs").then(
      (response) => response.data
    );
  },

  /**
   * Create a Stripe checkout session for purchasing credits
   */
  createStripeCheckoutSession(
    packageId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<ApiResponse<StripeCheckoutResponse>> {
    return ApiService.post<ApiResponse<StripeCheckoutResponse>>(
      "/credits/purchase/stripe/create-checkout-session",
      { packageId, successUrl, cancelUrl }
    ).then((response) => response.data);
  },

  /**
   * Create a PayPal order for purchasing credits
   */
  createPayPalOrder(
    packageId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<ApiResponse<PayPalOrderResponse>> {
    return ApiService.post<ApiResponse<PayPalOrderResponse>>(
      "/credits/purchase/paypal/create-order",
      { packageId, successUrl, cancelUrl }
    ).then((response) => response.data);
  },

  /**
   * Capture a PayPal payment
   */
  capturePayPalOrder(
    orderId: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    return ApiService.post<ApiResponse<{ success: boolean }>>(
      `/credits/purchase/paypal/capture-order`,
      { orderId }
    ).then((response) => response.data);
  },

  /**
   * Create a Stripe Payment Intent for purchasing credits
   * This allows using Stripe Elements directly on the site instead of redirecting
   */
  createStripePaymentIntent(
    packageId: string
  ): Promise<ApiResponse<StripePaymentIntentResponse>> {
    return ApiService.post<ApiResponse<StripePaymentIntentResponse>>(
      `/credits/purchase/stripe/create-payment-intent`,
      { packageId }
    ).then((response) => response.data);
  },
};

export default CreditService;
