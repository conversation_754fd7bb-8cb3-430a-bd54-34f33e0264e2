import { AlignJustify, X } from "lucide-react";
import { useState } from "react";
import LoginModal from "@/components/auth/LoginModal";
import SignupModal from "@/components/auth/SignUpModal";
import ForgotPasswordModal from "@/components/auth/ForgotPasswordModal";
import MobileNav from "./MobileNav";
import DesktopNav from "./DesktopNav";

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isShowLoginModal, setIsShowLoginModal] = useState(false);
  const [isShowSignUpModal, setIsShowSignUpModal] = useState(false);
  const [isShowForgotPassModal, setIsShowForgotPassModal] = useState(false);

  return (
    <>
      <header
        className={`border-b border-gray-800 sticky top-0 z-50 w-full transition-all duration-300 bg-gradient-to-b from-[#120d1f] to-black`}
      >
        <div className="mx-auto px-4 sm:px-6 lg:px-8 2xl:px-[120px] py-[18px] flex items-center justify-between w-full">
          <DesktopNav
            setIsShowLoginModal={setIsShowLoginModal}
            setIsShowSignUpModal={setIsShowSignUpModal}
          />
          <button
            className="lg:hidden block ml-4"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="w-6 h-6 text-white" />
            ) : (
              <AlignJustify className="w-6 h-6 text-white" />
            )}
          </button>
        </div>

        {isMobileMenuOpen && (
          <MobileNav
            setIsMobileMenuOpen={setIsMobileMenuOpen}
            setIsShowLoginModal={setIsShowLoginModal}
            setIsShowSignUpModal={setIsShowSignUpModal}
          />
        )}
      </header>
      <LoginModal
        isOpen={isShowLoginModal}
        onClose={() => setIsShowLoginModal(false)}
        onClickSignUpModal={() => {
          setIsShowLoginModal(false);
          setIsShowForgotPassModal(false);
          setIsShowSignUpModal(true);
        }}
        onClickForgotPassword={() => {
          setIsShowLoginModal(false);
          setIsShowSignUpModal(false);
          setIsShowForgotPassModal(true);
        }}
      />
      <SignupModal
        isOpen={isShowSignUpModal}
        onClose={() => setIsShowSignUpModal(false)}
        onClickLoginModal={() => {
          setIsShowLoginModal(true);
          setIsShowSignUpModal(false);
        }}
      />
      <ForgotPasswordModal
        isOpen={isShowForgotPassModal}
        onClose={() => setIsShowForgotPassModal(false)}
        onBack={() => {
          setIsShowForgotPassModal(false);
          setIsShowLoginModal(true);
        }}
      />
    </>
  );
}
