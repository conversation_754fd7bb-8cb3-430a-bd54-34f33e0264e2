import { Button } from "../ui/button";
import { <PERSON> } from "react-router-dom";
import React, { useState } from "react";
import { mobileNavLinks } from "../common/variable";
import { ChevronDown } from "lucide-react";

interface MobileNavProps {
  setIsMobileMenuOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setIsShowLoginModal: React.Dispatch<React.SetStateAction<boolean>>;
  setIsShowSignUpModal: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function MobileNav({
  setIsMobileMenuOpen,
  setIsShowLoginModal,
  setIsShowSignUpModal,
}: MobileNavProps) {
  const [openSubMenuLabel, setOpenSubMenuLabel] = useState<string | null>(null);

  const handleLinkClick = () => {
    setIsMobileMenuOpen(false);
    setOpenSubMenuLabel(null);
  };

  const toggleSubMenu = (label: string, event: React.MouseEvent) => {
    event.preventDefault();
    setOpenSubMenuLabel((prevLabel) => (prevLabel === label ? null : label));
  };

  const handleLoginClick = () => {
    setIsShowLoginModal(true);
    setIsMobileMenuOpen(false);
  };

  const handleSignUpClick = () => {
    setIsShowSignUpModal(true);
    setIsMobileMenuOpen(false);
  };

  return (
    <div className="block lg:hidden bg-gradient-to-b from-[#120d1f] to-black border-t">
      <div className="container mx-auto px-4 py-4 flex flex-col space-y-4 text-[#F5F5F7]">
        {mobileNavLinks.map((link) => {
          if (link.subItem) {
            const isOpen = openSubMenuLabel === link.label;

            return (
              <div key={link.label} className="flex flex-col">
                <button
                  onClick={(e) => toggleSubMenu(link.label, e)}
                  className="flex items-center justify-between w-full text-sm font-medium hover:text-foreground transition-colors py-2"
                >
                  {link.label}
                  <ChevronDown
                    className={`w-4 h-4 transition-transform duration-200 ${
                      isOpen ? "rotate-180" : ""
                    }`}
                  />
                </button>
                <div
                  className={`
                    flex flex-col pl-4 mt-2 space-y-3 overflow-hidden
                    transition-all duration-700 ease-in-out
                    ${
                      isOpen
                        ? "max-h-screen translate-y-0"
                        : "max-h-0 -translate-y-3"
                    }
                  `}
                >
                  {link.subItem.map((subLink) => (
                    <Link
                      key={subLink.to}
                      to={subLink.to}
                      className="text-sm text-gray-300 hover:text-white transition-colors"
                      onClick={handleLinkClick}
                    >
                      {subLink.label}
                    </Link>
                  ))}
                </div>
              </div>
            );
          } else {
            return (
              <Link
                key={link.to}
                to={link.to}
                className="text-sm font-medium hover:text-foreground transition-colors py-2"
                onClick={handleLinkClick}
              >
                {link.label}
              </Link>
            );
          }
        })}

        <div className="flex flex-col space-y-2 py-4 gap-3">
          <Button
            outline={false}
            variant={"animeShine"}
            className="rounded-full bg-black px-8 py-[10px] hover:bg-black text-sm font-normal text-[#F5F5F7]"
            onClick={handleLoginClick}
          >
            Log in
          </Button>
          <Button
            outline={false}
            variant={"animeGradient"}
            className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 px-8 py-[10px] text-sm font-normal text-[#F5F5F7]"
            onClick={handleSignUpClick}
          >
            Sign up
          </Button>
        </div>
      </div>
    </div>
  );
}
