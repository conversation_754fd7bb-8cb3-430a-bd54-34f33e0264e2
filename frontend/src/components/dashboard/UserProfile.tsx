import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useApp } from "@/contexts/useApp";
import AuthService from "@/services/auth.service";
import { toast } from "sonner";
import { Spinner } from "@/components/ui/spinner";
import { Badge } from "@/components/ui/badge";
import { Camera, X } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import SubscriptionPlansDisplay from "@/components/subscription/SubscriptionPlansDisplay";

const UserProfile = () => {
  const { user, updateUser, isLoading, refreshUserData } = useApp();
  const [isEditing, setIsEditing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.profile?.firstName || "",
    lastName: user?.profile?.lastName || "",
    company: user?.profile?.company || "",
    email: user?.email || "",
    avatarUrl: user?.profile?.avatarUrl || "",
  });
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!user) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
        <span className="ml-2">Loading profile...</span>
      </div>
    );
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Update user profile
    updateUser({
      profile: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        avatarUrl: user?.profile?.avatarUrl, // Avatar is handled separately by the upload function
        id: user?.profile?.id,
        userId: user?.profile?.userId,
      },
    });

    setIsEditing(false);
    toast.success("Profile updated successfully");
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image too large. Maximum size is 5MB.");
      return;
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please upload an image file.");
      return;
    }

    // First show a preview
    setIsUploading(true);
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setPreviewUrl(event.target.result as string);
      }
    };
    reader.onerror = () => {
      toast.error("Failed to read file.");
      setIsUploading(false);
    };
    reader.readAsDataURL(file);

    try {
      // Upload the file to the server
      const result = await AuthService.uploadProfileImage(file);

      // Update the user profile with the new avatar URL
      if (result.avatarUrl) {
        updateUser({
          profile: {
            ...user?.profile,
            avatarUrl: result.avatarUrl,
          },
        });
        toast.success("Profile picture updated successfully");
      }
      setIsUploading(false);
    } catch (error) {
      console.error("Error uploading profile image:", error);
      toast.error("Failed to upload profile image. Please try again.");
      setIsUploading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeAvatarPreview = () => {
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case "Free":
        return "bg-gray-500";
      case "Starter":
        return "bg-blue-500";
      case "Pro":
        return "bg-purple-500";
      case "Business":
        return "bg-amber-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">My Profile</CardTitle>
        <CardDescription>
          Manage your account information and subscription details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="relative group">
              <Avatar className="h-24 w-24 border-2 border-primary/20">
                <AvatarImage
                  src={previewUrl || user?.profile?.avatarUrl}
                  alt={user?.profile?.firstName}
                />
                <AvatarFallback className="text-3xl font-bold">
                  {user?.profile?.firstName?.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {isEditing && (
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 rounded-full">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="image/*"
                    className="hidden"
                  />
                  <Button
                    size="icon"
                    variant="ghost"
                    className="rounded-full bg-white/20 hover:bg-white/30"
                    onClick={triggerFileInput}
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <Spinner size="sm" />
                    ) : (
                      <Camera className="h-5 w-5 text-white" />
                    )}
                  </Button>
                  {previewUrl && (
                    <Button
                      size="icon"
                      variant="ghost"
                      className="rounded-full bg-white/20 hover:bg-white/30 ml-2"
                      onClick={removeAvatarPreview}
                    >
                      <X className="h-5 w-5 text-white" />
                    </Button>
                  )}
                </div>
              )}
            </div>
            <div className="space-y-1">
              <h3 className="text-2xl font-bold">
                {user?.profile?.firstName} {user?.profile?.lastName}
              </h3>
              <p className="text-muted-foreground">{user.email}</p>
              <div className="flex gap-2 mt-2">
                <Badge className={getPlanBadgeColor(user.plan)}>
                  {user.plan} Plan
                </Badge>
                <Badge variant="outline">{user?.credit?.balance} Credits</Badge>
                {user.role === "ADMIN" && (
                  <Badge variant="destructive">Admin</Badge>
                )}
              </div>
            </div>
          </div>

          {isEditing ? (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleChange}
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleChange}
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  value={formData.email}
                  disabled={true}
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">
                  Email cannot be changed. Contact support for assistance.
                </p>
              </div>
              <div className="flex gap-2">
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Spinner size="sm" className="mr-2" /> Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
            </form>
          ) : (
            <Button onClick={() => setIsEditing(true)}>Edit Profile</Button>
          )}

          <div className="border-t pt-4">
            <SubscriptionPlansDisplay onUpgradeSuccess={refreshUserData} />
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t bg-muted/50 flex justify-between">
        <div className="text-xs text-muted-foreground">
          Member since{" "}
          {new Date(user?.createdAt || new Date()).toLocaleDateString()}
        </div>
        <Button variant="ghost" size="sm" className="text-destructive">
          Delete Account
        </Button>
      </CardFooter>
    </Card>
  );
};

export default UserProfile;
