import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { useApp } from "@/contexts/useApp";
import { cn } from "@/lib/utils";

interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  adminOnly?: boolean;
}

const DashboardNav = () => {
  const location = useLocation();
  const { user } = useApp();
  const [activeItem, setActiveItem] = useState("");

  // Update active item when location changes
  useEffect(() => {
    const path = location.pathname.split("/")[2] || "";
    setActiveItem(path);
  }, [location]);

  const isAdmin = user?.role === "ADMIN";

  // Define navigation items
  const navItems: NavItem[] = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <rect width="7" height="9" x="3" y="3" rx="1" />
          <rect width="7" height="5" x="14" y="3" rx="1" />
          <rect width="7" height="9" x="14" y="12" rx="1" />
          <rect width="7" height="5" x="3" y="16" rx="1" />
        </svg>
      ),
    },
//     {
//       title: "Video Generation",
//       href: "/dashboard/video-generation",
//       icon: (
//         <svg
//           xmlns="http://www.w3.org/2000/svg"
//           viewBox="0 0 24 24"
//           fill="none"
//           stroke="currentColor"
//           strokeWidth="2"
//           strokeLinecap="round"
//           strokeLinejoin="round"
//           className="h-5 w-5"
//         >
//           <polygon points="23 7 16 12 23 17 23 7" />
//           <rect width="15" height="14" x="1" y="5" rx="2" ry="2" />
//         </svg>
//       ),
//     },
//     {
//       title: "Image Generation",
//       href: "/dashboard/image-generation",
//       icon: (
//         <svg
//           xmlns="http://www.w3.org/2000/svg"
//           viewBox="0 0 24 24"
//           fill="none"
//           stroke="currentColor"
//           strokeWidth="2"
//           strokeLinecap="round"
//           strokeLinejoin="round"
//           className="h-5 w-5"
//         >
//           <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
//           <circle cx="9" cy="9" r="2" />
//           <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
//         </svg>
//       ),
//     },
    {
      title: "Background Removal",
      href: "/dashboard/background-removal",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <path d="M21.64 3.64A1.5 1.5 0 0 0 20.5 3h-17A1.5 1.5 0 0 0 2 4.5v15A1.5 1.5 0 0 0 3.5 21h17a1.5 1.5 0 0 0 1.5-1.5v-15c0-.39-.15-.74-.36-1Z" />
          <path d="m4.5 3 5 3-5 3" />
        </svg>
      ),
    },
    {
      title: "Virtual Try-On",
      href: "/dashboard/virtual-try-on",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <path d="M6.56 22a2 2 0 0 1-1.72-1L1.5 13.5a2 2 0 0 1 .51-2.17l7.08-6.9a2 2 0 0 1 2.77 0l7.08 6.9a2 2 0 0 1 .51 2.17l-3.34 7.5a2 2 0 0 1-1.72 1z" />
          <path d="M12 12a2 2 0 0 0-2 2c0 2 2 3 2 3s2-1 2-3a2 2 0 0 0-2-2Z" />
        </svg>
      ),
    },
    {
      title: "My Content",
      href: "/dashboard/my-content",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
          <circle cx="12" cy="7" r="4" />
        </svg>
      ),
    },
    {
      title: "Credits",
      href: "/dashboard/credits",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <circle cx="12" cy="12" r="10" />
          <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
          <path d="M12 18V6" />
        </svg>
      ),
    },
    {
      title: "Subscription",
      href: "/dashboard/subscription",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
          <path d="M12 18V6" />
        </svg>
      ),
    },
    {
      title: "Profile",
      href: "/dashboard/profile",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
          <circle cx="12" cy="7" r="4" />
        </svg>
      ),
    },
    {
      title: "Admin Dashboard",
      href: "/admin",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-5 w-5"
        >
          <path d="M12 20h9" />
          <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
        </svg>
      ),
      adminOnly: true,
    },
  ];

  // Filter items based on user role
  const filteredNavItems = navItems.filter(
    (item) => !item.adminOnly || isAdmin
  );

  return (
    <nav className="space-y-1">
      {filteredNavItems.map((item) => {
        const isActive =
          (item.href === "/dashboard" && activeItem === "") ||
          (item.href !== "/dashboard" && item.href.includes(activeItem));

        return (
          <Link
            key={item.href}
            to={item.href}
            className={cn(
              "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
              isActive
                ? "bg-primary/10 text-primary"
                : "text-muted-foreground hover:bg-muted hover:text-foreground"
            )}
          >
            <span className="mr-3">{item.icon}</span>
            {item.title}
          </Link>
        );
      })}
    </nav>
  );
};

export default DashboardNav;
