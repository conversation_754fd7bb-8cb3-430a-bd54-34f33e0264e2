import { useState } from "react";

const DashboardCommunity = () => {
  const [activeTab, setActiveTab] = useState("All");

  const tabs = [
    "All",
    "Background Removal",
    "Virtual Try On",
    "SpeedPainting",
  ];

  // Sample images with different heights for masonry effect
  const images = [
    { id: 1, src: "https://picsum.photos/300/400?random=1", height: "h-64" },
    { id: 2, src: "https://picsum.photos/300/300?random=2", height: "h-48" },
    { id: 3, src: "https://picsum.photos/300/500?random=3", height: "h-80" },
    { id: 6, src: "https://picsum.photos/300/320?random=6", height: "h-52" },
    { id: 7, src: "https://picsum.photos/300/480?random=7", height: "h-76" },
    { id: 8, src: "https://picsum.photos/300/360?random=8", height: "h-60" },
    { id: 9, src: "https://picsum.photos/300/420?random=9", height: "h-68" },
    { id: 10, src: "https://picsum.photos/300/380?random=10", height: "h-64" },
    { id: 11, src: "https://picsum.photos/300/340?random=11", height: "h-56" },
    { id: 12, src: "https://picsum.photos/300/460?random=12", height: "h-74" },
    { id: 13, src: "https://picsum.photos/300/480?random=7", height: "h-76" },
  ];

  return (
    <div className=" text-white bg-[#1A1A1A]">
      <div className="mx-auto max-w-7xl p-4">
        {/* Tabs */}
        <div className="flex space-x-8 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`pb-4 px-1 font-medium transition-colors duration-200 cursor-pointer ${
                activeTab === tab
                  ? "text-white border-b-2 border-blue-500"
                  : "text-gray-400 hover:text-gray-300"
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Image Grid - Masonry Layout with Vertical Scrolling */}
        <div className="relative max-h-[calc(90vh-12rem)] overflow-y-auto overflow-x-hidden">
          <div className="columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4 space-y-4">
            {images.map((image) => (
              <div
                key={image.id}
                className="break-inside-avoid mb-4 group cursor-pointer"
              >
                <div className="relative overflow-hidden rounded-lg bg-gray-800 hover:scale-105 transition-transform duration-300">
                  <img
                    src={image.src}
                    alt={`Community image ${image.id}`}
                    className={`w-full ${image.height} object-cover`}
                    loading="lazy"
                  />
                </div>
              </div>
            ))}
          </div>
          <img src="/png/dashboard_bottom_shadow.png" className="absolute bottom-0 w-full" />
        </div>
      </div>
    </div>
  );
};

export default DashboardCommunity;

// import React, { useState } from "react";

// const DashboardCommunity = () => {
//   const [activeTab, setActiveTab] = useState("All");

//   const tabs = [
//     "All",
//     "Talking Avatar",
//     "Face Swap",
//     "Talking Photo",
//     "Image Generator",
//   ];

//   // Sample images with different heights for masonry effect
//   //   const images = [
//   //     { id: 1, src: "/png/horse_generated.png", height: "h-64" },
//   //     { id: 2, src: "/png/horse_generated.png", height: "h-48" },
//   //     { id: 3, src: "/png/horse_generated.png", height: "h-80" },
//   //     { id: 4, src: "/png/horse_generated.png", height: "h-56" },
//   //     { id: 5, src: "/png/horse_generated.png", height: "h-72" },
//   //     { id: 6, src: "/png/horse_generated.png", height: "h-52" },
//   //     { id: 7, src: "/png/horse_generated.png", height: "h-76" },
//   //     { id: 8, src: "/png/horse_generated.png", height: "h-60" },
//   //     { id: 9, src: "/png/horse_generated.png", height: "h-68" },
//   //     { id: 9, src: "/png/horse_generated.png", height: "h-68" },
//   //   ];
//   const images = [
//     { id: 1, src: "https://picsum.photos/300/400?random=1", height: "h-64" },
//     { id: 2, src: "https://picsum.photos/300/300?random=2", height: "h-48" },
//     { id: 3, src: "https://picsum.photos/300/500?random=3", height: "h-80" },
//     { id: 6, src: "https://picsum.photos/300/320?random=6", height: "h-52" },
//     { id: 7, src: "https://picsum.photos/300/480?random=7", height: "h-76" },
//     { id: 8, src: "https://picsum.photos/300/360?random=8", height: "h-60" },
//     { id: 9, src: "https://picsum.photos/300/420?random=9", height: "h-68" },
//     { id: 10, src: "https://picsum.photos/300/380?random=10", height: "h-64" },
//     { id: 11, src: "https://picsum.photos/300/340?random=11", height: "h-56" },
//     { id: 12, src: "https://picsum.photos/300/460?random=12", height: "h-74" },
//   ];

//   return (
//     <div className="min-h-screen text-white">
//       <div className="mx-auto">
//         {/* Tabs */}
//         <div className="flex space-x-8 mb-8 border-b border-gray-700">
//           {tabs.map((tab) => (
//             <button
//               key={tab}
//               onClick={() => setActiveTab(tab)}
//               className={`pb-4 px-1 font-medium transition-colors duration-200 ${
//                 activeTab === tab
//                   ? "text-white border-b-2 border-blue-500"
//                   : "text-gray-400 hover:text-gray-300"
//               }`}
//             >
//               {tab}
//             </button>
//           ))}
//         </div>

//         {/* Image Grid - Masonry Layout */}
//         <div className="columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4 space-y-4">
//           {images.map((image) => (
//             <div
//               key={image.id}
//               className="break-inside-avoid mb-4 group cursor-pointer"
//             >
//               <div className="relative overflow-hidden rounded-lg bg-gray-800 hover:scale-105 transition-transform duration-300">
//                 <img
//                   src={image.src}
//                   alt={`Community image ${image.id}`}
//                   className={`w-full ${image.height} object-cover`}
//                   loading="lazy"
//                 />
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default DashboardCommunity;
