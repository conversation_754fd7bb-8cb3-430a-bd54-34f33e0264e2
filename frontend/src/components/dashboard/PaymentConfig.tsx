import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { Spinner } from "@/components/ui/spinner";
import PaymentConfigService from "@/services/paymentConfig.service";

interface PaymentMethod {
  id: string;
  name: string;
  enabled: boolean;
  icon: string;
}

const PaymentConfig = () => {
  const [loading, setLoading] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: "stripe",
      name: "<PERSON>e",
      enabled: true,
      icon: "/icons/stripe.svg",
    },
    {
      id: "paypal",
      name: "PayPal",
      enabled: false,
      icon: "/icons/paypal.svg",
    },
  ]);

  const [stripeConfig, setStripeConfig] = useState({
    apiKey: "",
    secretKey: "",
    webhookSecret: "",
    isLive: false,
  });

  const [paypalConfig, setPaypalConfig] = useState({
    clientId: "",
    clientSecret: "",
    webhookId: "",
    isLive: false,
  });
  
  // Fetch payment configuration on component mount
  useEffect(() => {
    fetchPaymentConfig();
  }, []);
  
  const fetchPaymentConfig = async () => {
    try {
      setLoading(true);
      const config = await PaymentConfigService.getConfig();
      
      // Update payment methods
      setPaymentMethods([
        {
          id: "stripe",
          name: "Stripe",
          enabled: config.stripe.enabled,
          icon: "/icons/stripe.svg",
        },
        {
          id: "paypal",
          name: "PayPal",
          enabled: config.paypal.enabled,
          icon: "/icons/paypal.svg",
        },
      ]);
      
      // Update Stripe config
      setStripeConfig({
        apiKey: config.stripe.apiKey,
        secretKey: config.stripe.secretKey,
        webhookSecret: config.stripe.webhookSecret,
        isLive: config.stripe.isLive,
      });
      
      // Update PayPal config
      setPaypalConfig({
        clientId: config.paypal.clientId,
        clientSecret: config.paypal.clientSecret,
        webhookId: config.paypal.webhookId,
        isLive: config.paypal.isLive,
      });
    } catch (error) {
      toast.error("Failed to load payment configuration");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePaymentMethod = async (id: string) => {
    try {
      const method = paymentMethods.find((m) => m.id === id);
      if (!method) return;
      
      const newEnabledState = !method.enabled;
      
      // Update the API
      await PaymentConfigService.togglePaymentMethod(
        id as 'stripe' | 'paypal',
        newEnabledState
      );
      
      // Update local state
      setPaymentMethods(
        paymentMethods.map((method) =>
          method.id === id ? { ...method, enabled: newEnabledState } : method
        )
      );
      
      toast.success(`${method.name} ${newEnabledState ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      toast.error(`Failed to toggle ${id} payment method`);
      console.error(error);
    }
  };

  const handleSaveStripeConfig = async () => {
    try {
      setLoading(true);
      await PaymentConfigService.updateStripeConfig({
        ...stripeConfig,
        enabled: paymentMethods.find(m => m.id === 'stripe')?.enabled || false
      });
      toast.success("Stripe configuration saved successfully");
    } catch (error) {
      toast.error("Failed to save Stripe configuration");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSavePaypalConfig = async () => {
    try {
      setLoading(true);
      await PaymentConfigService.updatePayPalConfig({
        ...paypalConfig,
        enabled: paymentMethods.find(m => m.id === 'paypal')?.enabled || false
      });
      toast.success("PayPal configuration saved successfully");
    } catch (error) {
      toast.error("Failed to save PayPal configuration");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleTestConnection = async (method: 'stripe' | 'paypal') => {
    try {
      setLoading(true);
      const result = await PaymentConfigService.testConnection(method);
      if (result.success) {
        toast.success(result.message || `${method.charAt(0).toUpperCase() + method.slice(1)} connection successful`);
      } else {
        toast.error(result.message || `${method.charAt(0).toUpperCase() + method.slice(1)} connection failed`);
      }
    } catch (error) {
      toast.error(`Failed to test ${method} connection`);
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Payment Configuration</h2>
        {loading && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Spinner size="sm" />
            <span>Loading...</span>
          </div>
        )}
      </div>
      <div>
        <p className="text-muted-foreground">
          Configure your payment gateways and settings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>
            Enable or disable payment methods for your application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className="flex items-center justify-between space-x-4 rounded-md border p-4"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 flex items-center justify-center rounded-md border">
                    <img
                      src={method.icon}
                      alt={method.name}
                      className="w-8 h-8 object-contain"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium leading-none">
                      {method.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {method.enabled ? "Enabled" : "Disabled"}
                    </p>
                  </div>
                </div>
                <Switch
                  checked={method.enabled}
                  onCheckedChange={() => handleTogglePaymentMethod(method.id)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="stripe" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="stripe">Stripe</TabsTrigger>
          <TabsTrigger value="paypal">PayPal</TabsTrigger>
        </TabsList>
        <TabsContent value="stripe" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Stripe Configuration</CardTitle>
              <CardDescription>
                Configure your Stripe payment gateway settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="stripe-api-key">API Key (Publishable Key)</Label>
                <Input
                  id="stripe-api-key"
                  type="password"
                  placeholder="pk_test_..."
                  value={stripeConfig.apiKey}
                  onChange={(e) =>
                    setStripeConfig({ ...stripeConfig, apiKey: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="stripe-secret-key">Secret Key</Label>
                <Input
                  id="stripe-secret-key"
                  type="password"
                  placeholder="sk_test_..."
                  value={stripeConfig.secretKey}
                  onChange={(e) =>
                    setStripeConfig({ ...stripeConfig, secretKey: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="stripe-webhook-secret">Webhook Secret</Label>
                <Input
                  id="stripe-webhook-secret"
                  type="password"
                  placeholder="whsec_..."
                  value={stripeConfig.webhookSecret}
                  onChange={(e) =>
                    setStripeConfig({
                      ...stripeConfig,
                      webhookSecret: e.target.value,
                    })
                  }
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="stripe-mode"
                  checked={stripeConfig.isLive}
                  onCheckedChange={(checked: boolean) =>
                    setStripeConfig({ ...stripeConfig, isLive: checked })
                  }
                />
                <Label htmlFor="stripe-mode">
                  {stripeConfig.isLive ? "Live Mode" : "Test Mode"}
                </Label>
              </div>
              <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                <Button 
                  onClick={handleSaveStripeConfig} 
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Saving...
                    </>
                  ) : (
                    "Save Configuration"
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => handleTestConnection('stripe')}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Testing...
                    </>
                  ) : (
                    "Test Connection"
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="paypal" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>PayPal Configuration</CardTitle>
              <CardDescription>
                Configure your PayPal payment gateway settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="paypal-client-id">Client ID</Label>
                <Input
                  id="paypal-client-id"
                  type="password"
                  placeholder="Client ID"
                  value={paypalConfig.clientId}
                  onChange={(e) =>
                    setPaypalConfig({
                      ...paypalConfig,
                      clientId: e.target.value,
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="paypal-client-secret">Client Secret</Label>
                <Input
                  id="paypal-client-secret"
                  type="password"
                  placeholder="Client Secret"
                  value={paypalConfig.clientSecret}
                  onChange={(e) =>
                    setPaypalConfig({
                      ...paypalConfig,
                      clientSecret: e.target.value,
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="paypal-webhook-id">Webhook ID</Label>
                <Input
                  id="paypal-webhook-id"
                  type="text"
                  placeholder="Webhook ID"
                  value={paypalConfig.webhookId}
                  onChange={(e) =>
                    setPaypalConfig({
                      ...paypalConfig,
                      webhookId: e.target.value,
                    })
                  }
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="paypal-mode"
                  checked={paypalConfig.isLive}
                  onCheckedChange={(checked: boolean) =>
                    setPaypalConfig({ ...paypalConfig, isLive: checked })
                  }
                />
                <Label htmlFor="paypal-mode">
                  {paypalConfig.isLive ? "Live Mode" : "Sandbox Mode"}
                </Label>
              </div>
              <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                <Button 
                  onClick={handleSavePaypalConfig} 
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Saving...
                    </>
                  ) : (
                    "Save Configuration"
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => handleTestConnection('paypal')}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Testing...
                    </>
                  ) : (
                    "Test Connection"
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PaymentConfig;
