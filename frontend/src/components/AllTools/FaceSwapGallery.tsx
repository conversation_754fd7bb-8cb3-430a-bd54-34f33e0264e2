import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@radix-ui/react-dropdown-menu";

interface GalleryItem {
  id: number;
  type: "video" | "image";
  src: string;
  title: string;
  thumbnail: string;
}

const FaceSwapGallery: React.FC = () => {
  const [trendingFilter, setTrendingFilter] = useState<string>("Trending");
  const [categoryFilter, setCategoryFilter] = useState<string>("All");

  const galleryItems: GalleryItem[] = [
    {
      id: 1,
      type: "image",
      src: "https://picsum.photos/300/400?random=1",
      title: "Face Swap Video 1",
      thumbnail: "/images/face1.jpg",
    },
    {
      id: 2,
      type: "video",
      src: "/videos/faceswap2.mp4",
      title: "Animated Face Swap",
      thumbnail: "/images/anime.jpg",
    },
    {
      id: 3,
      type: "image",
      src: "/images/faceswap3.jpg",
      title: "Portrait Face Swap",
      thumbnail: "/images/portrait.jpg",
    },
    {
      id: 4,
      type: "image",
      src: "/images/faceswap4.jpg",
      title: "Creative Face Swap",
      thumbnail: "/images/balloons.jpg",
    },
    {
      id: 5,
      type: "video",
      src: "/videos/faceswap5.mp4",
      title: "Dance Face Swap",
      thumbnail: "/images/dance.jpg",
    },
    {
      id: 6,
      type: "image",
      src: "/images/faceswap6.jpg",
      title: "Fashion Face Swap",
      thumbnail: "/images/fashion.jpg",
    },
    {
      id: 7,
      type: "video",
      src: "/videos/faceswap7.mp4",
      title: "Movie Scene Face Swap",
      thumbnail: "/images/movie.jpg",
    },
    {
      id: 8,
      type: "image",
      src: "/images/faceswap8.jpg",
      title: "Art Style Face Swap",
      thumbnail: "/images/art.jpg",
    },
  ];

  const handleItemClick = (item: GalleryItem): void => {
    console.log(`Selected ${item.type}:`, item.title);
  };

  const trendingOptions = ["Trending", "Recent", "Popular", "Featured"];
  const categoryOptions = ["All", "Videos", "Images", "Portraits", "Creative"];

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col gap-5 mb-8">
          <h1 className="text-3xl font-inter font-semibold text-white">
            Face Swap Gallery
          </h1>

          {/* Filter Dropdowns */}
          <div className="flex items-center gap-4">
            {/* Trending Filter */}
            <div className="w-40 relative">
              <DropdownMenu>
                <DropdownMenuTrigger className="w-full bg-white/10 border border-gray-600 text-white px-4 py-2 rounded-lg focus:outline-none focus:border-purple-500 flex items-center justify-between">
                  {trendingFilter}
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-gray-800 border border-gray-600 rounded-lg mt-1 z-[1000] min-w-[160px]">
                  {trendingOptions.map((option) => (
                    <DropdownMenuItem
                      key={option}
                      onSelect={() => setTrendingFilter(option)}
                      className="px-4 py-2 text-white hover:bg-gray-700 cursor-pointer focus:bg-gray-700 rounded-md"
                    >
                      {option}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Category Filter */}
            <div className="w-40 relative">
              <DropdownMenu>
                <DropdownMenuTrigger className="w-full bg-white/10 border border-gray-600 text-white px-4 py-2 rounded-lg focus:outline-none focus:border-purple-500 flex items-center justify-between">
                  {categoryFilter}
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-gray-800 border border-gray-600 rounded-lg mt-1 z-[1000] min-w-[160px]">
                  {categoryOptions.map((option) => (
                    <DropdownMenuItem
                      key={option}
                      onSelect={() => setCategoryFilter(option)}
                      className="px-4 py-2 text-white hover:bg-gray-700 cursor-pointer focus:bg-gray-700 rounded-md"
                    >
                      {option}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {galleryItems.map((item) => (
            <div
              key={item.id}
              onClick={() => handleItemClick(item)}
              className="relative group cursor-pointer rounded-xl overflow-hidden bg-gray-800 hover:transform hover:scale-105 transition-all duration-300"
            >
              {/* Media Container */}
              <div className="relative aspect-[3/4] overflow-hidden">
                {/* Thumbnail Image or Video */}
                {item.type === "image" ? (
                  <img
                    src={item.thumbnail}
                    alt={item.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src =
                        "https://picsum.photos/300/400?random=" + item.id; // Dummy image
                    }}
                  />
                ) : (
                  <video
                    src={item.src}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLVideoElement;
                      target.poster =
                        "https://picsum.photos/300/400?random=" + item.id; // Dummy video placeholder
                    }}
                  />
                )}

                {/* Type Badge */}
                <div className="absolute top-3 left-3">
                  <span
                    className={`px-2 py-1 text-sm font-medium rounded-sm bg-gray-600/60`}
                  >
                    {item.type}
                  </span>
                </div>
              </div>

              {/* Title (Optional - shown on hover) */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-white text-sm font-medium truncate">
                  {item.title}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FaceSwapGallery;
