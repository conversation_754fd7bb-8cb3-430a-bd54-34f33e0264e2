import { useEffect, useState } from "react";
import {
  X,
  // Video,
} from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  BackgroundRemoverIcon,
  CreditIcon,
  HomeIconSidebar,
  PaymentIcon,
  ProfileIcon,
  SpeedpaintingIcon,
  VirtualTryOnIcon,
} from "@/lib/icons";

interface UserSidebarProps {
  activeItem: string;
  setActiveItem: (item: string) => void;
  isMobileMenuOpen: boolean;
  setIsMobileMenuOpen: (isOpen: boolean) => void;
}

const UserSidebar = ({
  activeItem,
  setActiveItem,
  isMobileMenuOpen,
  setIsMobileMenuOpen,
}: UserSidebarProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isHovered, setIsHovered] = useState(false);

  const generalItems = [
    {
      name: "Home",
      href: "/dashboard",
      icon: HomeIconSidebar,
    },
  ];

  const aiToolsItems = [
    {
      name: "Background Remover",
      href: "/ai-tool/background-remover",
      icon: BackgroundRemoverIcon,
    },
    {
      name: "Virtual Try-On",
      href: "/ai-tool/virtual-try-on",
      icon: VirtualTryOnIcon,
    },
    {
      name: "Speedpainting",
      href: "/ai-tool/speedpainting",
      icon: SpeedpaintingIcon,
    },
  ];

  const accountItems = [
    {
      name: "Credit",
      href: "/account/credit",
      icon: CreditIcon,
    },
    {
      name: "Profile",
      href: "/dashboard/profile",
      icon: ProfileIcon,
    },
    {
      name: "Payments",
      href: "/account/transaction",
      icon: PaymentIcon,
    },
  ];

  // Set the active item based on the current URL when the component mounts
  useEffect(() => {
    const currentPath = location.pathname;
    const allItems = [...generalItems, ...aiToolsItems, ...accountItems];
    const matchingItem = allItems.find((item) => currentPath === item.href);
    if (matchingItem) {
      setActiveItem(matchingItem.name);
    }
  }, [location.pathname, setActiveItem]);

  const handleItemClick = (itemName: string, href: string) => {
    setActiveItem(itemName);
    if (setIsMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
    navigate(href);
  };

  const SidebarContent = ({ isDesktop = false }) => (
    <>
      {/* Logo */}
      <div className="p-3 flex justify-center">
        <div className="flex items-center">
          {isDesktop && !isHovered ? (
            <img src="/png/about_logo.png" className="w-[150px]" />
          ) : (
            <img src="/png/new_miragic_logo.png" className="w-[160px]" />
          )}
        </div>
      </div>

      {/* Navigation and User Info (Scrollable) */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {/* General Section */}
        <div className={`${isDesktop && !isHovered ? "px-3" : "px-6"} mb-4`}>
          {(!isDesktop || isHovered) && (
            <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
              GENERAL
            </h3>
          )}
          <nav
            className={`space-y-1 ${
              !isHovered ? "pb-6 border-b-2 border-gray-500" : ""
            }`}
          >
            {generalItems.map((item) => {
              const isActive = activeItem === item.name;
              const activeColor = "#9855FF";
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name, item.href)}
                  className={`group relative w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                    isActive
                      ? `text-[${activeColor}]`
                      : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
                  } ${
                    isDesktop && !isHovered
                      ? "flex justify-center"
                      : "flex items-center space-x-3"
                  }`}
                  title={isDesktop && !isHovered ? item.name : undefined}
                >
                  <IconComponent color={isActive ? activeColor : "white"} />
                  {(!isDesktop || isHovered) && (
                    <span className="truncate">{item.name}</span>
                  )}

                  {/* Tooltip for collapsed state */}
                  {isDesktop && !isHovered && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* AI Tools Section */}
        <div
          className={`${isDesktop && !isHovered ? "px-3 pt-3" : "px-6"} mb-4`}
        >
          {(!isDesktop || isHovered) && (
            <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
              AI TOOLS
            </h3>
          )}
          <nav className="space-y-1">
            {aiToolsItems.map((item) => {
              const isActive = activeItem === item.name;
              const activeColor = "#9855FF";
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name, item.href)}
                  className={`group relative cursor-pointer w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                    isActive
                      ? `text-[${activeColor}]`
                      : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
                  } ${
                    isDesktop && !isHovered
                      ? "flex justify-center"
                      : "flex items-center space-x-3"
                  }`}
                  title={isDesktop && !isHovered ? item.name : undefined}
                >
                  <IconComponent color={isActive ? activeColor : "white"} />
                  {(!isDesktop || isHovered) && (
                    <span className="truncate">{item.name}</span>
                  )}

                  {/* Tooltip for collapsed state */}
                  {isDesktop && !isHovered && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Account Section */}
        <div
          className={`${isDesktop && !isHovered ? "px-3 pt-3" : "px-6"} mb-4`}
        >
          {(!isDesktop || isHovered) && (
            <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
              Account
            </h3>
          )}
          <nav className="space-y-1">
            {accountItems.map((item) => {
              const isActive = activeItem === item.name;
              const activeColor = "#9855FF";
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name, item.href)}
                  className={`group relative cursor-pointer w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                    isActive
                      ? `text-[${activeColor}]`
                      : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
                  } ${
                    isDesktop && !isHovered
                      ? "flex justify-center"
                      : "flex items-center space-x-3"
                  }`}
                  title={isDesktop && !isHovered ? item.name : undefined}
                >
                  <IconComponent color={isActive ? activeColor : "white"} />
                  {(!isDesktop || isHovered) && (
                    <span className="truncate">{item.name}</span>
                  )}

                  {/* Tooltip for collapsed state */}
                  {isDesktop && !isHovered && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Spacer */}
        <div className="h-6"></div>

        {/* User Info */}
        {/* <div className={`${isDesktop && !isHovered ? "px-1" : "px-2"}`}>
          <div
            className={`${
              isDesktop && !isHovered ? "max-w-[90%]" : "max-w-[96%]"
            } mx-auto bg-white/10 rounded-lg p-4`}
          >
            <div
              className={`flex items-center ${
                isDesktop && !isHovered ? "justify-center" : "space-x-3"
              }`}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold flex-shrink-0">
                JD
              </div>
              {(!isDesktop || isHovered) && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    John Doe
                  </p>
                  <p className="text-xs text-gray-400 truncate">
                    <EMAIL>
                  </p>
                </div>
              )}
            </div>
          </div>
        </div> */}

        {/* Extra Space at the Bottom */}
        <div className="h-6"></div>
      </div>
    </>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <aside
        className={`hidden lg:flex flex-col bg-[#1A1A1A] border-r border-gray-700 absolute z-50 transition-all duration-300 ease-in-out ${
          isHovered ? "w-64" : "w-20"
        } h-full`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <SidebarContent isDesktop={true} />
      </aside>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-50 bg-black/70 bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div
            className="fixed inset-y-0 left-0 w-64 bg-[#1A1A1A] border-r border-gray-700 overflow-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Mobile Close Button */}
            <div className="absolute top-2 right-2 z-10">
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-gray-400 hover:text-white rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <SidebarContent isDesktop={false} />
          </div>
        </div>
      )}
    </>
  );
};

export default UserSidebar;
