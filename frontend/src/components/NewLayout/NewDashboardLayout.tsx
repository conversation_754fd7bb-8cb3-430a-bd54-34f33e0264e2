import { useState } from "react";
import UserSidebar from "./UserSidebar";
import UserHeader from "./UserHeader";
import { Outlet } from "react-router-dom";

const NewDashboardLayout = () => {
  const [activeItem, setActiveItem] = useState("Home");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-900 text-white">
      {/* Spacer for Sidebar on Desktop */}
      <div className="hidden lg:block w-20" />

      {/* Sidebar Component */}
      <UserSidebar
        activeItem={activeItem}
        setActiveItem={setActiveItem}
        isMobileMenuOpen={isMobileMenuOpen}
        setIsMobileMenuOpen={setIsMobileMenuOpen}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden bg-[#131619]">
        {/* Header Component */}
        <UserHeader
          //   activeItem={activeItem}
          setIsMobileMenuOpen={setIsMobileMenuOpen}
        />

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-4 lg:p-6 bg-[#1A1A1A]">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default NewDashboardLayout;
