import { User, LogOut } from "lucide-react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useContext, useCallback } from "react";
import { HeaderConfigContext } from "@/contexts/AdminHeaderContext";
import { useNavigate } from "react-router-dom";
import { useApp } from "@/contexts/useApp";

interface AdminHeaderProps {
  setIsMobileMenuOpen: (isOpen: boolean) => void;
}

const AdminHeader = ({ setIsMobileMenuOpen }: AdminHeaderProps) => {
  const { title, description, content } = useContext(HeaderConfigContext);
  const navigate = useNavigate();
  const { logout } = useApp();

  // Memoize event handlers to prevent re-creation on each render
  const handleProfile = useCallback(() => {
    console.log("Navigate to profile");
  }, []);

  const handleLogout = useCallback(() => {
    console.log("Logout user");
    logout();
    navigate("/");
  }, [logout, navigate]);

  return (
    <header className="bg-[#1A1A1A] px-4 py-4 flex items-center justify-between">
      {/* Left Section - Title and Description */}
      <div className="flex flex-col">
        <h1 className="text-white text-2xl font-semibold">{title || ""}</h1>
        <p className="text-gray-400 text-sm mt-1">{description || ""}</p>
      </div>

      {/* Right Section - Content and Avatar with Dropdown */}
      <div className="flex items-center space-x-4">
        {content}
        <DropdownMenu.Root>
          <DropdownMenu.Trigger asChild>
            <div className="w-8 h-8 rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all">
              <img
                src="/png/avatar1.png"
                alt="User Avatar"
                className="w-full h-full object-cover"
              />
            </div>
          </DropdownMenu.Trigger>

          <DropdownMenu.Portal>
            <DropdownMenu.Content
              className="min-w-[160px] bg-[#2A2A2A] rounded-lg p-1 shadow-lg border border-gray-600 z-50"
              sideOffset={5}
              align="end"
            >
              <DropdownMenu.Item
                className="flex items-center gap-2 px-3 py-2 text-sm text-gray-200 hover:bg-gray-700 hover:text-white rounded cursor-pointer outline-none"
                onClick={handleProfile}
              >
                <User className="w-4 h-4" />
                Profile
              </DropdownMenu.Item>

              <DropdownMenu.Separator className="h-px bg-gray-600 my-1" />

              <DropdownMenu.Item
                className="flex items-center gap-2 px-3 py-2 text-sm text-gray-200 hover:bg-red-600 hover:text-white rounded cursor-pointer outline-none"
                onClick={handleLogout}
              >
                <LogOut className="w-4 h-4" />
                Logout
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Portal>
        </DropdownMenu.Root>

        {/* Mobile Menu Button - Hidden on larger screens */}
        <button
          type="button"
          onClick={() => setIsMobileMenuOpen(true)}
          className="lg:hidden p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16m-7 6h7"
            />
          </svg>
        </button>
      </div>
    </header>
  );
};

export default AdminHeader;
