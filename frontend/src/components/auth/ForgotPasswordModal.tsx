import React, { useState, useRef, useEffect } from "react";
import { X, Mail, Lock, Eye, EyeOff } from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack?: () => void;
}

type Step = "forgot" | "otp" | "changePassword";

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  isOpen,
  onClose,
  onBack,
}) => {
  const [currentStep, setCurrentStep] = useState<Step>("forgot");
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState(["", "", "", ""]);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const otpRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setCurrentStep("forgot");
      setEmail("");
      setOtp(["", "", "", ""]);
      setNewPassword("");
      setConfirmPassword("");
      setIsLoading(false);
    }
  }, [isOpen]);

  const handleSendOTP = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Sending OTP to:", email);
    setCurrentStep("otp");
  };

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 3) {
      otpRefs[index + 1].current?.focus();
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      otpRefs[index - 1].current?.focus();
    }
  };

  const handleVerifyOTP = async () => {
    const otpCode = otp.join("");
    if (otpCode.length !== 4) return;

    setIsLoading(true);
    console.log("Verifying OTP:", otpCode);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setCurrentStep("changePassword");
    }, 2000);
  };

  const handleResetPassword = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Resetting password");
    // Handle password reset logic here
    onClose();
  };

  const handleResendCode = () => {
    console.log("Resending OTP code");
    setOtp(["", "", "", ""]);
    // Focus first input
    otpRefs[0].current?.focus();
  };

  const handleBackClick = () => {
    if (currentStep === "otp") {
      setCurrentStep("forgot");
    } else if (currentStep === "changePassword") {
      setCurrentStep("otp");
    } else if (onBack) {
      onBack();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-primary/90 bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-[#1A1D21F5] rounded-2xl w-full max-w-md relative max-h-[90vh] flex flex-col">
        <button
          onClick={onBack}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10"
        >
          <X size={20} />
        </button>

        <div className="overflow-y-auto p-8">
          {/* Forgot Password Step */}
          {currentStep === "forgot" && (
            <>
              <div className="mb-6 mt-14">
                <h2 className="text-white text-2xl font-semibold mb-3">
                  forgot password?
                </h2>
                <p className="text-gray-400 text-sm">
                  No problem! Just fill in the email below and we'll send you
                  password reset instructions!
                </p>
              </div>

              <form onSubmit={handleSendOTP} className="space-y-6">
                <div>
                  <label className="block text-gray-200 mb-3">
                    *Your email
                  </label>
                  <div className="relative">
                    <Mail
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      size={18}
                    />
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                      required
                    />
                  </div>
                </div>

                <div className="flex gap-3">
                  <ShadowButton
                    onClick={handleBackClick}
                    className="flex-1 !bg-gray-600 hover:!bg-gray-700 py-3 px-4 rounded-lg font-medium transition-colors"
                  >
                    Back
                  </ShadowButton>
                  <ShadowButton className="flex-1 py-3 px-4">
                    Send OTP
                  </ShadowButton>
                </div>
              </form>
            </>
          )}

          {/* OTP Verification Step */}
          {currentStep === "otp" && (
            <>
              <div className="mb-8 mt-14">
                <h2 className="text-white text-2xl font-semibold mb-3">
                  Enter OTP code
                </h2>
                <p className="text-gray-400 text-sm">
                  Enter an OTP we sent email to {email}
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex justify-between">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      ref={otpRefs[index]}
                      type="text"
                      value={digit}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleOtpKeyDown(index, e)}
                      className="w-16 h-16 bg-white/10 border border-gray-700 rounded-lg text-center text-white text-lg font-medium focus:outline-none focus:border-purple-500 transition-colors"
                      maxLength={1}
                    />
                  ))}
                </div>

                <div className="">
                  <button
                    onClick={handleResendCode}
                    className="cursor-pointer text-gray-300 hover:text-gray-200 transition-colors text-base font-inter border-b"
                  >
                    Resend code
                  </button>
                </div>

                {isLoading && (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
                    <span className="ml-2 text-gray-400 text-sm">
                      Verifying OTP...
                    </span>
                  </div>
                )}

                <div className="flex gap-3">
                  <ShadowButton
                    onClick={handleBackClick}
                    className="flex-1 !bg-gray-600 hover:!bg-gray-700 py-3 px-4 rounded-lg"
                    disabled={isLoading}
                  >
                    Back
                  </ShadowButton>
                  <ShadowButton
                    className="flex-1 py-3 px-4"
                    onClick={handleVerifyOTP}
                    disabled={otp.join("").length !== 4 || isLoading}
                  >
                    {isLoading ? "Verifying..." : "Verify OTP"}
                  </ShadowButton>
                </div>
              </div>
            </>
          )}

          {/* Change Password Step */}
          {currentStep === "changePassword" && (
            <>
              <div className="mb-8 mt-14">
                <h2 className="text-white text-2xl font-semibold mb-3">
                  Change Password
                </h2>
              </div>

              <form onSubmit={handleResetPassword} className="space-y-6">
                <div>
                  <label className="block text-gray-300 mb-3">
                    * New Password
                  </label>
                  <div className="relative">
                    <Lock
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      size={18}
                    />
                    <input
                      type={showNewPassword ? "text" : "password"}
                      placeholder="Password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                    >
                      {showNewPassword ? (
                        <EyeOff size={18} />
                      ) : (
                        <Eye size={18} />
                      )}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-gray-300 mb-3">
                    * Confirm Password
                  </label>
                  <div className="relative">
                    <Lock
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      size={18}
                    />
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                      required
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                    >
                      {showConfirmPassword ? (
                        <EyeOff size={18} />
                      ) : (
                        <Eye size={18} />
                      )}
                    </button>
                  </div>
                </div>

                <ShadowButton className="w-full py-3 px-4">
                  Reset Password
                </ShadowButton>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordModal;
