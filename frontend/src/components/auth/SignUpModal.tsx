import React, { useState } from "react";
import { X, Mail, Lock, Eye, EyeOff, User } from "lucide-react";
import { AppleIcon, GoogleIcon } from "@/lib/icons";
import ShadowButton from "@/components//ui/shadowButton";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import { useNavigate } from "react-router-dom";
import type { AxiosError } from "axios";

interface SignUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  onClickLoginModal?: () => void;
}

const SignUpModal: React.FC<SignUpModalProps> = ({
  isOpen,
  onClose,
  onClickLoginModal,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const navigate = useNavigate();
  const { register, isLoading } = useApp();
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [error, setError] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Basic validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (formData.password.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    try {
      // Use the register function from AppContext
      const response = await register(
        formData.firstName,
        formData.lastName,
        formData.email,
        formData.password
      );

      console.log(response);
      if (response?.user?.emailVerified) {
        navigate("/");
        onClose();
        toast.success("Account created successfully! Welcome to Miragic-AI.");
      }
    } catch (error) {
      console.error("Registration error:", error);
      const axiosError = error as AxiosError<{
        error?: {
          message?: string;
        };
      }>;
      setError(
        axiosError.response?.data?.error?.message ||
          "Registration failed. Please try again."
      );
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-primary/90 bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-[#1A1D21F5] rounded-2xl w-full max-w-md relative max-h-[90vh] flex flex-col">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10"
        >
          <X size={20} />
        </button>

        <div className="overflow-y-auto p-8">
          {/* Logo and title */}
          <div className="">
            <img src="/png/new_miragic_logo.png" className="w-[120px]" />
            <h2 className="text-white text-xl font-semibold mt-8 mb-5">
              Welcome to MiragicAI
            </h2>
          </div>

          {/* Social login buttons */}
          <div className="space-y-3 mb-6">
            <button className="w-full bg-white/10 cursor-pointer hover:bg-gray-700 text-white py-3 px-4 rounded-lg flex items-center justify-center gap-4 transition-colors border border-gray-700">
              <GoogleIcon />
              Google Account
            </button>
            <button className="w-full bg-white/10 cursor-pointer hover:bg-gray-700 text-white py-3 px-4 rounded-lg flex items-center justify-center gap-4 transition-colors border border-gray-700">
              <AppleIcon />
              Apple Account
            </button>
          </div>

          <div className="relative flex items-center my-8">
            <div className="flex-grow border-t border-gray-600"></div>
            <span className="flex-shrink mx-4 text-gray-400 text-sm">
              or sign up with email
            </span>
            <div className="flex-grow border-t border-gray-600"></div>
          </div>
          {error && (
            <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md mb-4">
              {error}
            </div>
          )}
          {/* Sign Up form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="relative">
              <User
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="text"
                placeholder="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                disabled={isLoading}
                className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
            </div>

            <div className="relative">
              <User
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="text"
                placeholder="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                disabled={isLoading}
                className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
            </div>

            <div className="relative">
              <Mail
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="email"
                placeholder="Email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                disabled={isLoading}
                className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
            </div>

            <div className="relative">
              <Lock
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                disabled={isLoading}
                className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>

            <div className="relative mb-14">
              <Lock
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm Password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                disabled={isLoading}
                className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
              <button
                type="submit"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>

            <ShadowButton className="w-full py-3 px-4">Sign Up</ShadowButton>
          </form>

          <div className="mt-10 text-sm text-gray-400">
            Already have an account?{" "}
            <button
              onClick={onClickLoginModal}
              className="text-purple-400 hover:text-purple-300 transition-colors font-medium"
            >
              Log in
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUpModal;
