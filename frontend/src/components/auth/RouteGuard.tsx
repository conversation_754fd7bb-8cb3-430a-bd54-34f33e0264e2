import { useEffect } from "react";
import type { ReactNode } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useApp } from "@/contexts/useApp";
import { toast } from "sonner";

interface RouteGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
}

const RouteGuard = ({
  children,
  requireAuth = false,
  requireAdmin = false,
}: RouteGuardProps) => {
  const { user, isAuthenticated, isLoading } = useApp();
  const location = useLocation();
  console.log("requireAdmin", requireAdmin);
  console.log("isAuthenticated", isAuthenticated);
  console.log("user", user?.role);
  useEffect(() => {
    // Show toast messages for unauthorized access attempts
    if (!isLoading) {
      if (requireAuth && !isAuthenticated) {
        toast.error("Please log in to access this page");
      } else if (requireAdmin && (!isAuthenticated || user?.role !== "ADMIN")) {
        toast.error("You need ADMIN privileges to access this page");
      }
    }
  }, [isLoading, isAuthenticated, user, requireAuth, requireAdmin]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to login if authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // Redirect to dashboard if ADMIN access is required but user is not an ADMIN
  if (requireAdmin && (!isAuthenticated || user?.role !== "ADMIN")) {
    return <Navigate to="/dashboard" replace />;
  }

  // If all checks pass, render the children
  return <>{children}</>;
};

export default RouteGuard;
