import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import SubscriptionService from "@/services/subscription.service";
import type {
  SubscriptionPlan,
  UserSubscription,
  PlanComparisonData,
} from "@/services/subscription.service";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Check,
  X,
  Sparkles,
  CreditCard,
  LifeBuoy,
  Users,
  Code,
  Clock,
  Zap,
  HardDrive,
  Layers,
  Award,
  Infinity as InfinityIcon,
} from "lucide-react";

interface SubscriptionPlansDisplayProps {
  onUpgradeSuccess?: () => void;
}

const SubscriptionPlansDisplay = ({
  onUpgradeSuccess,
}: SubscriptionPlansDisplayProps) => {
  const { user, refreshUserData } = useApp();
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [userSubscription, setUserSubscription] =
    useState<UserSubscription | null>(null);
  const [comparisonData, setComparisonData] =
    useState<PlanComparisonData | null>(null);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showComparisonDialog, setShowComparisonDialog] = useState(false);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      const [plansData, userSubData, comparisonData] = await Promise.all([
        SubscriptionService.getSubscriptionPlans(),
        SubscriptionService.getUserSubscription(),
        SubscriptionService.compareSubscriptionPlans(),
      ]);

      setPlans(plansData);
      setUserSubscription(userSubData);
      setComparisonData(comparisonData);
    } catch (error) {
      console.error("Error loading subscription data:", error);
      toast.error("Failed to load subscription plans");
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPlan = async (planId: string) => {
    try {
      setProcessingPlan(planId);

      // If user already has this plan, don't do anything
      if (userSubscription?.plan.id === planId) {
        toast.info("You are already subscribed to this plan");
        setProcessingPlan(null);
        return;
      }

      // Create checkout session
      const response = await SubscriptionService.createCheckoutSession({
        planId,
        successUrl: `${window.location.origin}/dashboard/profile?success=true`,
        cancelUrl: `${window.location.origin}/dashboard/profile?canceled=true`,
      });

      // Redirect to checkout
      window.location.href = response.url;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast.error("Failed to process subscription upgrade");
      setProcessingPlan(null);
    }
  };

  const handleCancelSubscription = async () => {
    if (!userSubscription) return;

    try {
      setProcessingPlan("canceling");
      await SubscriptionService.cancelSubscription(userSubscription.id);
      toast.success("Subscription canceled successfully");

      // Refresh user data
      await refreshUserData();
      await loadSubscriptionData();

      // Call the onUpgradeSuccess callback if provided
      if (onUpgradeSuccess) {
        onUpgradeSuccess();
      }

      setProcessingPlan(null);
    } catch (error) {
      console.error("Error canceling subscription:", error);
      toast.error("Failed to cancel subscription");
      setProcessingPlan(null);
    }
  };

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case "Sparkles":
        return <Sparkles className="h-5 w-5" />;
      case "CreditCard":
        return <CreditCard className="h-5 w-5" />;
      case "LifeBuoy":
        return <LifeBuoy className="h-5 w-5" />;
      case "Users":
        return <Users className="h-5 w-5" />;
      case "Code":
        return <Code className="h-5 w-5" />;
      case "Clock":
        return <Clock className="h-5 w-5" />;
      case "Zap":
        return <Zap className="h-5 w-5" />;
      case "HardDrive":
        return <HardDrive className="h-5 w-5" />;
      case "Layers":
        return <Layers className="h-5 w-5" />;
      case "Award":
        return <Award className="h-5 w-5" />;
      case "Infinity":
        return <InfinityIcon className="h-5 w-5" />;
      default:
        return <Sparkles className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Spinner />
        <span className="ml-2">Loading subscription plans...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Subscription Details</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowComparisonDialog(true)}
          >
            Compare Plans
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={() => setShowUpgradeDialog(true)}
          >
            Upgrade Plan
          </Button>
        </div>
      </div> */}

      {/* Current Plan Display */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>
                {userSubscription
                  ? userSubscription.plan.displayName
                  : "Free Plan"}
                {userSubscription?.status === "CANCELED" && (
                  <Badge variant="outline" className="ml-2 text-yellow-600">
                    Canceled
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {userSubscription
                  ? userSubscription.plan.description
                  : "Basic features with limited usage"}
              </CardDescription>
            </div>
            <Badge
              className={
                userSubscription
                  ? SubscriptionService.getPlanBadgeClass(userSubscription.plan)
                  : "bg-gray-500"
              }
            >
              {userSubscription
                ? `${SubscriptionService.formatPrice(
                    userSubscription.plan.price
                  )}/${userSubscription.plan.interval}`
                : "Free"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Key Features</h4>
              <ul className="space-y-2">
                {userSubscription ? (
                  userSubscription?.plan?.featureHighlights
                    ?.slice(0, 4)
                    ?.map((highlight, index) => (
                      <li key={index} className="flex items-start">
                        <div className="mr-2 mt-0.5 text-primary">
                          {highlight.icon ? (
                            getIconComponent(highlight.icon)
                          ) : (
                            <Check className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <span className="text-sm font-medium">
                            {highlight.title}
                          </span>
                          <p className="text-xs text-muted-foreground">
                            {highlight.description}
                          </p>
                        </div>
                      </li>
                    ))
                ) : (
                  <>
                    <li className="flex items-start">
                      <div className="mr-2 mt-0.5 text-primary">
                        <Sparkles className="h-4 w-4" />
                      </div>
                      <div>
                        <span className="text-sm font-medium">
                          Basic AI Generation
                        </span>
                        <p className="text-xs text-muted-foreground">
                          Access to essential AI generation tools
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-0.5 text-primary">
                        <CreditCard className="h-4 w-4" />
                      </div>
                      <div>
                        <span className="text-sm font-medium">
                          Limited Credits
                        </span>
                        <p className="text-xs text-muted-foreground">
                          5 credits per month
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-0.5 text-primary">
                        <LifeBuoy className="h-4 w-4" />
                      </div>
                      <div>
                        <span className="text-sm font-medium">
                          Standard Support
                        </span>
                        <p className="text-xs text-muted-foreground">
                          Email support with 48-hour response time
                        </p>
                      </div>
                    </li>
                  </>
                )}
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-2">Usage Quotas</h4>
              <dl className="grid grid-cols-2 gap-x-4 gap-y-2">
                <div>
                  <dt className="text-xs text-muted-foreground">Credits</dt>
                  <dd className="text-sm font-medium">
                    {user?.credit?.balance || 0}
                  </dd>
                </div>
                {/* <div>
                  <dt className="text-xs text-muted-foreground">Videos</dt>
                  <dd className="text-sm font-medium">
                    {userSubscription
                      ? SubscriptionService.getFeatureDisplayValue(
                          "videoGenerationQuota",
                          userSubscription.plan.features.videoGenerationQuota
                        )
                      : "5"}{" "}
                    / month
                  </dd>
                </div>
                <div>
                  <dt className="text-xs text-muted-foreground">Images</dt>
                  <dd className="text-sm font-medium">
                    {userSubscription
                      ? SubscriptionService.getFeatureDisplayValue(
                          "imageGenerationQuota",
                          userSubscription.plan.features.imageGenerationQuota
                        )
                      : "20"}{" "}
                    / month
                  </dd>
                </div>
                <div>
                  <dt className="text-xs text-muted-foreground">BG Removals</dt>
                  <dd className="text-sm font-medium">
                    {userSubscription
                      ? SubscriptionService.getFeatureDisplayValue(
                          "backgroundRemovalQuota",
                          userSubscription.plan.features.backgroundRemovalQuota
                        )
                      : "10"}{" "}
                    / month
                  </dd>
                </div> */}
              </dl>
            </div>
          </div>
        </CardContent>
        {userSubscription && (
          <CardFooter className="border-t pt-4 flex justify-between">
            <div className="text-xs text-muted-foreground">
              {userSubscription.status === "CANCELED"
                ? `Access until ${new Date(
                    userSubscription.currentPeriodEnd
                  ).toLocaleDateString()}`
                : `Renews on ${new Date(
                    userSubscription.currentPeriodEnd
                  ).toLocaleDateString()}`}
            </div>
            {userSubscription.status === "ACTIVE" && (
              <Button
                variant="outline"
                size="sm"
                className="text-destructive"
                onClick={handleCancelSubscription}
                disabled={processingPlan === "canceling"}
              >
                {processingPlan === "canceling" ? (
                  <>
                    <Spinner size="sm" className="mr-2" /> Canceling...
                  </>
                ) : (
                  "Cancel Subscription"
                )}
              </Button>
            )}
          </CardFooter>
        )}
      </Card>

      {/* Upgrade Plan Dialog */}
      <Dialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Upgrade Your Plan</DialogTitle>
            <DialogDescription>
              Choose a plan that best fits your needs and unlock more features.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <Tabs defaultValue="monthly" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="monthly">Monthly Billing</TabsTrigger>
                <TabsTrigger value="yearly">
                  Yearly Billing (Save 20%)
                </TabsTrigger>
              </TabsList>
              <TabsContent value="monthly" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {plans
                    ?.filter((plan) => plan.interval === "month")
                    ?.sort(
                      (a, b) => a.sortOrder - b.sortOrder || a.price - b.price
                    )
                    ?.map((plan) => (
                      <div
                        key={plan.id}
                        className={`p-4 border rounded-lg ${
                          userSubscription?.plan.id === plan.id
                            ? `${SubscriptionService.getPlanColorClass(plan)}`
                            : ""
                        } ${plan.isFeatured ? "ring-2 ring-primary" : ""}`}
                      >
                        {plan.isFeatured && (
                          <div className="absolute -top-3 -right-3">
                            <Badge className="bg-primary">Popular</Badge>
                          </div>
                        )}
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium">{plan.displayName}</h3>
                          <Badge
                            className={SubscriptionService.getPlanBadgeClass(
                              plan
                            )}
                          >
                            {SubscriptionService.formatPrice(plan.price)}/month
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {plan.description}
                        </p>
                        <ul className="text-sm space-y-1 mb-3">
                          {plan?.featureHighlights?.map((highlight, index) => (
                            <li key={index} className="flex items-start">
                              <div className="mr-2 mt-0.5 text-primary">
                                {highlight.icon ? (
                                  getIconComponent(highlight.icon)
                                ) : (
                                  <Check className="h-4 w-4" />
                                )}
                              </div>
                              <span>{highlight.title}</span>
                            </li>
                          ))}
                        </ul>
                        <Button
                          variant={
                            userSubscription?.plan.id === plan.id
                              ? "outline"
                              : "default"
                          }
                          className="w-full"
                          disabled={
                            userSubscription?.plan.id === plan.id ||
                            processingPlan === plan.id
                          }
                          onClick={() => handleSelectPlan(plan.id)}
                        >
                          {processingPlan === plan.id ? (
                            <>
                              <Spinner size="sm" className="mr-2" />{" "}
                              Processing...
                            </>
                          ) : userSubscription?.plan.id === plan.id ? (
                            "Current Plan"
                          ) : (
                            "Select Plan"
                          )}
                        </Button>
                      </div>
                    ))}
                </div>
              </TabsContent>
              <TabsContent value="yearly" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {plans
                    ?.filter((plan) => plan.interval === "year")
                    ?.sort(
                      (a, b) => a.sortOrder - b.sortOrder || a.price - b.price
                    )
                    ?.map((plan) => (
                      <div
                        key={plan.id}
                        className={`p-4 border rounded-lg ${
                          userSubscription?.plan.id === plan.id
                            ? `${SubscriptionService.getPlanColorClass(plan)}`
                            : ""
                        } ${plan.isFeatured ? "ring-2 ring-primary" : ""}`}
                      >
                        {plan.isFeatured && (
                          <div className="absolute -top-3 -right-3">
                            <Badge className="bg-primary">Popular</Badge>
                          </div>
                        )}
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium">{plan.displayName}</h3>
                          <Badge
                            className={SubscriptionService.getPlanBadgeClass(
                              plan
                            )}
                          >
                            {SubscriptionService.formatPrice(plan.price)}/year
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {plan.description}
                        </p>
                        <ul className="text-sm space-y-1 mb-3">
                          {plan?.featureHighlights?.map((highlight, index) => (
                            <li key={index} className="flex items-start">
                              <div className="mr-2 mt-0.5 text-primary">
                                {highlight.icon ? (
                                  getIconComponent(highlight.icon)
                                ) : (
                                  <Check className="h-4 w-4" />
                                )}
                              </div>
                              <span>{highlight.title}</span>
                            </li>
                          ))}
                        </ul>
                        <Button
                          variant={
                            userSubscription?.plan.id === plan.id
                              ? "outline"
                              : "default"
                          }
                          className="w-full"
                          disabled={
                            userSubscription?.plan.id === plan.id ||
                            processingPlan === plan.id
                          }
                          onClick={() => handleSelectPlan(plan.id)}
                        >
                          {processingPlan === plan.id ? (
                            <>
                              <Spinner size="sm" className="mr-2" />{" "}
                              Processing...
                            </>
                          ) : userSubscription?.plan.id === plan.id ? (
                            "Current Plan"
                          ) : (
                            "Select Plan"
                          )}
                        </Button>
                      </div>
                    ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
          <DialogFooter className="flex justify-between items-center">
            <p className="text-xs text-muted-foreground">
              Need a custom plan?{" "}
              <a href="#" className="text-primary hover:underline">
                Contact sales
              </a>
            </p>
            <Button
              variant="outline"
              onClick={() => setShowUpgradeDialog(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Plan Comparison Dialog */}
      {comparisonData && (
        <Dialog
          open={showComparisonDialog}
          onOpenChange={setShowComparisonDialog}
        >
          <DialogContent className="sm:max-w-5xl max-h-[90vh] overflow-auto">
            <DialogHeader>
              <DialogTitle>Plan Comparison</DialogTitle>
              <DialogDescription>
                Compare features across different subscription plans.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4 overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="text-left p-2 border-b"></th>
                    {comparisonData?.plans?.map((plan) => (
                      <th
                        key={plan.id}
                        className={`text-center p-2 border-b ${
                          comparisonData?.currentPlanId === plan.id
                            ? SubscriptionService.getPlanColorClass(plan)
                            : ""
                        }`}
                      >
                        <div className="font-medium">{plan?.displayName}</div>
                        <div className="text-sm font-normal">
                          {SubscriptionService.formatPrice(plan?.price)}/
                          {plan?.interval}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {comparisonData?.featureCategories?.map(
                    (category, categoryIndex) => (
                      <>
                        <tr key={`category-${categoryIndex}`}>
                          <td
                            colSpan={comparisonData?.plans?.length + 1}
                            className="p-2 bg-muted/50 font-medium"
                          >
                            {category.name}
                          </td>
                        </tr>
                        {category?.features?.map((feature, featureIndex) => (
                          <tr key={`feature-${categoryIndex}-${featureIndex}`}>
                            <td className="p-2 border-b text-sm">
                              {feature
                                .replace(/([A-Z])/g, " $1")
                                .replace(/^./, (str) => str.toUpperCase())}
                            </td>
                            {comparisonData?.plans?.map((plan) => (
                              <td
                                key={`${plan.id}-${feature}`}
                                className={`p-2 border-b text-center text-sm ${
                                  comparisonData?.currentPlanId === plan.id
                                    ? SubscriptionService.getPlanColorClass(
                                        plan
                                      )
                                    : ""
                                }`}
                              >
                                {typeof plan.features[feature] === "boolean" ? (
                                  plan.features[feature] ? (
                                    <Check className="h-4 w-4 mx-auto text-green-600" />
                                  ) : (
                                    <X className="h-4 w-4 mx-auto text-red-500" />
                                  )
                                ) : (
                                  SubscriptionService.getFeatureDisplayValue(
                                    feature,
                                    plan.features[feature]
                                  )
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </>
                    )
                  )}
                </tbody>
              </table>
            </div>
            <DialogFooter>
              <Button onClick={() => setShowUpgradeDialog(true)}>
                Upgrade Now
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowComparisonDialog(false)}
              >
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default SubscriptionPlansDisplay;
