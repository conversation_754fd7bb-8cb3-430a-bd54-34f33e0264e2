import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import CreditService from "@/services/credit.service";
import type { CreditPackage } from "@/services/credit.service";
import type {
  UserSubscription,
  SubscriptionPlan,
} from "@/services/subscription.service";
import SubscriptionService from "@/services/subscription.service";
import { cn } from "@/lib/utils";
import CreditPurchaseModal from "@/components/payment/CreditPurchaseModal";
import { Loader2 } from "lucide-react";
import { Text } from "@/components/ui/text";
import RadioButton from "@/components/ui/radioBtn";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import SubscriptionCancellationModal from "@/components/subscription/SubscriptionCancellationModal";
import RefundRequestModal from "@/components/subscription/RefundRequestModal";
import { GradientBackground } from "./GradientBackground";

export const PricingCard = () => {
  const { user } = useApp();
  const [subscriptionPlans, setSubscriptionPlans] = useState<
    SubscriptionPlan[]
  >([]);
  const [creditPackages, setCreditPackages] = useState<CreditPackage[]>([]);
  const [userSubscription, setUserSubscription] =
    useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState({
    plans: true,
    packages: true,
    subscribing: false,
    purchasing: false,
    userSubscription: false,
  });
  const [error, setError] = useState({
    plans: "",
    packages: "",
    userSubscription: "",
  });

  // State for modals
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false);
  const [isCancellationModalOpen, setIsCancellationModalOpen] = useState(false);
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const [selectedPackageDetails, setSelectedPackageDetails] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const freePlan = [
    {
      icon: "/icons/album.svg",
      text: "20 Free credits",
    },
    {
      icon: "/icons/unlimited.svg",
      text: "Unlimited background removal try",
    },
    {
      icon: "/icons/setting.svg",
      text: "20 Virtual Try on Clothes",
    },
    {
      icon: "/icons/ps.svg",
      text: "4 Speedpainting Videos",
    },
    {
      icon: "/icons/refer.svg",
      text: "Refer friends and earn free credits",
    },
  ];

  const [selectedSubscriptionId, setSelectedSubscriptionId] = useState("");
  const [selectedPackageId, setSelectedPackageId] = useState("");

  // Fetch subscription plans and credit packages from the backend
  useEffect(() => {
    const fetchUserSubscription = async () => {
      if (!user) return;

      try {
        setLoading((prev) => ({ ...prev, userSubscription: true }));
        const subscription = await SubscriptionService.getUserSubscription();
        setUserSubscription(subscription);
        setError((prev) => ({ ...prev, userSubscription: "" }));
      } catch (err) {
        console.error("Error fetching user subscription:", err);
        setError((prev) => ({
          ...prev,
          userSubscription: "Failed to load user subscription",
        }));
      } finally {
        setLoading((prev) => ({ ...prev, subscribing: false }));
      }
    };
    fetchUserSubscription();
  }, [user]);

  // Refresh user subscription data
  const refreshUserSubscription = async () => {
    try {
      setLoading((prev) => ({ ...prev, userSubscription: true }));
      const subscription = await SubscriptionService.getUserSubscription();
      setUserSubscription(subscription);
      setError((prev) => ({ ...prev, userSubscription: "" }));
    } catch (err) {
      console.error("Error refreshing user subscription:", err);
    } finally {
      setLoading((prev) => ({ ...prev, userSubscription: false }));
    }
  };

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading((prev) => ({ ...prev, plans: true }));
        const response = await SubscriptionService.getSubscriptionPlans();
        setSubscriptionPlans(response.filter((plan) => plan.isActive));
        if (response.length > 0) {
          // Select the first plan by default
          console.log("userSubscription", userSubscription);
          setSelectedSubscriptionId(
            userSubscription ? userSubscription.plan.id : response[0].id
          );
        }
        setError((prev) => ({ ...prev, plans: "" }));
      } catch (err) {
        console.error("Error fetching subscription plans:", err);
        setError((prev) => ({
          ...prev,
          plans: "Failed to load subscription plans",
        }));
      } finally {
        setLoading((prev) => ({ ...prev, plans: false }));
      }
    };

    const fetchPackages = async () => {
      try {
        setLoading((prev) => ({ ...prev, packages: true }));
        const response = await CreditService.getCreditPackages();
        if (response.data) {
          setCreditPackages(response.data.filter((pkg) => pkg.isActive));
          if (response.data.length > 0) {
            // Select the first package by default
            setSelectedPackageId(response.data[0].id);
          }
        }
        setError((prev) => ({ ...prev, packages: "" }));
      } catch (err) {
        console.error("Error fetching credit packages:", err);
        setError((prev) => ({
          ...prev,
          packages: "Failed to load credit packages",
        }));
      } finally {
        setLoading((prev) => ({ ...prev, packages: false }));
      }
    };

    fetchPlans();
    fetchPackages();
  }, [userSubscription]);

  useEffect(() => {
    if (subscriptionPlans.length > 0 && userSubscription) {
      const currentPlanId = userSubscription.planId;
      setSelectedSubscriptionId(currentPlanId);
    }
  }, [subscriptionPlans, userSubscription]);

  // Handle subscription checkout
  const handleSubscribe = async () => {
    if (!user) {
      toast.error("Please sign in to subscribe");
      return;
    }

    if (!selectedSubscriptionId) {
      toast.error("Please select a subscription plan");
      return;
    }

    try {
      setLoading((prev) => ({ ...prev, subscribing: true }));
      const response = await SubscriptionService.createCheckoutSession({
        planId: selectedSubscriptionId,
        successUrl: `${window.location.origin}/dashboard/subscription`,
        cancelUrl: `${window.location.origin}/pricing?subscription=canceled`,
      });

      // Redirect to Stripe checkout
      if (response && response.url) {
        window.location.href = response.url;
      } else {
        throw new Error("Invalid checkout response");
      }
    } catch (err) {
      console.error("Error creating checkout session:", err);
      toast.error("Failed to create checkout session. Please try again.");
    } finally {
      setLoading((prev) => ({ ...prev, subscribing: false }));
    }
  };

  // Handle opening the subscription cancellation modal
  const handleOpenCancellationModal = () => {
    if (!user || !userSubscription) {
      toast.error("No active subscription to cancel");
      return;
    }
    setIsCancellationModalOpen(true);
  };

  // Handle subscription cancellation success
  const handleCancellationSuccess = async () => {
    // Refresh subscription status
    refreshUserSubscription();
  };

  // Handle credit purchase using Stripe Payment Elements
  const handlePurchaseCredits = () => {
    if (!user) {
      toast("Authentication required", {
        description: "Please sign in to purchase credits",
        // variant: "destructive",
      });
      return;
    }

    if (!selectedPackageId) {
      toast("Selection required", {
        description: "Please select a credit package",
        // variant: "destructive",
      });
      return;
    }

    // Find the selected package details
    const selectedPackage = creditPackages.find(
      (pkg) => pkg.id === selectedPackageId
    );
    if (!selectedPackage) {
      toast("Error", {
        description: "Selected package not found",
        // variant: "destructive",
      });
      return;
    }

    // Set the selected package details and open the modal
    setSelectedPackageDetails({
      id: selectedPackageId,
      name: `${selectedPackage.creditsAmount} Credits`,
    });
    setIsPurchaseModalOpen(true);
  };

  // Handle successful purchase
  const handlePurchaseSuccess = () => {
    toast("Purchase successful", {
      description: "Your credits have been added to your account",
      // variant: "default",
    });
    // Optionally refresh user credit balance or redirect to dashboard
  };

  const handleCreditPackageChange = (value: string) => {
    setSelectedPackageId(value);
  };

  return (
    <>
      {/* Credit Purchase Modal */}
      {selectedPackageDetails && (
        <CreditPurchaseModal
          isOpen={isPurchaseModalOpen}
          onClose={() => setIsPurchaseModalOpen(false)}
          packageId={selectedPackageDetails?.id || ""}
          packageName={selectedPackageDetails?.name || ""}
          onSuccess={handlePurchaseSuccess}
        />
      )}

      {userSubscription && (
        <SubscriptionCancellationModal
          isOpen={isCancellationModalOpen}
          onClose={() => setIsCancellationModalOpen(false)}
          subscription={userSubscription}
          onSuccess={handleCancellationSuccess}
          onRefundRequest={() => {
            setIsCancellationModalOpen(false);
            setIsRefundModalOpen(true);
          }}
        />
      )}

      {userSubscription && (
        <RefundRequestModal
          isOpen={isRefundModalOpen}
          onClose={() => setIsRefundModalOpen(false)}
          subscriptionId={userSubscription.id}
          subscriptionName={userSubscription.plan.displayName}
          onSuccess={refreshUserSubscription}
        />
      )}

      <GradientBackground
        imageUrl="/bg/price_bg.svg"
        startColor="rgba(18, 24, 40, 0.4)"
        endColor="rgba(18, 24, 40, 1)"
        direction="to bottom"
      />
      <div className="xl:max-w-[1440px] mx-auto">
        <div className="relative lg:pt-20 pt-12 lg:pb-14 pb-8">
          <h1 className="text-white font-semibold xl:text-5xl xl:leading-16 text-3xl text-center 2xl:px-[15%] lg:px-[10%] px-6">
            Select the right plan to boost your business productivity
          </h1>
        </div>

        <div className="grid items-center px-6 justify-center grid-cols-1 xl:grid-cols-10 w-full">
          <div className="xl:col-span-3 xl:-mr-7  rounded-[8px] z-[10] border bg-[#16243D] overflow-hidden flex flex-col gap-8 justify-between shadow-2xl border-[#192f50]">
            <div className="p-5 py-10 flex flex-col gap-8 ">
              <Text
                variant={"card_title"}
                className="text-[#F5F5F7] text-center"
              >
                Free plan
              </Text>
              <div className="flex flex-col gap-[36px] px-8 pb-16">
                {freePlan.map((feature, index) => (
                  <div key={index} className="flex items-center gap-4">
                    <img
                      src={feature.icon}
                      className="w-8 h-auto rounded-full"
                    />
                    <Text
                      variant="card_list_title"
                      className="text-[#D9D9D9] font-light"
                    >
                      {feature.text}
                    </Text>
                  </div>
                ))}
              </div>
              <Button
                outline={false}
                variant={"primary"}
                className="w-full px-5 border border-white/15 rounded-full bg-[#192f50] hover:bg-[#192f50]"
                disabled={!!user}
                onClick={() => (window.location.href = "/auth/register")}
              >
                Sign up for free
              </Button>
            </div>
            <div className="flex py-5 px-20  flex-col gap-2 items-center justify-center bg-[#192f50]">
              <Text
                variant={"section_title"}
                className="text-white font-light text-[12px] text-center"
              >
                <span className="font-semibold">Free Forever: </span>
                Recommended for personal use and evaluation
              </Text>
            </div>
          </div>

          <div className="xl:col-span-4 relative rounded-[8px] z-[20] border border-[#192f50] bg-[#16243D] flex flex-col gap-8 justify-between shadow-3xl">
            <div className="absolute -right-2 -top-2">
              <div className="relative">
                <img
                  src="/icons/banner.svg"
                  className="w-full h-full"
                  alt="Banner Icon"
                />
                <p className="absolute top-12 right-5 rotate-48 text-center text-black text-sm font-semibold whitespace-nowrap">
                  Best Value
                </p>
              </div>
            </div>
            <div className="p-4 pt-10 flex flex-col gap-8 ">
              <Text
                variant={"card_title"}
                className="text-[#F5F5F7] text-center"
              >
                Subscription Plan
              </Text>
              {loading.plans ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="w-8 h-8 text-white animate-spin" />
                </div>
              ) : error.plans ? (
                <div className="text-red-500 py-4 text-center">
                  {error.plans}
                </div>
              ) : (
                <>
                  <Text variant={"section_title"} className="text-center">
                    <span className="text-5xl font-semibold bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent">
                      {selectedSubscriptionId &&
                      subscriptionPlans.length > 0 ? (
                        <>
                          {subscriptionPlans.find(
                            (plan) => plan.id === selectedSubscriptionId
                          )?.currency === "USD" && "$"}
                          {subscriptionPlans.find(
                            (plan) => plan.id === selectedSubscriptionId
                          )?.price || 0}
                        </>
                      ) : (
                        "$0"
                      )}
                    </span>{" "}
                    <span className="text-[16px] bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent">
                      /{" "}
                      {(selectedSubscriptionId &&
                        subscriptionPlans.find(
                          (plan) => plan.id === selectedSubscriptionId
                        )?.interval) ||
                        "month"}
                    </span>
                  </Text>
                  <div className="flex flex-col gap-2">
                    {subscriptionPlans.map((plan) => {
                      // Calculate price per credit (if features include credits)
                      const creditsPerMonth =
                        plan.features?.credits ||
                        plan.features?.creditsPerMonth ||
                        (typeof plan.features === "object" &&
                        "credits" in plan.features
                          ? plan.features.credits
                          : 0);
                      const pricePerCredit = creditsPerMonth
                        ? (plan.price / Number(creditsPerMonth)).toFixed(3)
                        : "N/A";

                      return (
                        <div
                          key={plan.id}
                          className={cn(
                            "w-full grid cursor-pointer p-2.5 grid-cols-12 gap-4",
                            selectedSubscriptionId === plan.id
                              ? "bg-[#192f50]"
                              : userSubscription?.plan.id === plan.id
                              ? "bg-sky-400 "
                              : ""
                          )}
                          onClick={() => setSelectedSubscriptionId(plan.id)}
                        >
                          <div className="col-span-7">
                            <RadioButton
                              name="subscription"
                              value={plan.id}
                              checked={selectedSubscriptionId === plan.id}
                              onChange={(value) =>
                                setSelectedSubscriptionId(value)
                              }
                              label={`${creditsPerMonth} credits/month`}
                              labelClass="text-[#D9D9D9] text-base"
                            />
                          </div>
                          <Text
                            variant={"card_body"}
                            className="col-span-2 font-light text-[#D9D9D9] text-base"
                          >
                            {plan.currency === "USD" && "$ "}
                            {plan.price}
                          </Text>
                          <Text
                            variant={"card_body"}
                            className="text-[#D9D9D9] text-base text-right col-span-3 font-light"
                          >
                            {plan.currency === "USD" && "$"}
                            {pricePerCredit}/credit
                          </Text>
                        </div>
                      );
                    })}
                  </div>
                </>
              )}

              <div className="flex flex-col gap-2.5 justify-center items-center">
                {userSubscription && userSubscription.status === "ACTIVE" ? (
                  <div className="flex flex-col gap-2 w-full">
                    <div className="p-3 bg-green-900/20 border border-green-700/30 rounded-md mb-2">
                      <Text
                        variant={"card_body"}
                        className="text-white font-medium mb-1"
                      >
                        Current Plan: {userSubscription.plan.displayName}
                      </Text>
                      <Text
                        variant={"card_body"}
                        className="text-white/70 text-sm"
                      >
                        Renews on:{" "}
                        {new Date(
                          userSubscription.currentPeriodEnd
                        ).toLocaleDateString()}
                      </Text>
                    </div>
                    <Button
                      outline={false}
                      variant={"secondary"}
                      className="w-full bg-green-600 hover:bg-green-700"
                      disabled={true}
                    >
                      <span className="flex items-center">
                        <span className="mr-2 h-2 w-2 rounded-full bg-green-400"></span>
                        Currently Subscribed
                      </span>
                    </Button>
                    {!!userSubscription?.endDate &&
                    new Date(userSubscription.endDate) > new Date() ? (
                      <Button
                        outline={true}
                        variant={"outline"}
                        className="w-full"
                        disabled={true}
                      >
                        Already Cancelled
                      </Button>
                    ) : (
                      <Button
                        outline={true}
                        variant={"secondary"}
                        className="w-full"
                        onClick={handleOpenCancellationModal}
                        disabled={loading.subscribing}
                      >
                        Cancel Subscription
                      </Button>
                    )}
                  </div>
                ) : userSubscription &&
                  userSubscription.status === "CANCELED" ? (
                  <div className="flex flex-col gap-2 w-full">
                    <div className="p-3 bg-amber-900/20 border border-amber-700/30 rounded-md mb-2">
                      <Text
                        variant={"card_body"}
                        className="text-white font-medium mb-1"
                      >
                        Plan: {userSubscription.plan.displayName}
                      </Text>
                      <Text
                        variant={"card_body"}
                        className="text-white/70 text-sm"
                      >
                        Access until:{" "}
                        {new Date(
                          userSubscription.currentPeriodEnd
                        ).toLocaleDateString()}
                      </Text>
                    </div>
                    <Button
                      outline={false}
                      variant={"secondary"}
                      className="w-full bg-amber-600 hover:bg-amber-700"
                      disabled={true}
                    >
                      <span className="flex items-center">
                        <span className="mr-2 h-2 w-2 rounded-full bg-amber-400"></span>
                        Subscription Ending
                      </span>
                    </Button>
                    {/* Only show resubscribe button if the current period end date has passed */}
                    {new Date(userSubscription.currentPeriodEnd) <
                      new Date() && (
                      <Button
                        outline={false}
                        variant={"primary"}
                        className="w-full"
                        onClick={handleSubscribe}
                        disabled={
                          loading.subscribing || !selectedSubscriptionId
                        }
                      >
                        {loading.subscribing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          "Resubscribe"
                        )}
                      </Button>
                    )}
                  </div>
                ) : (
                  <Button
                    outline={false}
                    variant={"primary"}
                    className="w-full bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] rounded-full"
                    onClick={handleSubscribe}
                    disabled={loading.subscribing || !selectedSubscriptionId}
                  >
                    {loading.subscribing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      "Subscribe now"
                    )}
                  </Button>
                )}{" "}
                <Text
                  variant={"section_title"}
                  className=" font-light text-[12px]"
                >
                  *Price excluding VAT, if applicable
                </Text>
              </div>
            </div>
            <div className="flex p-5 flex-col gap-2 items-center justify-center bg-[#192f50]">
              <div className="flex items-center gap-2 text-center">
                <Text
                  variant={"section_title"}
                  className="text-white font-light text-[12px]"
                >
                  <span className="font-semibold">Risk free: </span>
                  14 Days Money Back Guarantee
                </Text>
              </div>
              <div className="flex items-center gap-2">
                <Text
                  variant={"section_title"}
                  className="text-white font-light text-[12px]"
                >
                  <span className="font-semibold">Flexible: </span>
                  Downgrade, upgrade or cancel any time
                </Text>
              </div>
              <div className="flex items-center gap-2">
                <Text
                  variant={"section_title"}
                  className="text-white font-light text-[12px]"
                >
                  <span className="font-semibold">Fair: </span>
                  Unused credits roll over as long as you're subscribed
                </Text>
              </div>
            </div>
          </div>

          <div className="xl:col-span-3 xl:-ml-5 rounded-[8px] z-[10] border border-[#192f50] bg-[#16243D] overflow-hidden flex flex-col gap-8 justify-between shadow-2xl">
            <div className="p-4 pt-10 flex flex-col gap-8 ">
              <div className="flex flex-col gap-2">
                <Text
                  variant={"card_title"}
                  className="text-[#F5F5F7] text-center"
                >
                  Top Up Credits
                </Text>
                {loading.packages ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="w-8 h-8 text-white animate-spin" />
                  </div>
                ) : error.packages ? (
                  <div className="text-red-500 py-4 text-center">
                    {error.packages}
                  </div>
                ) : (
                  <>
                    <Text variant={"section_title"} className="text-center">
                      <span className="text-5xl font-semibold bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent">
                        {selectedPackageId && creditPackages.length > 0 ? (
                          <>
                            {creditPackages.find(
                              (pkg) => pkg.id === selectedPackageId
                            )?.currency === "USD" && "$"}
                            {creditPackages.find(
                              (pkg) => pkg.id === selectedPackageId
                            )?.price || 0}
                          </>
                        ) : (
                          "$0"
                        )}
                      </span>
                      <span className="text-[16px] bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent">
                        {" "}
                        /{" "}
                        {selectedPackageId && creditPackages.length > 0 ? (
                          <>
                            {creditPackages.find(
                              (pkg) => pkg.id === selectedPackageId
                            )?.creditsAmount || 0}{" "}
                            Credits
                          </>
                        ) : (
                          "0 Credits"
                        )}
                      </span>
                    </Text>
                  </>
                )}
              </div>
              <div className="flex flex-col gap-2">
                {!loading.packages &&
                  !error.packages &&
                  creditPackages.map((pkg) => {
                    // Calculate price per credit
                    const pricePerCredit = (
                      pkg.price / pkg.creditsAmount
                    ).toFixed(3);

                    return (
                      <div
                        key={pkg.id}
                        onClick={() => handleCreditPackageChange(pkg.id)}
                        className={cn(
                          "w-full grid cursor-pointer p-2.5 grid-cols-12 gap-4",
                          selectedPackageId === pkg.id ? "bg-[#192f50]" : ""
                        )}
                      >
                        <div className="col-span-6">
                          <RadioButton
                            name="topup"
                            value={pkg.id}
                            checked={selectedPackageId === pkg.id}
                            onChange={(value) =>
                              handleCreditPackageChange(value)
                            }
                            label={`${pkg.creditsAmount} credits`}
                            labelClass="text-[#D9D9D9] text-base"
                          />
                        </div>
                        <Text
                          variant={"card_body"}
                          className="text-[#D9D9D9] text-base col-span-2 font-light"
                        >
                          {pkg.currency === "USD" && "$ "}
                          {pkg.price}
                        </Text>
                        <Text
                          variant={"card_body"}
                          className="text-[#D9D9D9] text-base col-span-4 text-right font-light"
                        >
                          {pkg.currency === "USD" && "$"}
                          {pricePerCredit}/credit
                        </Text>
                      </div>
                    );
                  })}
              </div>
              <div className="flex flex-col gap-2.5 justify-center items-center">
                <Button
                  outline={false}
                  variant={"primary"}
                  className="w-full bg-[#192f50] hover:bg-[#192f50] border-white/15 rounded-full"
                  onClick={handlePurchaseCredits}
                  disabled={loading.purchasing || !selectedPackageId}
                >
                  {loading.purchasing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Buy now"
                  )}
                </Button>{" "}
                <Text
                  variant={"section_title"}
                  className=" font-light text-[12px]"
                >
                  *Price including VAT, if applicable
                </Text>
              </div>
            </div>
            <div className="flex p-5 px-16 bg-[#192f50] flex-col gap-2 items-center justify-center">
              <Text
                variant={"section_title"}
                className="text-white text-center font-light text-[12px]"
              >
                Credits available for use anytime within two years from the date
                of purchase.
              </Text>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
