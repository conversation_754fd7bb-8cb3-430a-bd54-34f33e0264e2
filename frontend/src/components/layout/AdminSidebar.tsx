import { useEffect, useMemo, useState } from "react";
import { X, ChevronDown, ChevronRight } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";

interface AdminSidebarProps {
  activeItem: string;
  setActiveItem: (item: string) => void;
  isMobileMenuOpen: boolean;
  setIsMobileMenuOpen: (isOpen: boolean) => void;
}

const AdminSidebar = ({
  activeItem,
  setActiveItem,
  isMobileMenuOpen,
  setIsMobileMenuOpen,
}: AdminSidebarProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  const GENERAL_DATA = useMemo(
    () => [
      { name: "Dashboard", href: "/admin" },
      { name: "Users", href: "/admin/users" },
      { name: "Blog", href: "/admin/blog" },
      { name: "Analytics", href: "/admin/analytics" },
      { name: "Payments", href: "/admin/payments" },
      { name: "Refunds", href: "/admin/refunds" },
      { name: "Payment Settings", href: "/admin/payment-settings" },
      { name: "Subscription Plans", href: "/admin/subscription-plans" },
      { name: "Credit Packages", href: "/admin/credit-packages" },
      { name: "Service Costs", href: "/admin/service-costs" },
      { name: "Settings", href: "/admin/settings" },
    ],
    []
  );

  const VIRTUAL_TRYON_DATA = useMemo(
    () => [
      { name: "Models", href: "/admin/virtual-try-on/models" },
      { name: "Clothing", href: "/admin/virtual-try-on/clothing" },
      { name: "Statistics", href: "/admin/virtual-try-on/statistics" },
    ],
    []
  );

  // Set the active item based on the current URL when the component mounts
  useEffect(() => {
    const currentPath = location.pathname;
    const matchingItem = GENERAL_DATA.find((item) => currentPath === item.href);
    const matchingVirtualTryOnItem = VIRTUAL_TRYON_DATA.find(
      (item) => currentPath === item.href
    );

    if (matchingItem) {
      setActiveItem(matchingItem.name);
    } else if (matchingVirtualTryOnItem) {
      setActiveItem(matchingVirtualTryOnItem.name);
      // Auto-expand Virtual Try-On section if we're on one of its pages
      if (!expandedSections.includes("Virtual Try On")) {
        setExpandedSections((prev) => [...prev, "Virtual Try On"]);
      }
    }
  }, [
    location.pathname,
    setActiveItem,
    GENERAL_DATA,
    VIRTUAL_TRYON_DATA,
    expandedSections,
  ]);

  const handleItemClick = (itemName: string, href: string) => {
    setActiveItem(itemName);
    if (setIsMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
    navigate(href);
  };

  const toggleSection = (sectionName: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionName)
        ? prev.filter((name) => name !== sectionName)
        : [...prev, sectionName]
    );
  };

  const SidebarContent = ({ isDesktop = false }) => (
    <>
      {/* Logo */}
      <div className="p-5">
        <img
          src="/png/new_miragic_logo.png"
          className="w-[160px]"
          alt="Miragic Logo"
        />
      </div>

      {/* Navigation Section */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {/* General Section */}
        <div className={`${isDesktop ? "px-3" : "px-6"} mb-4`}>
          <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider ml-3 mt-3 mb-6">
            GENERAL
          </h3>
          <nav className="space-y-4">
            {GENERAL_DATA.map((item) => {
              const isActive = activeItem === item.name;
              const activeColor = "#9855FF";
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name, item.href)}
                  className={`group relative w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 hover:text-purple-600 cursor-pointer rounded-lg ${
                    isActive ? `text-[${activeColor}]` : "text-white"
                  } flex space-x-3`}
                >
                  <span className="truncate">{item.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Virtual Try-On Section */}
        <div className={`${isDesktop ? "px-3" : "px-6"} mb-4`}>
          <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider ml-3 mt-3 mb-6">
            FEATURES
          </h3>
          <nav className="space-y-4">
            {/* Virtual Try-On Parent Item */}
            <div>
              <button
                onClick={() => toggleSection("Virtual Try On")}
                className="group relative w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 hover:text-purple-600 cursor-pointer rounded-lg text-white flex items-center justify-between"
              >
                <span className="truncate">Virtual Try On</span>
                {expandedSections.includes("Virtual Try On") ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </button>

              {/* Virtual Try-On Sub Items */}
              {expandedSections.includes("Virtual Try On") && (
                <div className="ml-4 mt-2 space-y-2">
                  {VIRTUAL_TRYON_DATA.map((item) => {
                    const isActive = activeItem === item.name;
                    const activeColor = "#9855FF";
                    return (
                      <button
                        key={item.name}
                        onClick={() => handleItemClick(item.name, item.href)}
                        className={`group relative w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 hover:text-purple-600 cursor-pointer rounded-lg ${
                          isActive ? `text-[${activeColor}]` : "text-gray-300"
                        } flex space-x-3`}
                      >
                        <span className="truncate">{item.name}</span>
                      </button>
                    );
                  })}
                </div>
              )}
            </div>
          </nav>
        </div>
      </div>
    </>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <aside className="hidden lg:flex flex-col bg-primary w-64 h-full">
        <SidebarContent isDesktop={true} />
      </aside>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-50 bg-black/70 bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div
            className="fixed inset-y-0 left-0 w-64 bg-[#1A1A1A] border-r border-gray-700 overflow-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Mobile Close Button */}
            <div className="absolute top-2 right-2 z-10">
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-gray-400 hover:text-white rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <SidebarContent isDesktop={false} />
          </div>
        </div>
      )}
    </>
  );
};

export default AdminSidebar;
