import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  BackgroundRemoverIcon,
  CreditIcon,
  HomeIconSidebar,
  PaymentIcon,
  ProfileIcon,
  SpeedpaintingIcon,
  VirtualTryOnIcon,
} from "@/lib/icons";
import { PanelRightClose, PanelRightOpen } from "lucide-react";

interface UserSidebarProps {
  activeItem: string;
  setActiveItem: (item: string) => void;
  isMobileMenuOpen: boolean;
  setIsMobileMenuOpen: (isOpen: boolean) => void;
  isExpanded?: boolean;
  isMobile?: boolean;
  toggleSidebar: () => void;
}

const UserSidebar = ({
  activeItem,
  setActiveItem,
  // isMobileMenuOpen,
  setIsMobileMenuOpen,
  isExpanded = false,
  isMobile = false,
  toggleSidebar,
}: UserSidebarProps) => {
  const navigate = useNavigate();
  const location = useLocation();

  const generalItems = [
    {
      name: "Home",
      href: "/dashboard",
      icon: HomeIconSidebar,
    },
  ];

  const aiToolsItems = [
    {
      name: "Background Remover",
      href: "/ai-tool/background-remover",
      icon: BackgroundRemoverIcon,
    },
    {
      name: "Virtual Try On",
      href: "/ai-tool/virtual-try-on",
      icon: VirtualTryOnIcon,
    },
    {
      name: "Speedpainting",
      href: "/ai-tool/speedpainting",
      icon: SpeedpaintingIcon,
    },
  ];

  const accountItems = [
    {
      name: "Credit",
      href: "/account/credit",
      icon: CreditIcon,
    },
    {
      name: "Profile",
      href: "/dashboard/profile",
      icon: ProfileIcon,
    },
    {
      name: "Payments",
      href: "/account/transaction",
      icon: PaymentIcon,
    },
  ];

  // Set the active item based on the current URL when the component mounts
  useEffect(() => {
    const currentPath = location.pathname;
    const allItems = [...generalItems, ...aiToolsItems, ...accountItems];
    const matchingItem = allItems.find((item) => currentPath === item.href);
    if (matchingItem) {
      setActiveItem(matchingItem.name);
    }
  }, [location.pathname, setActiveItem]);

  const handleItemClick = (itemName: string, href: string) => {
    setActiveItem(itemName);
    if (setIsMobileMenuOpen && isMobile) {
      setIsMobileMenuOpen(false);
    }
    navigate(href);
  };

  const SidebarContent = () => (
    <aside className="flex flex-col bg-[#1A1A1A] h-full border-r border-gray-700 ">
      {/* Logo */}
      <div className="flex justify-center">
        <div className="flex items-center gap-3 mt-6">
          {!isMobile && !isExpanded ? (
            <div className="flex flex-col items-center gap-4">
              <img src="/png/about_logo.png" className="w-[40px]" alt="Logo" />
              <button
                onClick={toggleSidebar}
                className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
                title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
              >
                <PanelRightClose className="w-5 h-5" />
              </button>
            </div>
          ) : (
            <>
              <img
                src="/png/new_miragic_logo.png"
                className="w-[160px]"
                alt="Logo"
              />
              <button
                onClick={toggleSidebar}
                className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
                title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
              >
                <PanelRightOpen className="w-5 h-5" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Navigation and User Info (Scrollable) */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {/* General Section */}
        <div
          className={`${!isMobile && !isExpanded ? "px-3" : "px-6"} mb-4 mt-8`}
        >
          {(isMobile || isExpanded) && (
            <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
              GENERAL
            </h3>
          )}
          <nav
            className={`space-y-1 ${
              !isExpanded && !isMobile ? "pb-6 border-b-2 border-gray-500" : ""
            }`}
          >
            {generalItems.map((item) => {
              const isActive = activeItem === item.name;
              const activeColor = "#9855FF";
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name, item.href)}
                  className={`group relative w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg cursor-pointer ${
                    isActive
                      ? `text-[${activeColor}]`
                      : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
                  } ${
                    !isMobile && !isExpanded
                      ? "flex justify-center"
                      : "flex items-center space-x-3"
                  }`}
                  title={!isMobile && !isExpanded ? item.name : undefined}
                >
                  <IconComponent color={isActive ? activeColor : "white"} />
                  {(isMobile || isExpanded) && (
                    <span className="truncate">{item.name}</span>
                  )}

                  {/* Tooltip for collapsed state */}
                  {!isMobile && !isExpanded && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* AI Tools Section */}
        <div
          className={`${!isMobile && !isExpanded ? "px-3 pt-3" : "px-6"} mb-4`}
        >
          {(isMobile || isExpanded) && (
            <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
              AI TOOLS
            </h3>
          )}
          <nav className="space-y-1">
            {aiToolsItems.map((item) => {
              const isActive = activeItem === item.name;
              const activeColor = "#9855FF";
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name, item.href)}
                  className={`group relative cursor-pointer w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                    isActive
                      ? `text-[${activeColor}]`
                      : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
                  } ${
                    !isMobile && !isExpanded
                      ? "flex justify-center"
                      : "flex items-center space-x-3"
                  }`}
                  title={!isMobile && !isExpanded ? item.name : undefined}
                >
                  <IconComponent color={isActive ? activeColor : "white"} />
                  {(isMobile || isExpanded) && (
                    <span className="truncate">{item.name}</span>
                  )}

                  {/* Tooltip for collapsed state */}
                  {!isMobile && !isExpanded && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Account Section */}
        <div
          className={`${!isMobile && !isExpanded ? "px-3 pt-3" : "px-6"} mb-4`}
        >
          {(isMobile || isExpanded) && (
            <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
              Account
            </h3>
          )}
          <nav className="space-y-1">
            {accountItems.map((item) => {
              const isActive = activeItem === item.name;
              const activeColor = "#9855FF";
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name, item.href)}
                  className={`group relative cursor-pointer w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                    isActive
                      ? `text-[${activeColor}]`
                      : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
                  } ${
                    !isMobile && !isExpanded
                      ? "flex justify-center"
                      : "flex items-center space-x-3"
                  }`}
                  title={!isMobile && !isExpanded ? item.name : undefined}
                >
                  <IconComponent color={isActive ? activeColor : "white"} />
                  {(isMobile || isExpanded) && (
                    <span className="truncate">{item.name}</span>
                  )}

                  {/* Tooltip for collapsed state */}
                  {!isMobile && !isExpanded && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Extra Space at the Bottom */}
        <div className="h-6"></div>
      </div>
    </aside>
  );

  return <SidebarContent />;
};

export default UserSidebar;

// import { useEffect } from "react";
// import { useNavigate, useLocation } from "react-router-dom";
// import {
//   BackgroundRemoverIcon,
//   CreditIcon,
//   HomeIconSidebar,
//   PaymentIcon,
//   ProfileIcon,
//   SpeedpaintingIcon,
//   VirtualTryOnIcon,
// } from "@/lib/icons";
// import { PanelRightClose, PanelRightOpen } from "lucide-react";

// interface UserSidebarProps {
//   activeItem: string;
//   setActiveItem: (item: string) => void;
//   isMobileMenuOpen: boolean;
//   setIsMobileMenuOpen: (isOpen: boolean) => void;
//   isExpanded?: boolean;
//   isMobile?: boolean;
//   toggleSidebar: () => void;
// }

// const UserSidebar = ({
//   activeItem,
//   setActiveItem,
//   // isMobileMenuOpen,
//   setIsMobileMenuOpen,
//   isExpanded = false,
//   isMobile = false,
//   toggleSidebar,
// }: UserSidebarProps) => {
//   const navigate = useNavigate();
//   const location = useLocation();

//   const generalItems = [
//     {
//       name: "Home",
//       href: "/dashboard",
//       icon: HomeIconSidebar,
//     },
//   ];

//   const aiToolsItems = [
//     {
//       name: "Background Remover",
//       href: "/ai-tool/background-remover",
//       icon: BackgroundRemoverIcon,
//     },
//     {
//       name: "Virtual Try On",
//       href: "/ai-tool/virtual-try-on",
//       icon: VirtualTryOnIcon,
//     },
//     {
//       name: "Speedpainting",
//       href: "/ai-tool/speedpainting",
//       icon: SpeedpaintingIcon,
//     },
//   ];

//   const accountItems = [
//     {
//       name: "Credit",
//       href: "/account/credit",
//       icon: CreditIcon,
//     },
//     {
//       name: "Profile",
//       href: "/dashboard/profile",
//       icon: ProfileIcon,
//     },
//     {
//       name: "Payments",
//       href: "/account/transaction",
//       icon: PaymentIcon,
//     },
//   ];

//   // Set the active item based on the current URL when the component mounts
//   useEffect(() => {
//     const currentPath = location.pathname;
//     const allItems = [...generalItems, ...aiToolsItems, ...accountItems];
//     const matchingItem = allItems.find((item) => currentPath === item.href);
//     if (matchingItem) {
//       setActiveItem(matchingItem.name);
//     }
//   }, [location.pathname, setActiveItem]);

//   const handleItemClick = (itemName: string, href: string) => {
//     setActiveItem(itemName);
//     if (setIsMobileMenuOpen && isMobile) {
//       setIsMobileMenuOpen(false);
//     }
//     navigate(href);
//   };

//   const SidebarContent = () => (
//     <aside className="flex flex-col bg-[#1A1A1A] h-full border-r border-gray-700 ">
//       {/* Logo */}
//       <div className="flex justify-center">
//         <div className="flex items-center gap-3 mt-6">
//           {!isMobile && !isExpanded ? (
//             <div className="flex flex-col items-center gap-4">
//               <img src="/png/about_logo.png" className="w-[40px]" alt="Logo" />
//               <button
//                 onClick={toggleSidebar}
//                 className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
//                 title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
//               >
//                 <PanelRightClose className="w-5 h-5" />
//               </button>
//             </div>
//           ) : (
//             <>
//               <img
//                 src="/png/new_miragic_logo.png"
//                 className="w-[160px]"
//                 alt="Logo"
//               />
//               <button
//                 onClick={toggleSidebar}
//                 className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
//                 title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
//               >
//                 <PanelRightOpen className="w-5 h-5" />
//               </button>
//             </>
//           )}
//         </div>
//       </div>

//       {/* Navigation and User Info (Scrollable) */}
//       <div className="flex-1 overflow-y-auto overflow-x-hidden">
//         {/* General Section */}
//         <div
//           className={`${!isMobile && !isExpanded ? "px-3" : "px-6"} mb-4 mt-8`}
//         >
//           {(isMobile || isExpanded) && (
//             <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
//               GENERAL
//             </h3>
//           )}
//           <nav
//             className={`space-y-1 ${
//               !isExpanded && !isMobile ? "pb-6 border-b-2 border-gray-500" : ""
//             }`}
//           >
//             {generalItems.map((item) => {
//               const isActive = activeItem === item.name;
//               const activeColor = "#9855FF";
//               const IconComponent = item.icon;
//               return (
//                 <button
//                   key={item.name}
//                   onClick={() => handleItemClick(item.name, item.href)}
//                   className={`group relative w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg cursor-pointer ${
//                     isActive
//                       ? `text-[${activeColor}]`
//                       : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
//                   } ${
//                     !isMobile && !isExpanded
//                       ? "flex justify-center"
//                       : "flex items-center space-x-3"
//                   }`}
//                   title={!isMobile && !isExpanded ? item.name : undefined}
//                 >
//                   <IconComponent color={isActive ? activeColor : "white"} />
//                   {(isMobile || isExpanded) && (
//                     <span className="truncate">{item.name}</span>
//                   )}

//                   {/* Tooltip for collapsed state */}
//                   {!isMobile && !isExpanded && (
//                     <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
//                       {item.name}
//                     </div>
//                   )}
//                 </button>
//               );
//             })}
//           </nav>
//         </div>

//         {/* AI Tools Section */}
//         <div
//           className={`${!isMobile && !isExpanded ? "px-3 pt-3" : "px-6"} mb-4`}
//         >
//           {(isMobile || isExpanded) && (
//             <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
//               AI TOOLS
//             </h3>
//           )}
//           <nav className="space-y-1">
//             {aiToolsItems.map((item) => {
//               const isActive = activeItem === item.name;
//               const activeColor = "#9855FF";
//               const IconComponent = item.icon;
//               return (
//                 <button
//                   key={item.name}
//                   onClick={() => handleItemClick(item.name, item.href)}
//                   className={`group relative cursor-pointer w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
//                     isActive
//                       ? `text-[${activeColor}]`
//                       : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
//                   } ${
//                     !isMobile && !isExpanded
//                       ? "flex justify-center"
//                       : "flex items-center space-x-3"
//                   }`}
//                   title={!isMobile && !isExpanded ? item.name : undefined}
//                 >
//                   <IconComponent color={isActive ? activeColor : "white"} />
//                   {(isMobile || isExpanded) && (
//                     <span className="truncate">{item.name}</span>
//                   )}

//                   {/* Tooltip for collapsed state */}
//                   {!isMobile && !isExpanded && (
//                     <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
//                       {item.name}
//                     </div>
//                   )}
//                 </button>
//               );
//             })}
//           </nav>
//         </div>

//         {/* Account Section */}
//         <div
//           className={`${!isMobile && !isExpanded ? "px-3 pt-3" : "px-6"} mb-4`}
//         >
//           {(isMobile || isExpanded) && (
//             <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
//               Account
//             </h3>
//           )}
//           <nav className="space-y-1">
//             {accountItems.map((item) => {
//               const isActive = activeItem === item.name;
//               const activeColor = "#9855FF";
//               const IconComponent = item.icon;
//               return (
//                 <button
//                   key={item.name}
//                   onClick={() => handleItemClick(item.name, item.href)}
//                   className={`group relative cursor-pointer w-full text-left px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
//                     isActive
//                       ? `text-[${activeColor}]`
//                       : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
//                   } ${
//                     !isMobile && !isExpanded
//                       ? "flex justify-center"
//                       : "flex items-center space-x-3"
//                   }`}
//                   title={!isMobile && !isExpanded ? item.name : undefined}
//                 >
//                   <IconComponent color={isActive ? activeColor : "white"} />
//                   {(isMobile || isExpanded) && (
//                     <span className="truncate">{item.name}</span>
//                   )}

//                   {/* Tooltip for collapsed state */}
//                   {!isMobile && !isExpanded && (
//                     <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
//                       {item.name}
//                     </div>
//                   )}
//                 </button>
//               );
//             })}
//           </nav>
//         </div>

//         {/* Extra Space at the Bottom */}
//         <div className="h-6"></div>
//       </div>
//     </aside>
//   );

//   return <SidebarContent />;
// };

// export default UserSidebar;
