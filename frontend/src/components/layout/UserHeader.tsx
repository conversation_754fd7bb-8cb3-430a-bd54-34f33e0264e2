import { Globe, Bell, User, LogOut } from "lucide-react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useEffect, useState } from "react";
import UpdateAccountModal from "@/components/dashboard/UpdateAccountModal";
import ShadowButton from "@/components//ui/shadowButton";
import CreditService from "@/services/credit.service";
import { useApp } from "@/contexts/useApp";
import { useNavigate } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { UPLOAD_URL } from "@/services/api.service";

interface UserHeaderProps {
  setIsMobileMenuOpen: (isOpen: boolean) => void;
  // toggleSidebar?: () => void;
  // isSidebarExpanded?: boolean;
}

const UserHeader = ({
  setIsMobileMenuOpen,
}: // toggleSidebar,
// isSidebarExpanded = false,
UserHeaderProps) => {
  const { logout, setUserCredits, userCredits, user } = useApp();
  // const [loading, setLoading] = useState<boolean>(true);
  const [isOpenUpdateAccModal, setIsOpenUpdateAccModal] = useState(false);
  const navigate = useNavigate();

  const handleProfile = () => {
    // Add your profile navigation logic here
    navigate("/dashboard/profile");
    console.log("Navigate to profile");
  };

  const handleLogout = async () => {
    return logout();
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        // setLoading(true);

        // Fetch credit balance
        const balanceResponse = await CreditService.getUserCreditBalance();
        // setCreditBalance(balanceResponse.data);
        setUserCredits(balanceResponse?.data);
      } catch (error) {
        console.error("Error fetching billing data:", error);
      }
      // finally {
      //   setLoading(false);
      // }
    };

    fetchData();
  }, []);
  console.log("user", user);
  return (
    <>
      <header className="bg-[#1A1A1A] px-4 pb-4 pt-6 flex items-center justify-between gap-6">
        {/* Left Side - Sidebar Toggle (Desktop) + Contact Sales */}
        <div className="flex items-center gap-4">
          {/* {toggleSidebar && (
            <button
              onClick={toggleSidebar}
              className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
              title={isSidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
            >
              <Menu className="w-5 h-5" />
            </button>
          )} */}
        </div>

        {/* Right Side - Upgrade Button, Credits, Notifications, and User Avatar */}
        <div className="flex items-center gap-6">
          {/* Contact Sales */}
          <div className="flex items-center text-gray-400 hover:text-gray-200 cursor-pointer">
            <button className="rounded-lg transition-colors cursor-pointer">
              <Globe className="w-5 h-5" />
            </button>
            <button className="text-sm font-medium px-3 py-1 rounded-lg transition-colors cursor-pointer">
              Contact Sales
            </button>
          </div>
          {/* Center - Upgrade Button */}
          <div className="flex items-center space-x-2">
            <ShadowButton
              onClick={() => setIsOpenUpdateAccModal(true)}
              className="z-10 px-6 py-[6px] text-white text-base rounded-lg cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
            >
              Upgrade -{" "}
              {user?.subscription?.status === "ACTIVE"
                ? "Subscribed"
                : user?.creditPurchase?.status === "COMPLETED"
                ? "Top Up"
                : "Free Trial"}
            </ShadowButton>
          </div>

          {/* Credits, Notifications, and User Avatar */}
          <div className="flex items-center space-x-4">
            <div className="text-gray-400 text-sm font-medium px-3 py-2 rounded-lg border border-gray-500 cursor-pointer hover:bg-gray-800 hover:text-white">
              Credits |{" "}
              <span className="text-[#9855FF]">
                {userCredits?.balance?.toLocaleString() || "0"}
              </span>
            </div>
            {/*loading ? (
              <div className="col-span-full flex items-center justify-center py-12">
                <RefreshCw className="animate-spin h-8 w-8 text-primary" />
              </div>
            ) : (
              
            )*/}

            <button className="text-gray-400 hover:text-gray-200 transition-colors rounded-full p-2 border border-gray-500">
              <Bell className="w-5 h-5" />
            </button>

            {/* User Avatar with Dropdown */}
            <DropdownMenu.Root>
              <DropdownMenu.Trigger asChild>
                <Avatar className="w-8 h-8 rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all">
                  <AvatarImage
                    src={UPLOAD_URL + user?.profile.avatarUrl}
                    alt={user?.profile?.firstName}
                  />
                  <AvatarFallback className="text-2xl font-bold text-black">
                    {user?.profile?.firstName?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </DropdownMenu.Trigger>

              <DropdownMenu.Portal>
                <DropdownMenu.Content
                  className="min-w-[160px] bg-[#2A2A2A] rounded-lg p-1 shadow-lg border border-gray-600 z-50"
                  sideOffset={5}
                  align="end"
                >
                  <DropdownMenu.Item
                    className="flex items-center gap-2 px-3 py-2 text-sm text-gray-200 hover:bg-gray-700 hover:text-white rounded cursor-pointer outline-none"
                    onClick={handleProfile}
                  >
                    <User className="w-4 h-4" />
                    Profile
                  </DropdownMenu.Item>

                  <DropdownMenu.Separator className="h-px bg-gray-600 my-1" />

                  <DropdownMenu.Item
                    className="flex items-center gap-2 px-3 py-2 text-sm text-gray-200 hover:bg-red-600 hover:text-white rounded cursor-pointer outline-none"
                    onClick={handleLogout}
                  >
                    <LogOut className="w-4 h-4" />
                    Logout
                  </DropdownMenu.Item>
                </DropdownMenu.Content>
              </DropdownMenu.Portal>
            </DropdownMenu.Root>
          </div>

          {/* Mobile Menu Button - Hidden on larger screens */}
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="lg:hidden p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h16m-7 6h7"
              />
            </svg>
          </button>
        </div>
      </header>
      <UpdateAccountModal
        isOpen={isOpenUpdateAccModal}
        onClose={() => setIsOpenUpdateAccModal(false)}
      />
    </>
  );
};

export default UserHeader;

// import { Globe, Bell, User, LogOut, RefreshCw, } from "lucide-react";
// import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
// import { useEffect, useState } from "react";
// import UpdateAccountModal from "@/components/dashboard/UpdateAccountModal";
// import ShadowButton from "@/components//ui/shadowButton";
// import CreditService from "@/services/credit.service";
// import { useApp } from "@/contexts/useApp";
// import { useNavigate } from "react-router-dom";

// interface UserHeaderProps {
//   setIsMobileMenuOpen: (isOpen: boolean) => void;
//   // toggleSidebar?: () => void;
//   // isSidebarExpanded?: boolean;
// }

// const UserHeader = ({
//   setIsMobileMenuOpen,
//   // toggleSidebar,
//   // isSidebarExpanded = false,
// }: UserHeaderProps) => {
//   const { logout, setUserCredits, userCredits } = useApp();
//   const [loading, setLoading] = useState<boolean>(true);
//   const [isOpenUpdateAccModal, setIsOpenUpdateAccModal] = useState(false);
//   const navigate = useNavigate();

//   const handleProfile = () => {
//     // Add your profile navigation logic here
//     navigate("/dashboard/profile");
//     console.log("Navigate to profile");
//   };

//   const handleLogout = async () => {
//     return logout();
//   };

//   useEffect(() => {
//     const fetchData = async () => {
//       try {
//         setLoading(true);

//         // Fetch credit balance
//         const balanceResponse = await CreditService.getUserCreditBalance();
//         // setCreditBalance(balanceResponse.data);
//         setUserCredits(balanceResponse.data);
//       } catch (error) {
//         console.error("Error fetching billing data:", error);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchData();
//   }, []);

//   return (
//     <>
//       <header className="bg-[#1A1A1A] px-4 pb-4 pt-6 flex items-center justify-between gap-6">
//         {/* Left Side - Sidebar Toggle (Desktop) + Contact Sales */}
//         <div className="flex items-center gap-4">
//           {/* {toggleSidebar && (
//             <button
//               onClick={toggleSidebar}
//               className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
//               title={isSidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
//             >
//               <Menu className="w-5 h-5" />
//             </button>
//           )} */}
//         </div>

//         {/* Right Side - Upgrade Button, Credits, Notifications, and User Avatar */}
//         <div className="flex items-center gap-6">
//           {/* Contact Sales */}
//           <div className="flex items-center text-gray-400 hover:text-gray-200 cursor-pointer">
//             <button className="rounded-lg transition-colors cursor-pointer">
//               <Globe className="w-5 h-5" />
//             </button>
//             <button className="text-sm font-medium px-3 py-1 rounded-lg transition-colors cursor-pointer">
//               Contact Sales
//             </button>
//           </div>
//           {/* Center - Upgrade Button */}
//           <div className="flex items-center space-x-2">
//             <ShadowButton
//               onClick={() => setIsOpenUpdateAccModal(true)}
//               className="z-10 px-6 py-[6px] text-white text-base rounded-lg cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
//             >
//               Upgrade - Free Trial
//             </ShadowButton>
//           </div>

//           {/* Credits, Notifications, and User Avatar */}
//           <div className="flex items-center space-x-4">
//             {loading ? (
//               <div className="col-span-full flex items-center justify-center py-12">
//                 <RefreshCw className="animate-spin h-8 w-8 text-primary" />
//               </div>
//             ) : (
//               <div className="text-gray-400 text-sm font-medium px-3 py-2 rounded-lg border border-gray-500 cursor-pointer hover:bg-gray-800 hover:text-white">
//                 Credits |{" "}
//                 <span className="text-[#9855FF]">
//                   {userCredits?.balance.toLocaleString() || "0"}
//                 </span>
//               </div>
//             )}

//             <button className="text-gray-400 hover:text-gray-200 transition-colors rounded-full p-2 border border-gray-500">
//               <Bell className="w-5 h-5" />
//             </button>

//             {/* User Avatar with Dropdown */}
//             <DropdownMenu.Root>
//               <DropdownMenu.Trigger asChild>
//                 <div className="w-8 h-8 rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all">
//                   <img
//                     src="/png/avatar1.png"
//                     alt="User Avatar"
//                     className="w-full h-full object-cover"
//                   />
//                 </div>
//               </DropdownMenu.Trigger>

//               <DropdownMenu.Portal>
//                 <DropdownMenu.Content
//                   className="min-w-[160px] bg-[#2A2A2A] rounded-lg p-1 shadow-lg border border-gray-600 z-50"
//                   sideOffset={5}
//                   align="end"
//                 >
//                   <DropdownMenu.Item
//                     className="flex items-center gap-2 px-3 py-2 text-sm text-gray-200 hover:bg-gray-700 hover:text-white rounded cursor-pointer outline-none"
//                     onClick={handleProfile}
//                   >
//                     <User className="w-4 h-4" />
//                     Profile
//                   </DropdownMenu.Item>

//                   <DropdownMenu.Separator className="h-px bg-gray-600 my-1" />

//                   <DropdownMenu.Item
//                     className="flex items-center gap-2 px-3 py-2 text-sm text-gray-200 hover:bg-red-600 hover:text-white rounded cursor-pointer outline-none"
//                     onClick={handleLogout}
//                   >
//                     <LogOut className="w-4 h-4" />
//                     Logout
//                   </DropdownMenu.Item>
//                 </DropdownMenu.Content>
//               </DropdownMenu.Portal>
//             </DropdownMenu.Root>
//           </div>

//           {/* Mobile Menu Button - Hidden on larger screens */}
//           <button
//             onClick={() => setIsMobileMenuOpen(true)}
//             className="lg:hidden p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
//           >
//             <svg
//               className="w-6 h-6"
//               fill="none"
//               stroke="currentColor"
//               viewBox="0 0 24 24"
//               xmlns="http://www.w3.org/2000/svg"
//             >
//               <path
//                 strokeLinecap="round"
//                 strokeLinejoin="round"
//                 strokeWidth="2"
//                 d="M4 6h16M4 12h16m-7 6h7"
//               />
//             </svg>
//           </button>
//         </div>
//       </header>
//       <UpdateAccountModal
//         isOpen={isOpenUpdateAccModal}
//         onClose={() => setIsOpenUpdateAccModal(false)}
//       />
//     </>
//   );
// };

// export default UserHeader;
