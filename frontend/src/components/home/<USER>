import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "../ui/button";
import { AiToolIcon } from "@/lib/icons";

const SpeedPainting = () => {
  const navigate = useNavigate();
  return (
    <div className="flex flex-col lg:flex-row justify-between items-center mx-auto p-12 font-sans bg-[#222222] gap-12 z-10 rounded-3xl min-h-[400px] border border-gray-600 w-full max-w-7xl">
      {/* Left Section - Content */}
      <div className="flex flex-col justify-center gap-8 h-full lg:w-1/2">
        {/* AI Tool Badge */}
        <div className="flex justify-center lg:justify-start">
          <Button className="flex items-center !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition w-fit">
            <AiToolIcon />
            <span>AI Tool</span>
          </Button>
        </div>

        {/* Heading */}
        <div className="text-center lg:text-left">
          <h2 className="text-4xl lg:text-5xl text-white font-semibold mb-6">
            Speed Painting
          </h2>

          {/* Description */}
          <p className="text-xl lg:text-2xl text-white/80 leading-relaxed max-w-lg">
            Turn your image into a Hand-Drawn Video!
            <br />
            <span className="text-white/65">
              Just upload your image then pick your options.
            </span>
          </p>
        </div>
        <div className="mt-2">
          <Button
            onClick={() => navigate("/")}
            className="flex items-center !px-8 py-3 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition backdrop-blur-sm"
          >
            <span>Try Now</span>
          </Button>
        </div>
      </div>

      {/* Right Section - Video */}
      <div className="flex items-center justify-center lg:w-1/2 w-full">
        <div className="relative w-full max-w-[500px] aspect-video rounded-2xl overflow-hidden border-2 border-gray-600 shadow-[0_0_20px_rgba(129,90,219,0.3)]">
          <iframe
            className="absolute top-0 left-0 w-full h-full"
            src="https://www.youtube.com/embed/_mtvNOyd2cQ?autoplay=1&mute=0&loop=0&controls=1&rel=0&modestbranding=1"
            title="YouTube video player"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />

          {/* Optional overlay for styling */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none"></div>

          {/* Custom play button overlay (optional) */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20 opacity-0 hover:opacity-100 transition-opacity duration-300">
              <svg
                className="w-8 h-8 text-white ml-1"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpeedPainting;
