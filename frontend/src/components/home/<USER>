import { useEffect, useState } from "react";

// Image will be used in future updates
// import section_3_image from "/png/section_3_landing_page.png";
import TrustedTeam from "./TrustedTeam";
import { GenerationAILogo, SeperatorLineIcon } from "@/lib/icons";
import {
  Bell,
  ChevronRight,
  GraduationCap,
  ShoppingCart,
  Siren,
} from "lucide-react";
import MySlider from "./MySlider";
import Section4Cards from "./Section4Cards";
import CaseStudiesAndRelatedArticle from "./CaseStudiesAndRelatedArticle";
import LandingPageFAQ from "./LandingPageFAQ";
import BackgroundRemover from "./BackgroundRemover";
import SpeedPainting from "./Speedpainting";
import VirtualTryOn from "./VirtualTryOn";
import { useNavigate } from "react-router-dom";
import { useApp } from "@/contexts/useApp";
import LoginModal from "../auth/LoginModal";
import SignUpModal from "../auth/SignUpModal";
import ForgotPasswordModal from "../auth/ForgotPasswordModal";
import type { ApiBlogPost, BlogPost } from "@/services/blog.service";
import BlogService from "@/services/blog.service";


interface PaginatedApiBlogPosts {
  posts: ApiBlogPost[];
  totalCount?: number;
  totalPages?: number;
  currentPage?: number;
}

const HeroSection = () => {
  const { isAuthenticated } = useApp();

  const FEATURES: string[] = [
    "Background Remover",
    "Virtual Try On",
    "Speedpainting",
  ];

  const GENERATIVE_AI = [
    { title: "Advertising", Icon: Bell },
    { title: "Education", Icon: GraduationCap },
    { title: "Inspiration", Icon: Siren },
    { title: "E-Commerce", Icon: ShoppingCart },
  ];

  const [activeFeature, setActiveFeature] =
    useState<string>("Background Remover");

  const navigate = useNavigate();
  const [isShowSignUpModal, setIsShowSignUpModal] = useState(false);

  const [isShowLoginModal, setIsShowLoginModal] = useState(false);

  const [isShowForgotPassModal, setIsShowForgotPassModal] = useState(false);
  const [blogData, setBlogData] = useState<BlogPost[]>([]);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        const response = (await BlogService.getPosts({
          // page: currentPage,
          limit: 10,
          // search: searchTerm,
        })) as unknown as PaginatedApiBlogPosts;
        
        const transformedData: BlogPost[] = response.posts.map(
          (post: ApiBlogPost) => ({
            id: post.id,
            title: post.title,
            slug: post.slug,
            content: post.content || "",
            excerpt: post.excerpt || "",
            status: post.status || "PUBLISHED",
            authorId: post.authorId || post.author.id,
            categoryIds: post.categories.map((cat) => cat.id),
            tagIds: post.tags.map((tag) => tag.id),
            publishedAt: post.publishedAt || undefined,
            createdAt: post.createdAt,
            updatedAt: post.updatedAt,
            featuredImageUrl: post.featuredImageUrl || undefined,
            // Store the full objects for display purposes
            categories: post.categories,
            tags: post.tags,
            author: post.author
          })
        );
    
        setBlogData(transformedData);
      } catch (error) {
        console.error("Error fetching blog posts:", error);
      } finally {
        // Your finally block code here
      }
    };

    fetchBlogPosts();
  }, []);

  return (
    <div>
      <section className="relative w-full overflow-hidden px-10 bg-primary">
        <div className="z-10 text-white space-y-6 flex flex-col">
          <div className="z-10 border-1 border-gray-700 py-1 rounded-full flex w-fit gap-4 px-4 items-center mt-10 text-xs">
            <span className="z-10 bg-purple-400 rounded-full text-primary px-3 py-1">
              New
            </span>
            <span className="z-10 text-xs text-purple-400  uppercase tracking-widest">
              Latest integration just arrived
            </span>
          </div>
          <h1 className="z-10 text-5xl sm:text-5xl sm:text-left md:text-6xl xl:text-7xl font-semibold leading-tight font-inter mt-5">
            <span className="z-10 text-gray-300">Design The Future</span> <br />
            <span className="z-10 text-white">with Miragic AI</span>
          </h1>
          <p className="z-10 text-gray-400 text-lg max-w-md">
            No Code, No Design, No Problem
          </p>
          <div className="flex gap-4 z-10">
            <button
              onClick={() =>
                isAuthenticated
                  ? navigate("/signup")
                  : setIsShowSignUpModal(true)
              }
              className="z-10 px-6 text-white text-base rounded-lg cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
            >
              Get Started
            </button>
            <button
              onClick={() => navigate("/docs-api")}
              className="z-10 border border-gray-500 cursor-pointer hover:border-white text-white font-semibold px-6 py-2 rounded-lg"
            >
              API Docs
            </button>
          </div>
        </div>
        <div className="pb-20 mt-36 ">
          <TrustedTeam />
        </div>
        <img
          className="absolute top-0 left-0 opacity-25"
          src="/png/pattern_1.png"
        />
        <img className="absolute top-0 left-0 z-[5px]" src="/png/stars_1.png" />
        <img
          className="absolute bottom-0 right-0 w-4/5 z-[5px]"
          src="/png/shadow_hero_sec.png"
        />
      </section>
      {/* 2nd section */}
      <section className="relative w-full  bg-primary px-10">
        <div>
          <div className="flex justify-center">
            <h2 className="max-w-[550px] font-semibold text-4xl text-center font-inter text-white z-20">
              Boost your site's visibility with AI-powered Miragic tools
            </h2>
          </div>
          <div className="mt-9 flex flex-wrap justify-center gap-3">
            {FEATURES?.map((title: string) => (
              <p
                onClick={() => setActiveFeature(title)}
                className={`z-20 text-lg cursor-pointer ${
                  activeFeature === title ? "text-white" : "text-gray-400"
                }`}
                key={title}
              >
                {title}
              </p>
            ))}
          </div>
          <div className="flex justify-center items-center mt-16">
            {/* <BackgroundRemover /> */}
            {activeFeature === "Background Remover" ? (
              <BackgroundRemover />
            ) : activeFeature === "Virtual Try On" ? (
              <VirtualTryOn />
            ) : activeFeature === "Speedpainting" ? (
              <SpeedPainting />
            ) : null}
          </div>
        </div>
        {/* Shadows & stars */}
        <img
          className="absolute top-0 left-0 opacity-25"
          src="/png/pattern_1.png"
        />
        <img className="absolute top-0 left-0 z-[5]" src="/png/stars_1.png" />
        <img
          className="absolute top-0 right-0 w-4/5 z-[5]"
          src="/png/shadow_bottom_first_sec_2.png"
        />
      </section>
      <section className="relative w-full overflow-hidden bg-primary px-10 pb-16 min-h-screen pt-12">
        <img
          className="absolute top-[-430px]"
          src="/png/section_3_backg_ellipse.png"
        />
        <div className="flex justify-center min-h-[260px] z-10">
          <div className="relative w-5xl px-8 flex items-center">
            {/* Backgroun image */}
            <div
              className="absolute inset-0 bg-cover bg-center rounded-3xl border border-gray-600"
              style={{
                backgroundImage: 'url("/png/section_3_landing_page.png")',
              }}
            ></div>
            {/* Overlay content */}
            <div className="relative z-10 text-white flex flex-col justify-center max-w-[500px]">
              <h2 className="text-4xl">Free Online Tools</h2>
              <p className="text-gray-400 text-xl mt-2">
                Content Generation for your marketing
              </p>
            </div>
          </div>
        </div>
        <div className="my-14 flex justify-center gap-16">
          <div className="flex flex-col items-center gap-4 z-20">
            <p className="text-9xl font-semibold text-white font-inter">1.2M</p>
            <p className="text-sm text-gray-400 font-inter">
              Join Miragic users
            </p>
          </div>
          <div className="flex flex-col items-center gap-4 z-20">
            <p className="text-9xl font-semibold text-white font-inter">20K</p>
            <p className="text-sm text-gray-400 font-inter">Companies</p>
          </div>
        </div>
        <div className="flex justify-center min-h-[260px] z-10">
          <div className="relative w-5xl px-8 flex items-center">
            {/* Background image */}
            <div
              className="absolute inset-0 bg-cover bg-center rounded-3xl border border-gray-600"
              style={{
                backgroundImage: 'url("/png/section_3_discover_consumer.png")',
                backgroundPosition: "center",
              }}
            ></div>
            {/* Overlay content */}
            <div className="relative z-10 text-white flex flex-col justify-center max-w-[700px]">
              <h2 className="text-4xl">Discover Consumer Products</h2>
              <p className="text-gray-400 text-xl mt-2">
                Free Image Background Removal, Virtual Try On, Speedpainting in
                One Platform
              </p>
            </div>
          </div>
        </div>
        {/* <div className="mt-16">
          <TrustedTeam />
        </div> */}
      </section>
      <section className="bg-primary">
        <div className="min-h-[1024px] bg-gradient-to-b from-[#6929BE] to-primary to-60% rounded-t-4xl pb-20">
          <div className="flex justify-center pt-16">
            <div className="text-4xl font-inter font-semibold max-w-4xl text-white text-center leading-none">
              Immersive Brand Experiences Through The Power of Generative AI
              <span className="inline-block align-baseline ml-3 pt-3">
                <GenerationAILogo />
              </span>
            </div>
          </div>
          <div className="flex flex-col items-center justify-center md:flex-row gap-32 mt-20">
            <div className="flex flex-col gap-9 ml-36">
              {GENERATIVE_AI.map(({ title, Icon }, i) => (
                <div key={i} className="flex items-center gap-3 text-white">
                  <Icon />
                  <span className="text-3xl font-semibold">{title}</span>
                </div>
              ))}
            </div>
            <div className="max-w-[490px] max-h-[450px] overflow-hidden rounded-4xl flex items-center justify-center">
              <img src="/png/advertise.png" className="object-cover" />
            </div>
          </div>
          <div className="mt-28">
            <MySlider />
          </div>
        </div>
      </section>
      <section className="bg-primary hidden pb-10">
        <SeperatorLineIcon />
        <p className="text-white text-xl px-10 text-center mt-16 mb-16">
          Miragic is becoming an industry standard for marketing and advertising
          creators and innovators with{" "}
          <span className="text-secondary">millions</span> of assets generated
        </p>
        <div className="relative">
          <img
            src="/png/blackShadowReviewTop.png"
            className="absolute top-0 z-10 opacity-[40%] w-full h-[500px]"
          />
          <Section4Cards />
          <img
            src="/png/blackShadowReview.png"
            className="absolute bottom-0 w-full"
          />
        </div>
      </section>
      <section className="bg-primary pb-16">
        <SeperatorLineIcon />
        {/* <div className="pt-16 mx-auto pl-16">
          <CaseStudiesAndRelatedArticle
            title="Case Studies"
            width={320}
            titlePadding="pl-6 pr-14"
          />
        </div>
        <div className="pt-20">
          <SeperatorLineIcon />
        </div> */}
        <div className="pt-16 mx-auto pl-16">
          <CaseStudiesAndRelatedArticle
            title="Related Articles"
            width={350}
            titlePadding="pl-6 pr-14"
            data={blogData}
          />
        </div>
      </section>
      <section className="bg-primary pb-20 px-20">
        <LandingPageFAQ />
      </section>
      <section className="relative bg-primary pb-10 px-20">
        <div className="z-20 flex justify-center">
          <button className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-4 py-2 hover:bg-gray-900 cursor-pointer">
            <span className="text-white">See More</span>{" "}
            <ChevronRight className="text-gray-400" />
          </button>
        </div>
        <div className="z-20 min-h-[700px] w-full flex flex-col justify-center items-center font-inter">
          <h1 className="z-20 text-white text-6xl text-center">
            Ready to Transform Your Marketing?
          </h1>
          <h2 className="z-20 text-gray-400 text-4xl mt-7 max-w-4xl text-center">
            Experience AI-powered marketing that transforms, engages, and drives
            real results!
          </h2>
          <div className="flex gap-4 z-20 mt-16">
            <button
              onClick={() =>
                isAuthenticated
                  ? navigate("/signup")
                  : setIsShowSignUpModal(true)
              }
              className="z-20 px-6 text-white text-base rounded-lg cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
            >
              Get Started
            </button>

            <button
              onClick={() => navigate("/docs-api")}
              className="z-20 border border-gray-200 cursor-pointer hover:border-white text-white font-semibold px-6 py-2 rounded-lg"
            >
              API Docs
            </button>
          </div>
        </div>
        <img
          className="absolute top-0 left-0 opacity-25 w-full h-full object-cover"
          src="/png/pattern_1.png"
          alt="Background pattern"
        />
        <img
          className="absolute top-0 left-0 z-10 w-full h-full object-cover"
          src="/png/stars_1.png"
          alt="Stars overlay"
        />
        <img
          className="absolute bottom-0 left-0 w-1/2 h-auto object-contain"
          src="/png/step_intoFut_shadow_1.png"
          alt="Shadow 1"
        />
        <img
          className="absolute bottom-0 left-1/2 w-1/2 h-auto object-contain"
          src="/png/step_intoFut_shadow_2.png"
          alt="Shadow 2"
        />
      </section>

      <LoginModal
        isOpen={isShowLoginModal}
        onClose={() => setIsShowLoginModal(false)}
        onClickSignUpModal={() => {
          setIsShowLoginModal(false);
          setIsShowForgotPassModal(false);
          setIsShowSignUpModal(true);
        }}
        onClickForgotPassword={() => {
          setIsShowLoginModal(false);
          setIsShowSignUpModal(false);
          setIsShowForgotPassModal(true);
        }}
      />
      <SignUpModal
        isOpen={isShowSignUpModal}
        onClose={() => setIsShowSignUpModal(false)}
        onClickLoginModal={() => {
          setIsShowLoginModal(true);
          setIsShowSignUpModal(false);
        }}
      />
      <ForgotPasswordModal
        isOpen={isShowForgotPassModal}
        onClose={() => setIsShowForgotPassModal(false)}
        onBack={() => {
          setIsShowForgotPassModal(false);
          setIsShowLoginModal(true);
        }}
      />
    </div>
  );
};

export default HeroSection;
