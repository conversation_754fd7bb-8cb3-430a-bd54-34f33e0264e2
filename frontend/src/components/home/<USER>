import { useCallback, useState, useEffect } from "react";
import type { DragEvent } from "react";
import { But<PERSON> } from "../ui/button";
import { AiToolIcon } from "@/lib/icons";
import { ChevronRight, Plus, RefreshCw } from "lucide-react";

const SpeedPainting = () => {
  // const [isDragging, setIsDragging] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>();
  const [previewUrl, setPreviewUrl] = useState<string | null>("");
  const [fileType, setFileType] = useState<string | null>("");
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      setSelectedFile(file);

      if (file.type.startsWith("image/")) {
        setFileType("image");
      } else if (file.type.startsWith("video/")) {
        setFileType("video");
      } else {
        // Unsupported file type
        setFileType(null);
        setPreviewUrl(null);
        alert("Please select an image or video file.");
      }
    } else {
      setSelectedFile(null);
      setFileType(null);
      setPreviewUrl(null);
    }
  };
  useEffect(() => {
    if (selectedFile) {
      const url = URL.createObjectURL(selectedFile);
      setPreviewUrl(url);

      return () => {
        URL.revokeObjectURL(url);
        setPreviewUrl(null);
      };
    }
  }, [selectedFile]);

  const reset = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();

    setSelectedFile(null);
    setPreviewUrl(null);
    setFileType(null);
  };

  const handleDragEnter = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  }, []);

  // const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
  //   if (e.target.files && e.target.files.length > 0) {
  //     setSelectedFile(e.target.files[0]);
  //   }
  // };

  return (
    <div className="flex flex-col md:flex-row justify-between mx-auto p-12 font-sans text-center bg-[#222222] gap-10 z-10 rounded-3xl min-h-[400px] border border-gray-600 w-5xl">
      <div className="flex flex-col justify-between gap-12 h-full">
        <div className="flex flex-col items-center md:items-start">
          {fileType !== "video" && (
            <Button className="flex items-center  mb-10 !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
              <AiToolIcon />
              <span>AI Tool</span>
            </Button>
          )}

          <h2 className="text-4xl text-white font-semibold">Speedpainting</h2>
          <p className="mt-3 text-xl text-left text-white/65">
            Turn your image into a Hand-Drawn Video! <br /> Just upload your
            image then pick your options.
          </p>
        </div>

        {fileType === "video" && (
          <Button className="flex max-w-fit items-center !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
            {/* <AiToolIcon /> */}
            <span>Try it now</span>
            <ChevronRight />
          </Button>
        )}
      </div>
      <div
        className={`border-2 border-dashed border-gray-500 rounded-lg bg-white/5 flex items-center justify-center w-full max-w-[450px] min-h-[200px] max-h-[350px] relative ${
          selectedFile ? "" : "p-8"
        }`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="file-upload"
          accept=""
          onChange={handleFileChange}
          className="hidden"
        />
        <label
          htmlFor="file-upload"
          className="cursor-pointer relative w-full h-full flex items-center justify-center"
        >
          {selectedFile && previewUrl ? (
            fileType === "image" ? (
              <div className="relative w-full h-full ">
                <img
                  src={previewUrl}
                  alt="Selected preview"
                  className="w-full h-full object-cover rounded-lg"
                />
                <div className="absolute px-5 min-h-20 flex items-center justify-center gap-5 transform -translate-y-4 -translate-x-1/2  bottom-0 left-1/2 rounded-[10px] border border-[rgba(255,255,255,0.2)] bg-[rgba(14,14,20,0.83)] backdrop-blur-[10.9px]">
                  <button
                    onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                      reset(e)
                    }
                    className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-4 py-2 hover:bg-gray-900 cursor-pointer"
                  >
                    <RefreshCw className="text-gray-400" />
                  </button>
                  <Button
                    className="min-w-[120px] min-h-[42px] px-4 py-2 rounded-[10px] border border-white/15 bg-[#8C45FF]/40 shadow-[inset_0px_0px_6px_3px_rgba(255,255,255,0.25)] backdrop-blur-[7px]"
                    variant={"gradient"}
                  >
                    Download
                  </Button>
                </div>
              </div>
            ) : (
              <div className="relative w-full h-full">
                <video className="h-full w-auto" controls>
                  <source src={previewUrl} />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute flex backdrop-blur-[7px] shadow-[inset_0px_0px_6px_3px_rgba(255,255,255,0.25)] items-center rounded-full justify-center h-[50px] w-[50px] text-white bg-[rgba(140,69,255,0.4)] backdrop-blur-[7px] transition right-1/2 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <img src="/icons/solid_play.svg" alt="play" />
                </div>
              </div>
            )
          ) : (
            <div className="flex flex-col items-center justify-center h-full">
              <Button
                onClick={() => document.getElementById("file-upload")?.click()}
                className="flex items-center !px-7 text-white text-base rounded-md cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
              >
                <Plus />
                <span>Choose a Image or Drag & Drop</span>
              </Button>
              <p className="text-white font-bold my-3">or</p>
              <p className="text-sm text-gray-500 max-w-[300px]">
                Original image must be .png, .jpg, .jpeg or webp format and 30mb
                max size.
              </p>
            </div>
          )}
        </label>
      </div>
    </div>
  );
};

export default SpeedPainting;
