import { useRef, useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import { ChevronLeft, ChevronRight } from "lucide-react";

import "swiper/css";
import "swiper/css/navigation";

const CaseStudiesAndRelatedArticle = ({
  title = "Case Studies",
  width = 320, // Default width of each slide
  titlePadding = "",
}) => {
  const swiperRef = useRef<SwiperType | null>(null);
  const [slidesPerView, setSlidesPerView] = useState(1);

  const SLIDER_DATA = [
    {
      title: "Background Remover",
      image: "/png/case_studies_removebg.png",
      description: "Remove image backgrounds instantly with AI",
    },
    {
      title: "Virtual-Try-On",
      image: "/png/case_studies_vto.png",
      description: "Try products on virtually in real-time with AI",
    },
    {
      title: "Speed Painting",
      image: "/png/case_studies_sp.png",
      description: "Create stunning digital art quickly with AI",
    }
    // ... (other slide data remains unchanged)
  ];

  // Space between slides (in pixels)
  const spaceBetween = 20;

  // Function to calculate slidesPerView based on screen width
  const calculateSlidesPerView = () => {
    const screenWidth = window.innerWidth;
    const totalSlideWidth = width + spaceBetween; // Total width per slide (including space)

    // Calculate the number of slides that can fit
    let calculatedSlides = screenWidth / totalSlideWidth;

    // Round to nearest 0.5 (e.g., 2, 2.5, 3, 3.5, 4, 4.5)
    calculatedSlides = Math.round(calculatedSlides * 2) / 2;

    // Ensure at least 1 slide, and cap at a reasonable maximum (e.g., 4.5)
    return Math.max(1, Math.min(calculatedSlides, 4.5));
  };

  // Update slidesPerView on mount and window resize
  useEffect(() => {
    const updateSlides = () => {
      setSlidesPerView(calculateSlidesPerView());
    };

    // Initial calculation
    updateSlides();

    // Add resize event listener
    window.addEventListener("resize", updateSlides);

    // Cleanup
    return () => window.removeEventListener("resize", updateSlides);
  }, [width]); // Re-run when width prop changes

  // Dynamic breakpoints based on the provided width
  const breakpoints = {
    0: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
    768: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
    1024: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
  };

  return (
    <article className="relative w-full">
      {/* Custom navigation buttons */}
      <div className={`flex justify-between ${titlePadding}`}>
        <h2 className="text-white text-4xl font-inter">{title}</h2>
        <div className="flex space-x-4">
          <button
            onClick={() => swiperRef.current?.slidePrev()}
            className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
          >
            <ChevronLeft />
          </button>
          <button
            onClick={() => swiperRef.current?.slideNext()}
            className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
          >
            <ChevronRight />
          </button>
        </div>
      </div>

      <div className="pt-14">
        <Swiper
          modules={[Navigation]}
          onSwiper={(swiper: SwiperType) => {
            swiperRef.current = swiper;
          }}
          spaceBetween={spaceBetween}
          slidesPerView={slidesPerView} // Use dynamic slidesPerView
          centeredSlides={false}
          breakpoints={breakpoints}
          className="flex justify-center"
        >
          {SLIDER_DATA.map(({ title, description, image }, index) => (
            <SwiperSlide key={index}>
              <div className="flex justify-center">
                <div
                  className={`flex flex-col gap-4 bg-white/5 border border-white/25 text-white p-6 rounded-xl shadow-md w-full mr-5 max-w-[${width}px]`}
                >
                  <img
                    src={image}
                    height={120}
                    className="w-full max-h-[120px] object-cover rounded-md"
                  />
                  <p className="font-semibold font-inter text-white text-base mt-4">
                    {title}
                  </p>
                  <p className="text-gray-400 text-base mt-2">{description}</p>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </article>
  );
};

export default CaseStudiesAndRelatedArticle;

// import { useRef } from "react";
// import { Swiper, SwiperSlide } from "swiper/react";
// import { Navigation } from "swiper/modules";
// import { Swiper as SwiperType } from "swiper";
// import { ChevronLeft, ChevronRight } from "lucide-react";

// import "swiper/css";
// import "swiper/css/navigation";

// const CaseStudiesAndRelatedArticle = ({
//   title = "Case Studies",
//   width = 320,
// }) => {
//   const swiperRef = useRef<SwiperType | null>(null);

//   const SLIDER_DATA = [
//     {
//       title: "How Miragic Face Swap Technology Powered Nigerian Airways",
//       image: "/png/case_studies_plane.png",
//       description:
//         "It is a long established fact that a reader will be distracted by the readable. It is a long established fact that a reader will be.",
//     },
//     {
//       title: "How Miragic Face Swap Technology Powered Nigerian Airways",
//       image: "/png/case_studies_plane.png",
//       description:
//         "It is a long established fact that a reader will be distracted by the readable. It is a long established fact that a reader will be.",
//     },
//     {
//       title: "How Miragic Face Swap Technology Powered Nigerian Airways",
//       image: "/png/case_studies_plane.png",
//       description:
//         "It is a long established fact that a reader will be distracted by the readable. It is a long established fact that a reader will be.",
//     },
//     {
//       title: "How Miragic Face Swap Technology Powered Nigerian Airways",
//       image: "/png/case_studies_plane.png",
//       description:
//         "It is a long established fact that a reader will be distracted by the readable. It is a long established fact that a reader will be.",
//     },
//     {
//       title: "How Miragic Face Swap Technology Powered Nigerian Airways",
//       image: "/png/case_studies_plane.png",
//       description:
//         "It is a long established fact that a reader will be distracted by the readable. It is a long established fact that a reader will be.",
//     },
//     {
//       title: "How Miragic Face Swap Technology Powered Nigerian Airways",
//       image: "/png/case_studies_plane.png",
//       description:
//         "It is a long established fact that a reader will be distracted by the readable. It is a long established fact that a reader will be.",
//     },
//   ];

//   return (
//     <article className="relative w-full mx-auto pl-10">
//       {/* Custom navigation buttons */}
//       <div className="flex justify-between pl-6 pr-14">
//         <h2 className="text-white text-4xl font-inter">{title}</h2>
//         <div className="flex space-x-4">
//           <button
//             onClick={() => swiperRef.current?.slidePrev()}
//             className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
//           >
//             <ChevronLeft />
//           </button>
//           <button
//             onClick={() => swiperRef.current?.slideNext()}
//             className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
//           >
//             <ChevronRight />
//           </button>
//         </div>
//       </div>

//       <div className="pt-14">
//         <Swiper
//           modules={[Navigation]}
//           onSwiper={(swiper) => {
//             swiperRef.current = swiper;
//           }}
//           spaceBetween={20}
//           centeredSlides={false}
//           breakpoints={{
//             0: {
//               slidesPerView: 1.2, // small screens
//             },
//             768: {
//               slidesPerView: 3, // md screens
//             },
//             1024: {
//               slidesPerView: 4, // lg screens
//             },
//           }}
//           className="flex justify-center"
//         >
//           {SLIDER_DATA.map(({ title, description, image }, index) => (
//             <SwiperSlide key={index}>
//               <div className="flex justify-center">
//                 <div
//                   className={`flex flex-col gap-4 bg-white/5 border border-white/25 text-white p-6 rounded-xl shadow-md w-full mr-5 ${
//                     width ? `max-w-[${width}px]` : "max-w-[320px]"
//                   }`}
//                 >
//                   <img
//                     src={image}
//                     height={120}
//                     className="w-full max-h-[120px] object-cover rounded-md"
//                   />
//                   <p className="font-semibold font-inter text-white text-base mt-4">
//                     {title}
//                   </p>
//                   <p className="text-gray-400 text-base mt-2">{description}</p>
//                 </div>
//               </div>
//             </SwiperSlide>
//           ))}
//         </Swiper>
//       </div>
//     </article>
//   );
// };

// export default CaseStudiesAndRelatedArticle;
