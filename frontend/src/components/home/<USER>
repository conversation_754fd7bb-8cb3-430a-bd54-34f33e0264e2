import { useCallback, useState, useEffect } from "react";
import type { ChangeE<PERSON>, DragEvent } from "react";
import { But<PERSON> } from "../ui/button";
import { AiToolIcon } from "@/lib/icons";
import {
  AlertCircle,
  ChevronRight,
  Loader2,
  Plus,
  RefreshCw,
  X,
} from "lucide-react";

import AiCustomBgSelectionModal from "../aiTools/AiCustomBgSelectionModal";
import BgRemovalService from "@/services/bgRemoval.service";

const BackgroundRemover = () => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isOpenBgSelectModal, setIsOpenBgSelectModal] =
    useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  // Generate array of model images
  const modelImages = Array.from(
    { length: 10 },
    (_, i) => `/Human_model/${i + 1}.jpg`
  );

  useEffect(() => {
    if (uploadedFile) {
      const url = URL.createObjectURL(uploadedFile);
      setPreviewUrl(url);

      return () => {
        URL.revokeObjectURL(url);
        setPreviewUrl(null);
      };
    }
  }, [uploadedFile]);

  const handleDragEnter = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    const files: FileList = e.dataTransfer.files;
    if (files.length > 0) {
      const file: File = files[0];
      setUploadedFile(file);
      setSelectedModel(null); // Clear model selection when file is uploaded
      const reader: FileReader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>): void => {
        if (e.target?.result) {
          setProcessedImage(null);
          setError(null);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
    const files: FileList | null = e.target.files;
    if (files && files.length > 0) {
      const file: File = files[0];
      setUploadedFile(file);
      setSelectedModel(null); // Clear model selection when file is uploaded
      const reader: FileReader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>): void => {
        if (e.target?.result) {
          setProcessedImage(null);
          setError(null);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleModelSelect = (modelPath: string): void => {
    setSelectedModel(modelPath);
    setUploadedFile(null); // Clear uploaded file when model is selected
    setPreviewUrl(modelPath);
    setProcessedImage(null);
    setError(null);
  };

  const reset = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault();
    setUploadedFile(null);
    setSelectedModel(null);
    setPreviewUrl(null);
    setProcessedImage(null);
    setError(null);
    setIsProcessing(false);
  };

  const handleDownload = (): void => {
    if (processedImage) {
      fetch(processedImage)
        .then((response) => response.blob())
        .then((blob) => {
          const link = document.createElement("a");
          const url = URL.createObjectURL(blob);
          link.href = url;
          link.download = `bg-removed-${Date.now()}.jpg`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.error("Error fetching image:", error);
          alert("Failed to download the image.");
        });
    } else {
      console.log("No generated image to download");
      alert("No generated image available to download.");
    }
  };

  const handleProcessImage = async (): Promise<void> => {
    console.log("Processing image...");

    // Handle both uploaded file and selected model
    let fileToProcess: File | null = null;

    if (uploadedFile) {
      fileToProcess = uploadedFile;
    } else if (selectedModel) {
      // Convert selected model image to File object
      try {
        const response = await fetch(selectedModel);
        const blob = await response.blob();
        fileToProcess = new File([blob], "selected-model.jpg", {
          type: "image/jpeg",
        });
      } catch (error) {
        console.error("Error converting model image to file:", error);
        setError("Failed to process selected model image");
        return;
      }
    }

    if (!fileToProcess) return;

    try {
      setError(null);
      setIsProcessing(true);
      const startTime = Date.now();
      const response = await BgRemovalService.directRemoveBackgroundHomePage(
        fileToProcess
      );
      console.log("API responseacnssancajcns:", response);
      
      const processingTime = Date.now() - startTime;
      console.log(`Background removal completed in ${processingTime}ms`);

      if (response.success && response.url) {
        setProcessedImage(response.url);
        // if (response.data.cached) {
        //   console.log("Used cached result for faster processing");
        // }
        setIsProcessing(false);
      } else {
        throw new Error(response.message || "Failed to remove background");
      }
    } catch (error: unknown) {
      console.error("Error processing image:", error);
      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as {
          response?: { data?: { error?: { code?: string; message?: string } } };
        };
        if (apiError.response?.data?.error?.code === "INSUFFICIENT_CREDITS") {
          setError(
            "You don't have enough credits for this operation. Please purchase more credits."
          );
        } else if (
          apiError.response?.data?.error?.code === "RATE_LIMIT_EXCEEDED"
        ) {
          setError("Rate limit exceeded. Please try again in a moment.");
        } else {
          setError(
            "Failed to process image: " +
              (apiError.response?.data?.error?.message || "Unknown error")
          );
        }
      } else if (error instanceof Error) {
        setError("Failed to process image: " + error.message);
      } else {
        setError("Failed to process image: Unknown error");
      }
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex flex-col lg:flex-row justify-between items-start mx-auto p-8 lg:p-12 font-sans bg-[#222222] gap-8 lg:gap-12 z-10 rounded-3xl min-h-[400px] border border-gray-600 w-full max-w-7xl">
      {/* Left Section - Content (50%) */}
      <div className="flex flex-col justify-center items-center lg:items-start gap-6 lg:gap-8 h-full lg:w-1/2 text-center lg:text-left">
        {/* AI Tool Badge */}
        {!uploadedFile && !selectedModel && (
          <div className="flex justify-center lg:justify-start">
            <Button className="flex items-center !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition w-fit">
              <AiToolIcon />
              <span>AI Tool</span>
            </Button>
          </div>
        )}

        {/* Heading */}
        <div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl text-white font-semibold mb-4 lg:mb-6">
            Background Remover
          </h2>

          {/* Description */}
          <p className="text-sm md:text-base lg:text-lg text-white/80 leading-relaxed max-w-xl mx-auto lg:mx-0">
            No matter if you want to make a background transparent (PNG), add a
            white background to a photo, extract or isolate the subject, or get
            the cutout of a photo - you can do all this and more with
            miragic.ai, the AI background remover for professionals.
          </p>
        </div>

        {/* Generate Button */}
        {(uploadedFile || selectedModel) && (
          <div className="mt-2">
            <Button
              disabled={isProcessing}
              onClick={handleProcessImage}
              className="flex items-center !px-8 py-3 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
            >
              {isProcessing ? (
                <Loader2 className="animate-spin" size={18} />
              ) : (
                <>
                  <span>Generate</span>
                  <ChevronRight />
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Right Section - Upload/Preview Area (50%) */}
      <div className="flex flex-col gap-6 lg:w-1/2 w-full">
        {/* Main Upload/Preview Box */}
        <div
          className={`border-2 border-dashed border-gray-500 rounded-lg bg-white/5 flex items-center justify-center w-full min-h-[300px] max-h-[400px] relative ${
            uploadedFile || selectedModel ? "" : "p-8"
          }`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <input
            type="file"
            id="file-upload"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />

          <label
            htmlFor="file-upload"
            className="cursor-pointer relative w-full h-full flex items-center justify-center"
          >
            {(uploadedFile || selectedModel) && previewUrl ? (
              <div className="relative w-full h-full">
                <button
                  onClick={reset}
                  className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full cursor-pointer hover:bg-red-600 transition z-10"
                >
                  <X className="w-4 h-4" />
                </button>
                <img
                  src={previewUrl}
                  alt="Selected preview"
                  className="w-full h-full max-h-[350px] object-contain rounded-lg"
                />
                {processedImage && (
                  <div className="absolute px-4 lg:px-5 min-h-16 lg:min-h-20 flex items-center justify-center gap-3 lg:gap-5 transform -translate-y-3 lg:-translate-y-4 -translate-x-1/2 bottom-0 left-1/2 rounded-[10px] border border-[rgba(255,255,255,0.2)] bg-[rgba(14,14,20,0.83)] backdrop-blur-[10.9px]">
                    <button
                      onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                        reset(e)
                      }
                      className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-3 lg:px-4 py-2 hover:bg-gray-900 cursor-pointer"
                    >
                      <RefreshCw className="text-gray-400 w-4 h-4" />
                    </button>
                    <Button
                      onClick={handleDownload}
                      className="min-w-[100px] lg:min-w-[120px] min-h-[38px] lg:min-h-[42px] px-3 lg:px-4 py-2 text-sm lg:text-base rounded-[10px] border border-white/15 bg-[#8C45FF]/40 shadow-[inset_0px_0px_6px_3px_rgba(255,255,255,0.25)] backdrop-blur-[7px]"
                      variant={"gradient"}
                    >
                      Download
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                <Button
                  onClick={() =>
                    document.getElementById("file-upload")?.click()
                  }
                  className="flex items-center !px-7 text-white text-base rounded-md cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
                >
                  <Plus />
                  <span>Choose an Image or Drag & Drop</span>
                </Button>
                <p className="text-white font-bold my-3">or</p>
                <p className="text-sm text-gray-500 max-w-[300px] text-center">
                  Original image must be .png, .jpg, .jpeg or webp format and
                  30mb max size.
                </p>
              </div>
            )}
          </label>

          {/* Error Display */}
          {error && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-11/12">
              <div className="p-3 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
                <AlertCircle className="text-red-500" size={18} />
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}
        </div>

        {/* Pre-made Models Section */}
        <div className="flex flex-col gap-4">
          <div className="text-center lg:text-left">
            <h3 className="text-lg text-white font-semibold mb-2">
              Or try with our models
            </h3>
            <p className="text-sm text-white/65">
              Click on any model to test the background removal
            </p>
          </div>

          {/* Model Images Grid */}
          <div className="grid grid-cols-5 gap-3 max-w-[450px] mx-auto lg:mx-0">
            {modelImages.slice(0, 5).map((modelPath, index) => (
              <div
                key={index}
                onClick={() => handleModelSelect(modelPath)}
                className={`relative aspect-square rounded-lg overflow-hidden border-2 cursor-pointer transition-all hover:scale-105 ${
                  selectedModel === modelPath
                    ? "border-purple-500 shadow-[0_0_10px_rgba(129,90,219,0.6)]"
                    : "border-gray-500 hover:border-purple-400"
                }`}
              >
                <img
                  src={modelPath}
                  alt={`Model ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src =
                      "https://via.placeholder.com/100x100/333333/ffffff?text=Model";
                  }}
                />
                {selectedModel === modelPath && (
                  <div className="absolute inset-0 bg-purple-500/20 flex items-center justify-center">
                    <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <AiCustomBgSelectionModal
        isOpen={isOpenBgSelectModal}
        onClose={() => setIsOpenBgSelectModal(false)}
        onSelect={() => {}}
      />
    </div>
  );
};

export default BackgroundRemover;

// import { useCallback, useState, useEffect } from "react";
// import type { ChangeEvent, DragEvent } from "react";
// import { Button } from "../ui/button";
// import { AiToolIcon } from "@/lib/icons";
// import {
//   AlertCircle,
//   ChevronRight,
//   Loader2,
//   Plus,
//   RefreshCw,
//   X,
// } from "lucide-react";

// import AiCustomBgSelectionModal from "../AllTools/AiCustomBgSelectionModal";

// import BgRemovalService from "@/services/bgRemoval.service";

// const BackgroundRemover = () => {
//   // const [isDragging, setIsDragging] = useState<boolean>(false);
//   // const [uploadedFile, setUploadedFile] = useState<File | null>();
//   const [previewUrl, setPreviewUrl] = useState<string | null>(null);
//   // const [fileType, setFileType] = useState<string | null>(null);

//   // const [isDragOver, setIsDragOver] = useState<boolean>(false);
//   // const [uploadedImage, setUploadedImage] = useState<string | null>(null);
//   const [uploadedFile, setUploadedFile] = useState<File | null>(null);
//   const [isOpenBgSelectModal, setIsOpenBgSelectModal] =
//     useState<boolean>(false);
//   const [isProcessing, setIsProcessing] = useState<boolean>(false);
//   const [processedImage, setProcessedImage] = useState<string | null>(null);
//   // const [, setCurrentJob] = useState<BgRemovalJob | null>(null);
//   const [error, setError] = useState<string | null>(null);

//   // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//   //   const file = e.target.files?.[0];

//   //   if (file) {
//   //     setUploadedFile(file);

//   //     if (file.type.startsWith("image/")) {
//   //       setFileType("image");

//   //     } else if (file.type.startsWith("video/")) {
//   //       setFileType("video");
//   //     } else {
//   //       // Unsupported file type
//   //       setFileType(null);
//   //       setPreviewUrl(null);
//   //       alert("Please select an image or video file.");
//   //     }
//   //   } else {
//   //     setUploadedFile(null);
//   //     setFileType(null);
//   //     setPreviewUrl(null);
//   //   }
//   // };
//   useEffect(() => {
//     if (uploadedFile) {
//       const url = URL.createObjectURL(uploadedFile);
//       setPreviewUrl(url);

//       return () => {
//         URL.revokeObjectURL(url);
//         setPreviewUrl(null);
//       };
//     }
//   }, [uploadedFile]);

//   const handleDragEnter = useCallback((e: DragEvent<HTMLDivElement>) => {
//     e.preventDefault();
//     e.stopPropagation();
//     // setIsDragging(true);
//   }, []);

//   // const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
//   //   if (e.target.files && e.target.files.length > 0) {
//   //     setUploadedFile(e.target.files[0]);
//   //   }
//   // };

//   // const [isHovering, setIsHovering] = useState<boolean>(false);

//   const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
//     e.preventDefault();
//     // setIsDragOver(true);
//   };

//   const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
//     e.preventDefault();
//     // setIsDragOver(false);
//   };

//   const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
//     e.preventDefault();
//     // setIsDragOver(false);
//     const files: FileList = e.dataTransfer.files;
//     if (files.length > 0) {
//       const file: File = files[0];
//       setUploadedFile(file);
//       const reader: FileReader = new FileReader();
//       reader.onload = (e: ProgressEvent<FileReader>): void => {
//         if (e.target?.result) {
//           // setUploadedImage(e.target.result as string);
//           setProcessedImage(null);
//           // setCurrentJob(null);
//           setError(null);
//         }
//       };
//       reader.readAsDataURL(file);
//     }
//   };

//   const handleFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
//     const files: FileList | null = e.target.files;
//     if (files && files.length > 0) {
//       const file: File = files[0];
//       setUploadedFile(file);
//       const reader: FileReader = new FileReader();
//       reader.onload = (e: ProgressEvent<FileReader>): void => {
//         if (e.target?.result) {
//           // setUploadedImage(e.target.result as string);
//           setProcessedImage(null);
//           // setCurrentJob(null);
//           setError(null);
//         }
//       };
//       reader.readAsDataURL(file);
//     }
//   };

//   const reset = (e: React.MouseEvent<HTMLButtonElement>): void => {
//     e.preventDefault();
//     // setUploadedImage(null);
//     setUploadedFile(null);
//     setProcessedImage(null);
//     // setCurrentJob(null);
//     setError(null);
//     setIsProcessing(false);
//   };

//   const handleDownload = (): void => {
//     if (processedImage) {
//       fetch(processedImage)
//         .then((response) => response.blob())
//         .then((blob) => {
//           const link = document.createElement("a");
//           const url = URL.createObjectURL(blob);
//           link.href = url;
//           link.download = `bg-removed-${Date.now()}.jpg`;
//           document.body.appendChild(link);
//           link.click();
//           document.body.removeChild(link);
//           URL.revokeObjectURL(url);
//         })
//         .catch((error) => {
//           console.error("Error fetching image:", error);
//           alert("Failed to download the image.");
//         });
//     } else {
//       console.log("No generated image to download");
//       alert("No generated image available to download.");
//     }
//   };

//   const handleProcessImage = async (): Promise<void> => {
//     console.log("Processing image...");
//     if (!uploadedFile) return;

//     try {
//       setError(null);
//       setIsProcessing(true);
//       const startTime = Date.now();
//       const response = await BgRemovalService.directRemoveBackgroundHomePage(
//         uploadedFile
//       );
//       const processingTime = Date.now() - startTime;
//       console.log(`Background removal completed in ${processingTime}ms`);

//       if (response.success && response.data.imageUrl) {
//         setProcessedImage(response.data.imageUrl);
//         if (response.data.jobId) {
//           // setCurrentJob({
//           //   id: response.data.jobId,
//           //   userId: "",
//           //   originalImageUrl: uploadedImage || "",
//           //   resultImageUrl: response.data.imageUrl,
//           //   status: "COMPLETED",
//           //   createdAt: new Date().toISOString(),
//           //   updatedAt: new Date().toISOString(),
//           // });
//         }
//         if (response.data.cached) {
//           console.log("Used cached result for faster processing");
//         }
//         setIsProcessing(false);
//       } else {
//         throw new Error(response.message || "Failed to remove background");
//       }
//     } catch (error: unknown) {
//       console.error("Error processing image:", error);
//       if (error && typeof error === "object" && "response" in error) {
//         const apiError = error as {
//           response?: { data?: { error?: { code?: string; message?: string } } };
//         };
//         if (apiError.response?.data?.error?.code === "INSUFFICIENT_CREDITS") {
//           // setInsufficientCredits(true);
//           setError(
//             "You don't have enough credits for this operation. Please purchase more credits."
//           );
//         } else if (
//           apiError.response?.data?.error?.code === "RATE_LIMIT_EXCEEDED"
//         ) {
//           setError("Rate limit exceeded. Please try again in a moment.");
//         } else {
//           setError(
//             "Failed to process image: " +
//               (apiError.response?.data?.error?.message || "Unknown error")
//           );
//         }
//       } else if (error instanceof Error) {
//         setError("Failed to process image: " + error.message);
//       } else {
//         setError("Failed to process image: Unknown error");
//       }
//       setIsProcessing(false);
//     }
//   };

//   return (
//     <div className="flex flex-col md:flex-row justify-between mx-auto p-12 font-sans text-center bg-[#222222] gap-10 z-10 rounded-3xl min-h-[400px] border border-gray-600 w-5xl">
//       <div className="flex min-h-[350px] flex-col justify-between gap-12 h-full">
//         <div className="flex flex-col items-center md:items-start">
//           {!uploadedFile && (
//             <Button className="flex items-center  mb-10 !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
//               <AiToolIcon />
//               <span>AI Tool</span>
//             </Button>
//           )}

//           <h2 className="text-4xl text-white font-semibold">
//             Background remover
//           </h2>
//           <p className="mt-3 text-xl text-white/65">
//             No matter if you want to make a background transparent (PNG), add a
//             white background to a photo, extract or isolate the subject, or get
//             the cutout of a photo - you can do all this and more with
//             miragic.ai, the AI background remover for professionals.
//           </p>
//         </div>

//         {/* {fileType === "video" && (
//           <>
//             <div>
//               <Text variant={"body"}>
//                 ✓ 5 Customized Instant Avatars <br /> ✓ Upload file size limited
//                 to 150M <br /> ✓ 60+ free Public Studio Avatars
//               </Text>
//             </div>
//             <Button className="flex max-w-fit items-center !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
//               <span>Try it now</span>
//               <ChevronRight />
//             </Button>
//           </>
//         )} */}

//         {uploadedFile && (
//           <div className="flex gap-4">
//             <Button
//               disabled={isProcessing}
//               onClick={handleProcessImage}
//               className="flex items-center  mb-10 !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
//             >
//               {isProcessing ? (
//                 <Loader2 className="animate-spin" size={18} />
//               ) : (
//                 <>
//                   <span>Generate</span>
//                   <ChevronRight />
//                 </>
//               )}
//             </Button>
//             {/* <button
//               onClick={() => reset()}
//               className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-4 py-2 hover:bg-gray-900 cursor-pointer"
//             >
//               <RefreshCw className="text-gray-400" />
//             </button>
//             <div className="relative z-10">
//               <button
//                 onClick={() => setIsOpenBgSelectModal(!isOpenBgSelectModal)}
//                 className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-4 py-2 hover:bg-gray-900 cursor-pointer"
//               >
//                 <span className="text-white">AI Background</span>
//                 <ChevronDown className="text-gray-400" />
//               </button>
//             </div> */}
//           </div>
//         )}
//       </div>
//       <div
//         className={`border-2 border-dashed border-gray-500 rounded-lg bg-white/5 flex items-center justify-center w-full max-w-[450px] min-h-[200px] max-h-[350px] relative ${
//           uploadedFile ? "" : "p-8"
//         }`}
//         onDragEnter={handleDragEnter}
//         onDragLeave={handleDragLeave}
//         onDragOver={handleDragOver}
//         onDrop={handleDrop}
//       >
//         <div className="absolute transform translate-y-4/5 -translate-x-1/2 bottom-0 left-1/2">
//           {error && (
//             <div className="mt-4 p-2 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
//               <AlertCircle className="text-red-500" size={18} />
//               <p className="text-red-400 text-sm">{error}</p>
//             </div>
//           )}
//         </div>
//         <input
//           type="file"
//           id="file-upload"
//           accept=""
//           onChange={handleFileSelect}
//           className="hidden"
//         />
//         <label
//           htmlFor="file-upload"
//           className="cursor-pointer relative w-full h-full flex items-center justify-center"
//         >
//           {uploadedFile && previewUrl ? (
//             // fileType === "image" ? (
//             <div className="relative w-full h-full ">
//               <button
//                 onClick={reset}
//                 className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full cursor-pointer hover:bg-red-600 transition"
//               >
//                 <X className="w-4 h-4" />
//               </button>
//               <img
//                 src={previewUrl}
//                 alt="Selected preview"
//                 className="w-full h-full object-contain rounded-lg"
//               />
//               {processedImage && (
//                 <div className="absolute px-5 min-h-20 flex items-center justify-center gap-5 transform -translate-y-4 -translate-x-1/2  bottom-0 left-1/2 rounded-[10px] border border-[rgba(255,255,255,0.2)] bg-[rgba(14,14,20,0.83)] backdrop-blur-[10.9px]">
//                   <button
//                     onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
//                       reset(e)
//                     }
//                     className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-4 py-2 hover:bg-gray-900 cursor-pointer"
//                   >
//                     <RefreshCw className="text-gray-400" />
//                   </button>
//                   <Button
//                     onClick={handleDownload}
//                     className="min-w-[120px] min-h-[42px] px-4 py-2 rounded-[10px] border border-white/15 bg-[#8C45FF]/40 shadow-[inset_0px_0px_6px_3px_rgba(255,255,255,0.25)] backdrop-blur-[7px]"
//                     variant={"gradient"}
//                   >
//                     Download
//                   </Button>
//                 </div>
//                 // <Button
//                 //   onClick={handleDownload}
//                 //   // disabled={isProcessing}
//                 //   variant={"gradient"}
//                 //   className="absolute transform -translate-y-4 -translate-x-1/2  bottom-0 left-1/2 min-w-[180px] rounded-[10px] border border-white/15 bg-[#8C45FF]/40 shadow-[inset_0px_0px_6px_3px_rgba(255,255,255,0.25)] backdrop-blur-[7px]"
//                 // >
//                 //   {isProcessing ? (
//                 //     <Loader2 className="animate-spin" size={18} />
//                 //   ) : null}
//                 //   {processedImage && "Download"}
//                 // </Button>
//               )}
//             </div>
//           ) : (
//             // ) : (
//             //   <div className="relative w-full h-full">
//             //     <video className="h-full w-auto" controls>
//             //       <source src={previewUrl} />
//             //       Your browser does not support the video tag.
//             //     </video>
//             //     <div className="absolute flex backdrop-blur-[7px] shadow-[inset_0px_0px_6px_3px_rgba(255,255,255,0.25)] items-center rounded-full justify-center h-[50px] w-[50px] text-white bg-[rgba(140,69,255,0.4)] backdrop-blur-[7px] transition right-1/2 top-1/2 transform translate-x-1/2 -translate-y-1/2">
//             //       <img src="/icons/solid_play.svg" alt="play" />
//             //     </div>
//             //   </div>
//             // )
//             <div className="flex flex-col items-center justify-center h-full">
//               <Button
//                 onClick={() => document.getElementById("file-upload")?.click()}
//                 className="flex items-center !px-7 text-white text-base rounded-md cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
//               >
//                 <Plus />
//                 <span>Choose a Video or Drag & Drop</span>
//               </Button>
//               <p className="text-white font-bold my-3">or</p>
//               <p className="text-sm text-gray-500 max-w-[300px]">
//                 Original image must be .png, .jpg, .jpeg or webp format and 30mb
//                 max size.
//               </p>
//             </div>
//           )}
//         </label>
//       </div>
//       {/* <div className="flex flex-col h-full relative max-w-[450px] min-h-[200px] max-h-[350px] relative "> */}

//       {/* {true == true && (
//           <div className="mt-4 p-3 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
//             <AlertCircle className="text-red-500" size={18} />
//             <p className="text-red-400 text-sm">{error}</p>
//           </div>
//         )} */}
//       {/* </div> */}

//       <AiCustomBgSelectionModal
//         isOpen={isOpenBgSelectModal}
//         onClose={() => setIsOpenBgSelectModal(false)}
//         onSelect={() => {}}
//       />
//     </div>
//   );
// };

// export default BackgroundRemover;
