import { useCallback, useState, useEffect } from "react";
import type { DragE<PERSON> } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { AiToolIcon } from "@/lib/icons";
import { PlusIcon, RefreshCw } from "lucide-react";
import { Text } from "../ui/text";
const instructionItems = [
  {
    step: 1,
    title: "Select Clothes",
    description:
      "Choose which clothes you’d like to try on, please follow the guidelines",
  },
  {
    step: 2,
    title: "Pick a model",
    description: "Choose a model of your own to try on",
  },
  {
    step: 3,
    title: "Try it on!",
    description: "Click “Generate” to see the outfit come to life on the model",
  },
];

const VirtualTryOn = () => {
  // const [isDragging, setIsDragging] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>();
  const [previewUrl, setPreviewUrl] = useState<string | boolean>("");
  const [fileType, setFileType] = useState<string | null>("");
  // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];

  //   if (file) {
  //     setSelectedFile(file);

  //     if (file.type.startsWith("image/")) {
  //       setFileType("image");
  //     } else if (file.type.startsWith("video/")) {
  //       setFileType("video");
  //     } else {
  //       // Unsupported file type
  //       setFileType(null);
  //       setPreviewUrl(false);
  //       alert("Please select an image or video file.");
  //     }
  //   } else {
  //     setSelectedFile(null);
  //     setFileType(null);
  //     setPreviewUrl(false);
  //   }
  // };
  useEffect(() => {
    if (selectedFile) {
      const url = URL.createObjectURL(selectedFile);
      setPreviewUrl(url);

      return () => {
        URL.revokeObjectURL(url);
        setPreviewUrl(false);
      };
    }
  }, [selectedFile]);

  const reset = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();

    setSelectedFile(null);
    setPreviewUrl(false);
    setFileType(null);
  };

  const handleDragEnter = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  }, []);

  // const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
  //   if (e.target.files && e.target.files.length > 0) {
  //     setSelectedFile(e.target.files[0]);
  //   }
  // };

  return (
    <div className="flex flex-col md:flex-row justify-between mx-auto p-12 font-sans text-center bg-[#222222] gap-10 z-10 rounded-3xl min-h-[400px] h-auto border border-gray-600 w-5xl">
      <div className="flex flex-col justify-between gap-12 h-full">
        <div className="flex flex-col items-center md:items-start">
          {fileType !== "video" && (
            <Button className="flex items-center  mb-10 !px-7 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
              <AiToolIcon />
              <span>AI Tool</span>
            </Button>
          )}

          <h2 className="text-4xl text-white font-semibold">Virtual Try On</h2>
          <p className="mt-3 text-xl text-left text-white/65">
            With our Virtual Try-On, users can experiment with outfits,
            visualize diverse fits.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          {instructionItems.map((item, index) => {
            return (
              <div key={index} className="flex items-start gap-4">
                <span className="flex min-h-6 mt-1 min-w-6 rounded-full bg-white/40 items-center justify-center">
                  {item.step}
                </span>
                <div className="flex flex-col">
                  <Text
                    className="text-[14px] text-white"
                    variant={"card_title_small"}
                  >
                    {item.title}
                  </Text>
                  <Text className="text-[12px]" variant={"card_body"}>
                    {item.description}
                  </Text>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      {previewUrl ? (
        <div className="relative w-full h-full ">
          <img
            src={"/png/genereted_ai_img.png"}
            alt="Selected preview"
            className="w-full h-full object-cover rounded-lg"
          />

          <div className="absolute px-5 min-h-20 flex items-center justify-center gap-5 transform -translate-y-4 -translate-x-1/2  bottom-0 left-1/2 rounded-[10px] border border-[rgba(255,255,255,0.2)] bg-[rgba(14,14,20,0.83)] backdrop-blur-[10.9px]">
            <button
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => reset(e)}
              className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-4 py-2 hover:bg-gray-900 cursor-pointer"
            >
              <RefreshCw className="text-gray-400" />
            </button>
            <Button
              className="min-w-[120px] min-h-[42px] px-4 py-2 rounded-[10px] border border-white/15 bg-[#8C45FF]/40 shadow-[inset_0px_0px_6px_3px_rgba(255,255,255,0.25)] backdrop-blur-[7px]"
              variant={"gradient"}
            >
              Download
            </Button>
          </div>
        </div>
      ) : (
        <div
          className={` rounded-lg  flex flex-col gap-5 w-full h-full min-h-[200px] relative `}
        >
          <Text variant={"card_title_large"}>Select clothes</Text>
          <div className="flex items-center gap-10 justify-between w-full">
            <Button className="flex min-w-[200px] rounded-[10px] items-center  !px-7 text-white text-base cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
              <span>Single clothes</span>
            </Button>
            <Button
              className="flex items-center justify-center min-w-[200px] rounded-[10px] px-7 text-base text-white cursor-pointer transition
      border border-[rgba(255,255,255,0.15)]
      bg-transparent backdrop-blur-[7px] 
      hover:bg-gradient-to-br hover:from-[#4e1e95] hover:to-[#3d0d6a]
      hover:shadow-[0_0_15px_rgba(129,90,219,0.9)]"
            >
              <span>Top & Bottom</span>
            </Button>
          </div>
          <div
            className={`border-2 flex-col border-gray-500 rounded-lg bg-white/5 flex items-center justify-center w-full min-h-[100px] max-h-[150px] relative ${
              selectedFile ? "" : "p-8"
            }`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <Text variant={"card_title_small"} className="text-white">
              + Add item
            </Text>
            <Text variant={"card_body"}>Or drag & drop here</Text>
          </div>
          <div className="flex flex-col gap-5">
            <div className="flex flex-col">
              <Text variant={"card_title_small"} className="text-white">
                Select a model
              </Text>
              <Text variant={"card_body"}>Select our model to try on</Text>
            </div>
            <div className="flex items-center gap-8 flex-wrap">
              <div
                className={`border-2 text-[32px]  flex-col border-gray-500 rounded-lg bg-white/5 flex items-center justify-center h-[75px] w-[85px] relative`}
              >
                <PlusIcon className="text-white" />
              </div>{" "}
              <div
                className={`border-2 text-[32px] overflow-hidden  flex-col border-gray-500 rounded-lg  flex items-center justify-center h-[75px] w-[85px] relative`}
              >
                <img
                  src="/png/model.png"
                  alt="model"
                  className="w-full h-full object-cover"
                />
              </div>{" "}
              <div
                className={`border-2 text-[32px] overflow-hidden  flex-col border-gray-500 rounded-lg  flex items-center justify-center h-[75px] w-[85px] relative`}
              >
                <img
                  src="/png/model.png"
                  alt="model"
                  className="w-full h-full object-cover"
                />
              </div>{" "}
              <div
                className={`border-2 text-[32px] overflow-hidden  flex-col border-gray-500 rounded-lg  flex items-center justify-center h-[75px] w-[85px] relative`}
              >
                <img
                  src="/png/model.png"
                  alt="model"
                  className="w-full h-full object-cover"
                />
              </div>{" "}
            </div>
            <Button
              onClick={() => setPreviewUrl(true)}
              className="flex min-h-[50px] min-w-[200px] rounded-[10px] items-center  !px-7 text-white text-base cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
            >
              <span>Generate</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VirtualTryOn;
