import { MyStarIcon } from "@/lib/icons";
import React from "react";

const Section4Cards = () => {
  // Sample data for the cards, grouped into 3 columns with 3-4 cards each
  const columnData = [
    // First column (3 cards)
    [
      {
        id: 1,
        name: "<PERSON><PERSON><PERSON>",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
      {
        id: 2,
        name: "<PERSON><PERSON><PERSON>",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
      {
        id: 3,
        name: "<PERSON><PERSON><PERSON>",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
    ],
    // Second column (3 cards)
    [
      {
        id: 5,
        name: "<PERSON><PERSON><PERSON>",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
      {
        id: 6,
        name: "<PERSON><PERSON><PERSON>",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
      {
        id: 7,
        name: "Hiddison",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
    ],
    // Third column (3 cards)
    [
      {
        id: 8,
        name: "Hiddison",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
      {
        id: 9,
        name: "Hiddison",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
      {
        id: 10,
        name: "Hiddison",
        review:
          "It is a long established fact that a reader will be distracted by the readable content.",
        rating: 5,
        date: "Sep 27, 2025",
        image: "/png/avatar1.png",
      },
    ],
  ];

  // Function to render stars based on rating
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <span
          key={i}
          className={i < rating ? "text-yellow-400" : "text-gray-400"}
        >
          <MyStarIcon />
        </span>
      );
    }
    return stars;
  };

  return (
    <div className="bg-primary px-10 py-12">
      <div className="flex justify-center">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-24 relative">
          {columnData.map((column, colIndex) => (
            <React.Fragment key={colIndex}>
              {/* Placeholder for second column to maintain grid spacing */}
              {colIndex === 1 && (
                <div className="flex flex-col gap-6 invisible">
                  {column.map((card) => (
                    <div
                      key={card.id}
                      className="bg-white/5 text-white rounded-lg p-10 shadow-lg flex flex-col overflow-hidden w-[281px]"
                    >
                      {/* Card content */}
                    </div>
                  ))}
                </div>
              )}
              <div
                className={`flex flex-col gap-6 items-center ${
                  colIndex === 1
                    ? "absolute md:top-[40px] w-full md:w-[calc(33.333%-1.5rem)] md:left-1/2 md:transform md:-translate-x-1/2"
                    : ""
                }`}
              >
                {column.map((card) => (
                  <div
                    key={card.id}
                    className="bg-white/5 text-white rounded-lg p-10 shadow-lg flex flex-col overflow-hidden w-[281px]"
                  >
                    <div className="flex items-start gap-3">
                      <img
                        src={card.image}
                        alt={`${card.name} profile`}
                        className="w-12 h-12 rounded-full mb-2"
                      />
                      <h3 className="text-lg font-semibold">{card.name}</h3>
                    </div>
                    <div className="flex mt-3">{renderStars(card.rating)}</div>
                    <p className="text-base text-gray-200 text-left mt-4">
                      {card.review}
                    </p>
                    <p className="text-xs text-gray-500 mt-7">{card.date}</p>
                  </div>
                ))}
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-primary px-10">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 relative">
        {columnData.map((column, colIndex) => (
          <React.Fragment key={colIndex}>
            {/* Placeholder for the second column to reserve grid space */}
            {colIndex === 1 && (
              <div className="flex flex-col gap-6 invisible">
                {column.map((card) => (
                  <div
                    key={card.id}
                    className="bg-white/5 text-white rounded-lg p-4 shadow-lg flex flex-col overflow-hidden w-[281px]"
                  >
                    <div className="flex items-start gap-3">
                      <img
                        src={card.image}
                        alt={`${card.name} profile`}
                        className="w-12 h-12 rounded-full mb-2"
                      />
                      <h3 className="text-lg font-semibold">{card.name}</h3>
                    </div>
                    <div className="flex mt-3">{renderStars(card.rating)}</div>
                    <p className="text-base text-gray-400 text-left mt-4">
                      {card.review}
                    </p>
                    <p className="text-xs text-gray-500 mt-2">{card.date}</p>
                  </div>
                ))}
              </div>
            )}
            {/* Actual column rendering */}
            <div
              className={`flex flex-col gap-6 ${
                colIndex === 1
                  ? "absolute md:top-[40px] w-full md:w-[calc(33.333%-1.5rem)] md:left-[calc(33.333%+0.75rem)]"
                  : ""
              }`}
            >
              {column.map((card) => (
                <div
                  key={card.id}
                  className="bg-white/5 text-white rounded-lg p-10 shadow-lg flex flex-col overflow-hidden w-[281px]"
                >
                  <div className="flex items-start gap-3">
                    <img
                      src={card.image}
                      alt={`${card.name} profile`}
                      className="w-12 h-12 rounded-full mb-2"
                    />
                    <h3 className="text-lg font-semibold">{card.name}</h3>
                  </div>
                  <div className="flex mt-3">{renderStars(card.rating)}</div>
                  <p className="text-base text-gray-200 text-left mt-4">
                    {card.review}
                  </p>
                  <p className="text-xs text-gray-500 mt-7">{card.date}</p>
                </div>
              ))}
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default Section4Cards;
