import { useRef, useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import { ChevronLeft, ChevronRight } from "lucide-react";

import "swiper/css";
import "swiper/css/navigation";
import { useNavigate } from "react-router-dom";

const SLIDER_DATA = [
  {
    title: "Background Remover",
    image: "/png/case_studies_removebg.png",
    description: "Remove image backgrounds instantly with AI",
    href: "/ai-tool/background-remover",
  },
  {
    title: "Virtual-Try-On",
    image: "/png/case_studies_vto.png",
    description: "Try products on virtually in real-time with AI",
    href: "/ai-tool/virtual-try-on",
  },
  {
    title: "Speed Painting",
    image: "/png/case_studies_sp.png",
    description: "Create stunning digital art quickly with AI",
    href: "/ai-tool/speedpainting",
  },
];

interface FavoriteToolSliderProps {
  title: string;
  width: number;
  titlePadding?: string;
}

const FavoriteToolSlider: React.FC<FavoriteToolSliderProps> = ({
  title = "Case Studies",
  width = 320, // Default width of each slide
  titlePadding = "",
}) => {
  const swiperRef = useRef<SwiperType | null>(null);
  const [slidesPerView, setSlidesPerView] = useState(1);
  const navigate = useNavigate();

  // Space between slides (in pixels)
  const spaceBetween = 20;

  // Function to calculate slidesPerView based on screen width
  const calculateSlidesPerView = () => {
    const screenWidth = window.innerWidth;
    const totalSlideWidth = width + spaceBetween; // Total width per slide (including space)

    // Calculate the number of slides that can fit
    let calculatedSlides = screenWidth / totalSlideWidth;

    // Round to nearest 0.5 (e.g., 2, 2.5, 3, 3.5, 4, 4.5)
    calculatedSlides = Math.round(calculatedSlides * 2) / 2;

    // Ensure at least 1 slide, and cap at a reasonable maximum (e.g., 4.5)
    return Math.max(1, Math.min(calculatedSlides, 4.5));
  };

  // Update slidesPerView on mount and window resize
  useEffect(() => {
    const updateSlides = () => {
      setSlidesPerView(calculateSlidesPerView());
    };

    // Initial calculation
    updateSlides();

    // Add resize event listener
    window.addEventListener("resize", updateSlides);

    // Cleanup
    return () => window.removeEventListener("resize", updateSlides);
  }, [width]); // Re-run when width prop changes

  // Dynamic breakpoints based on the provided width
  const breakpoints = {
    0: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
    768: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
    1024: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
  };

  return (
    <article className="relative w-full">
      {/* Custom navigation buttons */}
      <div className={`flex justify-between ${titlePadding}`}>
        <h2 className="text-white text-4xl font-inter">{title}</h2>
        <div className="flex space-x-4">
          <button
            onClick={() => swiperRef.current?.slidePrev()}
            className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
          >
            <ChevronLeft />
          </button>
          <button
            onClick={() => swiperRef.current?.slideNext()}
            className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
          >
            <ChevronRight />
          </button>
        </div>
      </div>

      <div className="pt-14">
        <Swiper
          modules={[Navigation]}
          onSwiper={(swiper: SwiperType) => {
            swiperRef.current = swiper;
          }}
          spaceBetween={spaceBetween}
          slidesPerView={slidesPerView} // Use dynamic slidesPerView
          centeredSlides={false}
          breakpoints={breakpoints}
          className="flex justify-center"
        >
          {SLIDER_DATA.map((post, index: number) => (
            <SwiperSlide key={index} className="h-auto">
              <div
                className="flex flex-1 justify-center cursor-pointer h-full"
                onClick={() => navigate(post.href)}
              >
                <div
                  className="flex flex-col bg-white/5 border border-white/25 text-white p-6 rounded-xl shadow-md w-full mr-5 h-80"
                  style={{ maxWidth: `${width}px` }}
                >
                  <img
                    src={post.image}
                    alt={post.title}
                    height={120}
                    className="w-full h-[120px] object-cover rounded-md flex-shrink-0"
                  />
                  <div className="flex flex-col flex-grow justify-between mt-4">
                    <div>
                      <p className="font-semibold font-inter text-white text-base">
                        {post.title}
                      </p>
                      <p className="text-gray-400 text-base mt-2">
                        {post.description.substring(0, 50) + "..."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </article>
  );
};

export default FavoriteToolSlider;
