import { useRef, useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import { ChevronLeft, ChevronRight } from "lucide-react";

import "swiper/css";
import "swiper/css/navigation";
import type { BlogPost } from "@/services/blog.service";
import { useNavigate } from "react-router-dom";

interface CaseStudiesAndRelatedArticleProps {
  title: string;
  width: number;
  titlePadding?: string;
  data: BlogPost[];
}

const CaseStudiesAndRelatedArticle: React.FC<
  CaseStudiesAndRelatedArticleProps
> = ({
  title = "Case Studies",
  width = 320, // Default width of each slide
  titlePadding = "",
  data,
}) => {
  const swiperRef = useRef<SwiperType | null>(null);
  const [slidesPerView, setSlidesPerView] = useState(1);
  const navigate = useNavigate();

  // Space between slides (in pixels)
  const spaceBetween = 20;

  // Default image fallback
  const DEFAULT_IMAGE =
    "https://images.unsplash.com/photo-1626544827763-d516dce335e2?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3";

  // Truncate text to specified length with ellipsis
  // const truncateText = (text: string, maxLength: number = 120) => {
  //   if (text.length <= maxLength) return text;
  //   return text.slice(0, maxLength).trim() + "...";
  // };

  // // Get author name safely
  // const getAuthorName = (blog: BlogPost) => {
  //   if (!blog.author?.profile) return "Unknown Author";
  //   const firstName = blog.author.profile.firstName || "";
  //   const lastName = blog.author.profile.lastName || "";
  //   return `${firstName} ${lastName}`.trim() || "Unknown Author";
  // };

  // Format the published date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  // Function to calculate slidesPerView based on screen width
  const calculateSlidesPerView = () => {
    const screenWidth = window.innerWidth;
    const totalSlideWidth = width + spaceBetween; // Total width per slide (including space)

    // Calculate the number of slides that can fit
    let calculatedSlides = screenWidth / totalSlideWidth;

    // Round to nearest 0.5 (e.g., 2, 2.5, 3, 3.5, 4, 4.5)
    calculatedSlides = Math.round(calculatedSlides * 2) / 2;

    // Ensure at least 1 slide, and cap at a reasonable maximum (e.g., 4.5)
    return Math.max(1, Math.min(calculatedSlides, 4.5));
  };

  // Update slidesPerView on mount and window resize
  useEffect(() => {
    const updateSlides = () => {
      setSlidesPerView(calculateSlidesPerView());
    };

    // Initial calculation
    updateSlides();

    // Add resize event listener
    window.addEventListener("resize", updateSlides);

    // Cleanup
    return () => window.removeEventListener("resize", updateSlides);
  }, [width]); // Re-run when width prop changes

  // Dynamic breakpoints based on the provided width
  const breakpoints = {
    0: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
    768: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
    1024: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
  };

  if (!data || !data?.length) return null;

  return (
    <article className="relative w-full">
      {/* Custom navigation buttons */}
      <div className={`flex justify-between ${titlePadding}`}>
        <h2 className="text-white text-4xl font-inter">{title}</h2>
        <div className="flex space-x-4">
          <button
            onClick={() => swiperRef.current?.slidePrev()}
            className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
          >
            <ChevronLeft />
          </button>
          <button
            onClick={() => swiperRef.current?.slideNext()}
            className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
          >
            <ChevronRight />
          </button>
        </div>
      </div>

      <div className="pt-14">
        <Swiper
          modules={[Navigation]}
          onSwiper={(swiper: SwiperType) => {
            swiperRef.current = swiper;
          }}
          spaceBetween={spaceBetween}
          slidesPerView={slidesPerView} // Use dynamic slidesPerView
          centeredSlides={false}
          breakpoints={breakpoints}
          className="flex justify-center"
        >
          {data.map((post: BlogPost, index: number) => (
            <SwiperSlide key={post.id || index}>
              <div className="flex justify-center">
                <div
                  onClick={() => navigate(`/blog/${post.slug}`)}
                  className="flex flex-col h-[420px] bg-white/5 border border-white/25 hover:border-white/35 text-white rounded-xl shadow-md w-full mr-5 cursor-pointer transition-all duration-300 overflow-hidden"
                  style={{ maxWidth: `${width}px` }}
                >
                  {/* Image Section - Fixed Height */}
                  <div className="h-[160px] w-full overflow-hidden">
                    <img
                      src={post.featuredImageUrl || DEFAULT_IMAGE}
                      alt={post.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = DEFAULT_IMAGE;
                      }}
                    />
                  </div>

                  {/* Content Section */}
                  <div className="flex flex-col flex-1 justify-between p-6">
                    {/* Top Content */}
                    <div className="flex flex-col gap-3">
                      {/* Date */}
                      <p className="text-gray-400 text-sm">
                        {post.publishedAt
                          ? formatDate(post.publishedAt)
                          : "Draft"}
                      </p>

                      {/* Title - Fixed Height */}
                      <div className="h-[48px] flex items-start">
                        <p
                          className="font-semibold font-inter text-white text-base leading-tight"
                          style={{
                            display: "-webkit-box",
                            WebkitBoxOrient: "vertical",
                            WebkitLineClamp: 2,
                            overflow: "hidden",
                          }}
                        >
                          {post.title}
                        </p>
                      </div>

                      {/* Excerpt - Fixed Height */}
                      <div className="h-[60px] flex items-start">
                        <p
                          className="text-gray-400 text-sm leading-relaxed"
                          style={{
                            display: "-webkit-box",
                            WebkitBoxOrient: "vertical",
                            WebkitLineClamp: 3,
                            overflow: "hidden",
                          }}
                        >
                          {post.excerpt ||
                            (post.content
                              ? post.content.substring(0, 150) + "..."
                              : "No description available")}
                        </p>
                      </div>
                    </div>

                    {/* Bottom Content - Author */}
                    <div className="mt-auto pt-3 border-t border-gray-700">
                      <p className="text-gray-400 text-sm">
                        By Admin
                        {/* Admin:{" "}
                        <span className="font-semibold text-white">
                          {getAuthorName(post)}
                        </span> */}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </article>
  );
};

export default CaseStudiesAndRelatedArticle;

// import { useRef, useEffect, useState } from "react";
// import { Swiper, SwiperSlide } from "swiper/react";
// import { Navigation } from "swiper/modules";
// import { Swiper as SwiperType } from "swiper";
// import { ChevronLeft, ChevronRight } from "lucide-react";

// import "swiper/css";
// import "swiper/css/navigation";
// import type { BlogPost } from "@/services/blog.service";
// import { useNavigate } from "react-router-dom";

// // const SLIDER_DATA = [
// //   {
// //     title: "Background Remover",
// //     image: "/png/case_studies_removebg.png",
// //     description: "Remove image backgrounds instantly with AI",
// //   },
// //   {
// //     title: "Virtual-Try-On",
// //     image: "/png/case_studies_vto.png",
// //     description: "Try products on virtually in real-time with AI",
// //   },
// //   {
// //     title: "Speed Painting",
// //     image: "/png/case_studies_sp.png",
// //     description: "Create stunning digital art quickly with AI",
// //   }
// // ];

// interface CaseStudiesAndRelatedArticleProps {
//   title: string;
//   width: number;
//   titlePadding?: string;
//   data: BlogPost[];
// }

// const CaseStudiesAndRelatedArticle: React.FC<
//   CaseStudiesAndRelatedArticleProps
// > = ({
//   title = "Case Studies",
//   width = 320, // Default width of each slide
//   titlePadding = "",
//   data,
// }) => {
//   const swiperRef = useRef<SwiperType | null>(null);
//   const [slidesPerView, setSlidesPerView] = useState(1);
//   const navigate = useNavigate();

//   // Space between slides (in pixels)
//   const spaceBetween = 20;

//   // Function to calculate slidesPerView based on screen width
//   const calculateSlidesPerView = () => {
//     const screenWidth = window.innerWidth;
//     const totalSlideWidth = width + spaceBetween; // Total width per slide (including space)

//     // Calculate the number of slides that can fit
//     let calculatedSlides = screenWidth / totalSlideWidth;

//     // Round to nearest 0.5 (e.g., 2, 2.5, 3, 3.5, 4, 4.5)
//     calculatedSlides = Math.round(calculatedSlides * 2) / 2;

//     // Ensure at least 1 slide, and cap at a reasonable maximum (e.g., 4.5)
//     return Math.max(1, Math.min(calculatedSlides, 4.5));
//   };

//   // Update slidesPerView on mount and window resize
//   useEffect(() => {
//     const updateSlides = () => {
//       setSlidesPerView(calculateSlidesPerView());
//     };

//     // Initial calculation
//     updateSlides();

//     // Add resize event listener
//     window.addEventListener("resize", updateSlides);

//     // Cleanup
//     return () => window.removeEventListener("resize", updateSlides);
//   }, [width]); // Re-run when width prop changes

//   // Dynamic breakpoints based on the provided width
//   const breakpoints = {
//     0: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
//     768: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
//     1024: { slidesPerView: Math.max(calculateSlidesPerView(), 1) },
//   };

//   if(!data || !data?.length) return null;

//   return (
//     <article className="relative w-full">
//       {/* Custom navigation buttons */}
//       <div className={`flex justify-between ${titlePadding}`}>
//         <h2 className="text-white text-4xl font-inter">{title}</h2>
//         <div className="flex space-x-4">
//           <button
//             onClick={() => swiperRef.current?.slidePrev()}
//             className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
//           >
//             <ChevronLeft />
//           </button>
//           <button
//             onClick={() => swiperRef.current?.slideNext()}
//             className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
//           >
//             <ChevronRight />
//           </button>
//         </div>
//       </div>

//       <div className="pt-14">
//         <Swiper
//           modules={[Navigation]}
//           onSwiper={(swiper: SwiperType) => {
//             swiperRef.current = swiper;
//           }}
//           spaceBetween={spaceBetween}
//           slidesPerView={slidesPerView} // Use dynamic slidesPerView
//           centeredSlides={false}
//           breakpoints={breakpoints}
//           className="flex justify-center"
//         >
//           {data.map((post: BlogPost, index: number) => (
//             <SwiperSlide key={post.id || index}>
//               <div className="flex justify-center" onClick={() => navigate(`/blog/${post.slug}`)}>
//                 <div
//                   className="flex flex-col gap-4 bg-white/5 border border-white/25 text-white p-6 rounded-xl shadow-md w-full mr-5"
//                   style={{ maxWidth: `${width}px` }}
//                 >
//                   <img
//                     src={post.featuredImageUrl}
//                     alt={post.title}
//                     height={120}
//                     className="w-full max-h-[120px] object-cover rounded-md"
//                   />
//                   <p className="font-semibold font-inter text-white text-base mt-4">
//                     {post.title}
//                   </p>
//                   <p className="text-gray-400 text-base mt-2">
//                     {post.excerpt || post.content.substring(0, 150) + "..."}
//                   </p>
//                 </div>
//               </div>
//             </SwiperSlide>
//           ))}
//         </Swiper>
//       </div>
//     </article>
//   );
// };

// export default CaseStudiesAndRelatedArticle;
