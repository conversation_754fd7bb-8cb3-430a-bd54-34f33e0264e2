import { useEffect, useState } from "react";
import {
  getAllBlogCategoriesAdmin,
  createBlogCategoryAdmin,
  updateBlogCategoryAdmin, // TODO: Uncomment when service function is ready
  type CreateBlogCategoryPayload,
  deleteBlogCategoryAdmin,
  // type UpdateBlogCategoryPayload, // TODO: Uncomment when type is ready
} from "../../../services/adminBlog.service";
import type { BlogCategory } from "../../../services/adminBlog.service";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";

// BlogCategoriesManager Component
const BlogCategoriesManager = ({
  isCreateModalOpen,
  setIsCreateModalOpen,
}: {
  isCreateModalOpen: boolean;
  setIsCreateModalOpen: (open: boolean) => void;
}) => {
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Create Modal State

  const [newCategoryName, setNewCategoryName] = useState("");
  const [newCategorySlug, setNewCategorySlug] = useState("");
  const [newCategoryDescription, setNewCategoryDescription] = useState("");

  // Edit Modal State
  const [editingCategory, setEditingCategory] = useState<BlogCategory | null>(
    null
  );
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editCategoryName, setEditCategoryName] = useState("");
  const [editCategorySlug, setEditCategorySlug] = useState("");
  const [editCategoryDescription, setEditCategoryDescription] = useState("");

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Shared function to fetch categories
  const fetchCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getAllBlogCategoriesAdmin(1, 100); // Fetching up to 100 categories for now
      setCategories(response.data);
    } catch (err) {
      setError("Failed to fetch categories. Please try again.");
      console.error("Fetch Categories Error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleOpenEditModal = (category: BlogCategory) => {
    setEditingCategory(category);
    setEditCategoryName(category.name);
    setEditCategorySlug(category.slug);
    setEditCategoryDescription(category.description || "");
    setIsEditModalOpen(true);
  };

  const resetCreateForm = () => {
    setNewCategoryName("");
    setNewCategorySlug("");
    setNewCategoryDescription("");
    setIsCreateModalOpen(false);
  };

  const resetEditForm = () => {
    setEditingCategory(null);
    setEditCategoryName("");
    setEditCategorySlug("");
    setEditCategoryDescription("");
    setIsEditModalOpen(false);
  };

  const handleCreateCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCategoryName.trim() || !newCategorySlug.trim()) {
      toast("Category Name and Slug are required."); // TODO: Replace with toast
      return;
    }
    setIsSubmitting(true);
    try {
      const payload: CreateBlogCategoryPayload = {
        name: newCategoryName,
        slug: newCategorySlug,
        description: newCategoryDescription || undefined,
      };
      await createBlogCategoryAdmin(payload);
      toast("Category created successfully!"); // TODO: Replace with toast
      fetchCategories(); // Refetch categories
      resetCreateForm();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (apiError: any) {
      console.error("Failed to create category:", apiError);
      toast(
        `Error creating category: ${
          apiError?.response?.data?.message ||
          apiError.message ||
          "Unknown error"
        }` // TODO: Replace with toast
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (
      !editingCategory ||
      !editCategoryName.trim() ||
      !editCategorySlug.trim()
    ) {
      toast(
        "Category Name and Slug are required, and a category must be selected for editing."
      ); // TODO: Replace with toast
      return;
    }
    setIsSubmitting(true);
    console.log("Submitting update for category:", editingCategory.id, {
      name: editCategoryName,
      slug: editCategorySlug,
      description: editCategoryDescription,
    });

    const payload: CreateBlogCategoryPayload = {
      name: editCategoryName,
      slug: editCategorySlug,
      description: editCategoryDescription || undefined,
    };

    try {
      await updateBlogCategoryAdmin(editingCategory.id, payload);
      toast("Category updated successfully!"); // TODO: Replace with toast
      fetchCategories();
      resetEditForm();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (apiError: any) {
      console.error("Failed to update category:", apiError);
      toast(
        `Error updating category: ${
          apiError?.response?.data?.message ||
          apiError.message ||
          "Unknown error"
        }` // TODO: Replace with toast
      );
    } finally {
      setIsSubmitting(false);
    }

    resetEditForm();
    setIsSubmitting(false);
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await deleteBlogCategoryAdmin(categoryId);
      toast("Category deleted successfully!");
      fetchCategories();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (apiError: any) {
      console.error("Failed to delete category:", apiError);
      toast(
        `Error deleting category: ${
          apiError?.response?.data?.message ||
          apiError.message ||
          "Unknown error"
        }`
      );
    }
  };

  if (loading) {
    return <div className="text-center py-10">Loading categories...</div>;
  }

  if (error) {
    return <div className="text-center py-10 text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-white">Blog Categories</h1>
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            {/* <Button className="bg-purple-600 hover:bg-purple-700 text-white">
              Create New Category
            </Button> */}
          </DialogTrigger>
          <DialogContent className="sm:max-w-[480px] bg-gray-800 border-gray-700 text-white">
            <DialogHeader>
              <DialogTitle>Create New Category</DialogTitle>
              <DialogDescription>
                Fill in the details for the new blog category. Click save when
                you're done.
              </DialogDescription>
            </DialogHeader>
            <form
              onSubmit={handleCreateCategorySubmit}
              className="space-y-4 py-2 pb-4"
            >
              <div>
                <Label htmlFor="create-name" className="text-sm font-medium">
                  Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="create-name"
                  type="text"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  placeholder="e.g., Technology"
                  className="mt-1 block w-full bg-gray-700 border-gray-600 rounded-md shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm text-white placeholder-gray-400"
                  required
                />
              </div>
              <div>
                <Label htmlFor="create-slug" className="text-sm font-medium">
                  Slug <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="create-slug"
                  type="text"
                  value={newCategorySlug}
                  onChange={(e) => setNewCategorySlug(e.target.value)}
                  placeholder="e.g., technology"
                  className="mt-1 block w-full bg-gray-700 border-gray-600 rounded-md shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm text-white placeholder-gray-400"
                  required
                />
              </div>
              <div>
                <Label
                  htmlFor="create-description"
                  className="text-sm font-medium"
                >
                  Description
                </Label>
                <Input
                  id="create-description"
                  type="text"
                  value={newCategoryDescription}
                  onChange={(e) => setNewCategoryDescription(e.target.value)}
                  placeholder="Optional: A brief description of the category"
                  className="mt-1 block w-full bg-gray-700 border-gray-600 rounded-md shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm text-white placeholder-gray-400"
                />
              </div>
              <DialogFooter className="border-t border-gray-700 pt-4 sm:justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetCreateForm}
                  className="hover:text-white border-gray-600 hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-700"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Creating..." : "Create Category"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Categories Table */}
      {categories.length === 0 && !loading && !error && (
        <div className="text-center py-10 text-gray-400">
          No categories found. Create one to get started!
        </div>
      )}
      {categories.length > 0 && (
        <div className="overflow-x-auto rounded-lg border border-gray-700 bg-gray-800 shadow-md">
          <Table>
            <TableHeader>
              <TableRow className="border-b-gray-700 hover:bg-gray-750">
                <TableHead className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider whitespace-nowrap">
                  Name
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider whitespace-nowrap">
                  Slug
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Description
                </TableHead>
                <TableHead className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider whitespace-nowrap">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="divide-y divide-gray-700">
              {categories.map((category) => (
                <TableRow
                  key={category.id}
                  className="hover:bg-gray-750 transition-colors duration-150"
                >
                  <TableCell className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                    {category.name}
                  </TableCell>
                  <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    {category.slug}
                  </TableCell>
                  <TableCell className="px-6 py-4 text-sm text-gray-300 max-w-xs truncate">
                    {category.description || "N/A"}
                  </TableCell>
                  <TableCell className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenEditModal(category)}
                      className="mr-2 border-blue-500 text-blue-500 hover:bg-blue-600 hover:text-white focus:ring-blue-500 transition-colors duration-150"
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteCategory(category.id)}
                      className="mr-2 border-red-500 text-red-500 hover:bg-red-600 hover:text-white focus:ring-red-500 transition-colors duration-150"
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Edit Category Dialog */}
      {editingCategory && (
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="sm:max-w-[480px] bg-gray-800 border-gray-700 text-white">
            <DialogHeader>
              <DialogTitle>Edit Category: {editingCategory.name}</DialogTitle>
              <DialogDescription>
                Update the details for this category.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleUpdateCategorySubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-name"
                    type="text"
                    value={editCategoryName}
                    onChange={(e) => setEditCategoryName(e.target.value)}
                    className="col-span-3 bg-gray-700 border-gray-600 focus:border-purple-500"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-slug" className="text-right">
                    Slug
                  </Label>
                  <Input
                    id="edit-slug"
                    type="text"
                    value={editCategorySlug}
                    onChange={(e) => setEditCategorySlug(e.target.value)}
                    className="col-span-3 bg-gray-700 border-gray-600 focus:border-purple-500"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-description" className="text-right">
                    Description
                  </Label>
                  <Input
                    id="edit-description"
                    type="text"
                    value={editCategoryDescription}
                    onChange={(e) => setEditCategoryDescription(e.target.value)}
                    className="col-span-3 bg-gray-700 border-gray-600 focus:border-purple-500"
                  />
                </div>
              </div>
              <DialogFooter className="border-t border-gray-700 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsEditModalOpen(false);
                    setEditingCategory(null); // Clear editing category on cancel
                  }}
                  className="hover:text-white border-gray-600 hover:bg-gray-700"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-700"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default BlogCategoriesManager;
