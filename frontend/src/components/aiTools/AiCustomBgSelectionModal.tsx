import React, { useState } from "react";
import { X } from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";

// Define the props interface
interface AiCustomBgSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect?: (avatarId: number) => void;
}

const AiCustomBgSelectionModal: React.FC<AiCustomBgSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
}) => {
  const [selectedAvatar, setSelectedAvatar] = useState<number | null>(null);

  if (!isOpen) return null;

  // Generate avatar data for 12 avatars (3 rows of 4)
  const avatars = Array.from({ length: 12 }, (_, index) => ({
    id: index + 1,
    src: `/png/select_ai_background.jpg`,
    alt: `Avatar ${index + 1}`,
  }));

  const handleAvatarClick = (avatarId: number): void => {
    setSelectedAvatar(avatarId);
  };

  const handleCancel = (): void => {
    setSelectedAvatar(null);
    onClose();
  };

  const handleSelect = (): void => {
    if (selectedAvatar && onSelect) {
      onSelect(selectedAvatar);
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-[#2A2D35] rounded-xl max-w-lg w-full mx-auto relative">
        {/* Close button */}
        <button
          onClick={handleCancel}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10"
        >
          <X size={20} />
        </button>

        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-white mb-2">
              Select your AI custom background
            </h2>
          </div>

          {/* Avatar Grid */}
          <div className="grid grid-cols-4 gap-3 mb-8">
            {avatars.map((avatar) => (
              <button
                key={avatar.id}
                onClick={() => handleAvatarClick(avatar.id)}
                className={`relative aspect-square rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                  selectedAvatar === avatar.id
                    ? "border-purple-500 ring-2 ring-purple-500 ring-opacity-50"
                    : "border-gray-600 hover:border-gray-500"
                }`}
              >
                <img
                  src={avatar.src}
                  alt={avatar.alt}
                  className="w-full h-full object-cover object-top"
                  onError={(e) => {
                    // Fallback for missing images
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    target.parentElement!.innerHTML = `
                      <div class="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-lg">
                        ${avatar.id}
                      </div>
                    `;
                  }}
                />
                {selectedAvatar === avatar.id && (
                  <div className="absolute inset-0 bg-secondary/15 flex items-center justify-center">
                    <div className="w-6 h-6 rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                )}
              </button>
            ))}
          </div>

          {/* Action buttons */}
          <div className="flex justify-center gap-3">
            <button
              onClick={handleCancel}
              className="cursor-pointer !px-6 bg-gray-600 hover:bg-gray-700 text-white rounded-full font-medium transition-colors"
            >
              Cancel
            </button>
            <ShadowButton
              onClick={handleSelect}
              disabled={!selectedAvatar}
              className="!px-6 !rounded-full"
            >
              Select
            </ShadowButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AiCustomBgSelectionModal;
