import { Text } from "../ui/text";
import { Link } from "react-router-dom";

export default function BottomFooter() {
  const currentYear = new Date().getFullYear();
  return (
    <>
      <Text variant={"small_title"} className="text-white">
        © {currentYear}
        <Link
          to={"/"}
          className="bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent"
        >
          {" "}
          MiragicAI
        </Link>
        . All Rights Reserved.
      </Text>
      <div className="flex justify-center items-center gap-2">
        <Text
          variant={"small_title"}
          className="bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent"
        >
          <Link to={"/coming-soon"}>Terms of Service</Link>
        </Text>
        <span>|</span>
        <Text
          variant={"small_title"}
          className="bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] bg-clip-text text-transparent"
        >
          <Link to={"/coming-soon"}>Privacy & Policy</Link>
        </Text>
      </div>
    </>
  );
}
