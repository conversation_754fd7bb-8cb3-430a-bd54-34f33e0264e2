import React from "react";

const BackgroundElements: React.FC = () => {
  return (
    <>
      {/* Shadow Image */}
      <img
        src="/png/image_gene_shadow_1.png"
        className="absolute bottom-[-24px] right-[-24px]"
        alt=""
        aria-hidden="true"
      />

      {/* Background Grid Pattern with Fade Effect */}
      <div className="absolute inset-0" aria-hidden="true">
        <div
          className="grid grid-cols-12 h-full opacity-10"
          style={{
            maskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
            WebkitMaskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
          }}
        >
          {Array.from({ length: 144 }).map((_, i: number) => (
            <div key={i} className="border border-white/20"></div>
          ))}
        </div>
      </div>

      {/* Floating Dots */}
      <div
        className="absolute top-20 right-20 w-2 h-2 bg-white/30 rounded-full animate-pulse"
        aria-hidden="true"
      ></div>
      <div
        className="absolute top-40 right-40 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-1000"
        aria-hidden="true"
      ></div>
      <div
        className="absolute bottom-32 left-32 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-500"
        aria-hidden="true"
      ></div>
      <div
        className="absolute bottom-20 right-60 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-700"
        aria-hidden="true"
      ></div>
    </>
  );
};

export default BackgroundElements;
