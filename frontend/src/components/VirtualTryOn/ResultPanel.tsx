import React from "react";
import { RefreshCw, Download } from "lucide-react";

interface ResultPanelProps {
  generatedImage: string | null;
  isProcessing: boolean;
  processingProgress?: string;
  currentJobId?: string | null;
  onReset: () => void;
  onDownload: () => void;
}

const ResultPanel: React.FC<ResultPanelProps> = ({
  generatedImage,
  isProcessing,
  processingProgress = "",
  currentJobId,
  onReset,
  onDownload,
}) => {
  console.log("ResultPanel state:", {
    generatedImage: !!generatedImage,
    isProcessing,
    processingProgress,
    currentJobId,
  });

  // Show instructions when no image and not processing
  if (!generatedImage && !isProcessing) {
    return (
      <div className="opacity-75 rounded-2xl p-8">
        <div className="space-y-6">
          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
              1
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Select Clothes</h3>
              <p className="text-gray-400">
                Choose which clothes you'd like to try on, please follow the
                guidelines
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
              2
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Pick a Model</h3>
              <p className="text-gray-400">
                Choose a model of your own to try on
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
              3
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Try it On!</h3>
              <p className="text-gray-400">
                Click "Generate" to see the outfit come to life on the model
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800/20 backdrop-blur-sm border border-gray-600 rounded-2xl overflow-hidden">
      <div className="relative">
        {/* Show loading state while processing */}
        {isProcessing ? (
          <div className="w-full h-96 flex items-center justify-center bg-gray-900/50">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500 mx-auto mb-4"></div>
              <p className="text-white text-lg font-medium">
                {processingProgress || "Generating Virtual Try-On..."}
              </p>
              <p className="text-gray-400 text-sm mt-2">
                This may take a few moments
              </p>
              {currentJobId && (
                <p className="text-gray-500 text-xs mt-2 font-mono">
                  Job ID: {currentJobId.slice(0, 8)}...
                </p>
              )}

              {/* Progress indicator based on processing progress */}
              <div className="mt-4 w-64 mx-auto">
                <div className="flex justify-between text-xs text-gray-400 mb-2">
                  <span
                    className={
                      processingProgress.includes("queue")
                        ? "text-purple-400"
                        : ""
                    }
                  >
                    Queue
                  </span>
                  <span
                    className={
                      processingProgress.includes("generating")
                        ? "text-purple-400"
                        : ""
                    }
                  >
                    Processing
                  </span>
                  <span
                    className={
                      processingProgress.includes("Finalizing")
                        ? "text-purple-400"
                        : ""
                    }
                  >
                    Finalizing
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full transition-all duration-500"
                    style={{
                      width: processingProgress.includes("queue")
                        ? "20%"
                        : processingProgress.includes("generating")
                        ? "60%"
                        : processingProgress.includes("Finalizing")
                        ? "90%"
                        : processingProgress.includes("completed")
                        ? "100%"
                        : "10%",
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Show generated image when available and not processing */
          generatedImage && (
            <img
              src={generatedImage}
              alt="Virtual try on result"
              className="w-full h-auto object-contain"
              style={{
                background:
                  "url(\"data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' width='20' height='20' patternUnits='userSpaceOnUse'%3e%3crect width='10' height='10' fill='%23f3f4f6'/%3e%3crect x='10' y='10' width='10' height='10' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e\")",
              }}
              onLoad={() =>
                console.log("✅ Generated image loaded successfully")
              }
              onError={(e) => {
                console.error("❌ Failed to load generated image:", e);
                // Could add error handling here, like showing a placeholder or retry button
              }}
            />
          )
        )}

        {/* Control Buttons Overlay - only show when we have a result and not processing */}
        {generatedImage && !isProcessing && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center gap-3">
            <button
              onClick={onReset}
              className="flex items-center justify-center w-10 h-10 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              title="Reset and try again"
            >
              <RefreshCw size={18} />
            </button>
            <button
              onClick={onDownload}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              <Download size={18} />
              Download
            </button>
          </div>
        )}

        {/* Success overlay when processing just completed */}
        {processingProgress.includes("completed") && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-green-600/90 backdrop-blur-sm rounded-lg px-4 py-2">
            <p className="text-white text-sm font-medium">
              ✅ Virtual try on completed successfully!
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultPanel;
