import React from "react";
import { RefreshCw, Download } from "lucide-react";

interface ResultPanelProps {
  generatedImage: string | null; // Add this prop
  isProcessing: boolean; // Add this prop
  onReset: () => void;
  onDownload: () => void;
}

const ResultPanel: React.FC<ResultPanelProps> = ({
  generatedImage,
  isProcessing,
  onReset,
  onDownload,
}) => {
  if (!generatedImage && !isProcessing) {
    return (
      <div className="opacity-75 rounded-2xl p-8">
        <div className="space-y-6">
          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
              1
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Select Clothes</h3>
              <p className="text-gray-400">
                Choose which clothes you'd like to try on, please follow the
                guidelines
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
              2
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Pick a Model</h3>
              <p className="text-gray-400">
                Choose a model of your own to try on
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
              3
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Try it On!</h3>
              <p className="text-gray-400">
                Click "Generate" to see the outfit come to life on the model
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getDisplayImage = (): string => {
    // Priority 1: Generated image (result from API)
    if (generatedImage) return generatedImage;

    // // Priority 2: Model images (for preview)
    // if (uploadedModelImage) return uploadedModelImage;
    // if (selectedModel) {
    //   const modelData = models.find((m) => m.id === selectedModel);
    //   if (modelData) return modelData.image;
    // }

    // // Priority 3: Clothing images (for preview)
    // if (uploadedImage) return uploadedImage;
    // if (selectedRecentItem) return selectedRecentItem.image;
    // if (uploadedTopImage) return uploadedTopImage;
    // if (selectedTopItem) return selectedTopItem.image;
    // if (uploadedBottomImage) return uploadedBottomImage;
    // if (selectedBottomItem) return selectedBottomItem.image;

    return "";
  };

  return (
    <div className="bg-gray-800/20 backdrop-blur-sm border border-gray-600 rounded-2xl overflow-hidden">
      <div className="relative">
        {isProcessing ? (
          // Show loading state while processing
          <div className="w-full h-96 flex items-center justify-center ">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500 mx-auto mb-4"></div>
              <p className="text-white text-lg font-medium">
                Generating Virtual Try-On...
              </p>
              <p className="text-gray-400 text-sm mt-2">
                This may take a few moments
              </p>
            </div>
          </div>
        ) : (
          <img
            src={getDisplayImage()}
            alt="Virtual try-on result"
            className="w-full h-auto object-contain"
            style={{
              background:
                "url(\"data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' width='20' height='20' patternUnits='userSpaceOnUse'%3e%3crect width='10' height='10' fill='%23f3f4f6'/%3e%3crect x='10' y='10' width='10' height='10' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e\")",
            }}
          />
        )}

        {/* Control Buttons Overlay - only show when not processing */}
        {!isProcessing && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center gap-3">
            <button
              onClick={onReset}
              className="flex items-center justify-center w-10 h-10 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              title="Reset"
            >
              <RefreshCw size={18} />
            </button>
            <button
              onClick={onDownload}
              disabled={!generatedImage}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              <Download size={18} />
              Download
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultPanel;
