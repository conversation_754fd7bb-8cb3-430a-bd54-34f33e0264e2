import React from "react";

interface ClothingItem {
  id: string;
  image: string;
  type: string;
  name?: string;
  category?: string;
}

interface RecentItemsProps {
  items: ClothingItem[];
  selectedItem: ClothingItem | null;
  onItemClick: (item: ClothingItem) => void;
  onSeeAllClick: () => void;
  isLoading?: boolean;
  clothingMode: "Single clothes" | "Top & Bottom";
}

const RecentItems: React.FC<RecentItemsProps> = ({
  items,
  selectedItem,
  onItemClick,
  onSeeAllClick,
  isLoading = false,
  clothingMode,
}) => {
  console.log("Recent clothing items:", items);
  const clothingItems = items.filter(
    (item) => item.type !== "full_set" || clothingMode === "Single clothes"
  );
  return (
    <div className="mb-6 relative">
      <div className="flex justify-between items-center mb-3">
        <h4 className="text-white font-medium">Recent Clothing Items</h4>
        <button
          onClick={onSeeAllClick}
          className="text-gray-400 hover:text-gray-300 cursor-pointer transition-colors"
        >
          See all
        </button>
      </div>
      <div className="grid grid-cols-4 gap-2">
        {isLoading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, index) => (
            <div
              key={`loading-${index}`}
              className="aspect-square bg-gray-700 rounded-lg animate-pulse"
            />
          ))
        ) : items.length === 0 ? (
          // Empty state
          <div className="col-span-4 text-center py-8 text-gray-400">
            <p>No clothing items yet</p>
            <p className="text-sm">Upload clothing items to see them here</p>
          </div>
        ) : (
          // Recent items
          clothingItems.slice(0, 6).map((item) => (
            <div
              key={item.id}
              onClick={() => onItemClick(item)}
              className={`aspect-square bg-gray-700 rounded-lg overflow-hidden cursor-pointer border-2 transition-colors ${
                selectedItem?.id === item.id
                  ? "border-purple-400"
                  : "border-transparent hover:border-gray-500"
              }`}
            >
              <img
                src={item.image}
                alt={`${item.name || "Clothing item"} - ${item.type}`}
                className="w-full h-full object-cover object-top"
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default RecentItems;
