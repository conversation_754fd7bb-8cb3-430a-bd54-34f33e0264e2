import React from "react";

interface ClothingItem {
  id: string;
  image: string;
  type: string;
}

interface RecentItemsProps {
  items: ClothingItem[];
  selectedItem: ClothingItem | null;
  onItemClick: (item: ClothingItem) => void;
  onSeeAllClick: () => void;
}

const RecentItems: React.FC<RecentItemsProps> = ({
  items,
  selectedItem,
  onItemClick,
  onSeeAllClick,
}) => {
  console.log("items", items);
  return (
    <div className="mb-6 relative">
      <div className="flex justify-between items-center mb-3">
        <h4 className="text-white font-medium">Recent Items</h4>
        <button
          onClick={onSeeAllClick}
          className="text-gray-400 hover:text-gray-300 cursor-pointer transition-colors"
        >
          See all
        </button>
      </div>
      <div className="grid grid-cols-4 gap-2">
        {items.slice(0, 6).map((item) => (
          <div
            key={item.id}
            onClick={() => onItemClick(item)}
            className={`aspect-square bg-gray-700 rounded-lg overflow-hidden cursor-pointer border-2 transition-colors ${
              selectedItem?.id === item.id
                ? "border-purple-400"
                : "border-transparent hover:border-gray-500"
            }`}
          >
            <img
              src={item.image}
              alt={`Clothing item ${item.type} ${item.id}`}
              className="w-full h-full object-cover object-top"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecentItems;
