import React from "react";
import { X } from "lucide-react";

interface ClothingItem {
  id: string;
  image: string;
  type: string;
}

interface ClothesModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTab: "all" | "top" | "bottom";
  onTabChange: (tab: "all" | "top" | "bottom") => void;
  allItems: ClothingItem[];
  topItems: ClothingItem[];
  bottomItems: ClothingItem[];
  selectedItem: ClothingItem | null;
  selectedTopItem: ClothingItem | null;
  selectedBottomItem: ClothingItem | null;
  onItemClick: (
    item: ClothingItem,
    targetType?: "single" | "top" | "bottom"
  ) => void;
  clothingMode: "Single clothes" | "Top & Bottom";
}

const ClothesModal: React.FC<ClothesModalProps> = ({
  isOpen,
  onClose,
  selectedTab,
  onTabChange,
  allItems,
  topItems,
  bottomItems,
  selectedItem,
  selectedTopItem,
  selectedBottomItem,
  onItemClick,
  clothingMode,
}) => {
  if (!isOpen) return null;

  const getModalItems = (): ClothingItem[] => {
    switch (selectedTab) {
      case "top":
        return topItems;
      case "bottom":
        return bottomItems;
      default:
        return allItems;
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleItemClick = (item: ClothingItem) => {
    if (clothingMode === "Top & Bottom") {
      // In Top & Bottom mode, determine target based on selected tab
      if (selectedTab === "top" || selectedTab === "all") {
        onItemClick(item, "top");
      } else if (selectedTab === "bottom") {
        onItemClick(item, "bottom");
      }
    } else {
      // In Single clothes mode
      onItemClick(item, "single");
    }
  };

  const getSelectedItemForTab = (item: ClothingItem): boolean => {
    if (clothingMode === "Single clothes") {
      return selectedItem?.id === item.id;
    } else {
      // In Top & Bottom mode, check based on current tab
      if (selectedTab === "top" || selectedTab === "all") {
        return selectedTopItem?.id === item.id;
      } else if (selectedTab === "bottom") {
        return selectedBottomItem?.id === item.id;
      }
    }
    return false;
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-gray-800 border border-gray-600 rounded-2xl max-w-4xl w-full max-h-[80vh] flex flex-col">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-600">
          <h2 className="text-white text-xl font-semibold">
            {clothingMode === "Top & Bottom"
              ? "Select Clothing Items"
              : "All Clothes"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Modal Tabs - Only show if we have different categories */}
        {(topItems.length > 0 || bottomItems.length > 0) && (
          <div className="flex gap-2 p-6 pb-0">
            <button
              onClick={() => onTabChange("all")}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedTab === "all"
                  ? "bg-purple-600 text-white"
                  : "bg-gray-700 text-gray-300 hover:bg-gray-600"
              }`}
            >
              All
            </button>
            {topItems.length > 0 && (
              <button
                onClick={() => onTabChange("top")}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedTab === "top"
                    ? "bg-purple-600 text-white"
                    : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                }`}
              >
                Top
              </button>
            )}
            {bottomItems.length > 0 && (
              <button
                onClick={() => onTabChange("bottom")}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedTab === "bottom"
                    ? "bg-purple-600 text-white"
                    : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                }`}
              >
                Bottom
              </button>
            )}
          </div>
        )}

        {/* Modal Content */}
        <div className="flex-1 overflow-auto p-6">
          {clothingMode === "Top & Bottom" && selectedTab !== "all" && (
            <div className="mb-4 p-3 bg-purple-900/20 rounded-lg border border-purple-600/30">
              <p className="text-purple-200 text-sm">
                Selecting from "{selectedTab}" tab will add to your{" "}
                {selectedTab} clothing slot
              </p>
            </div>
          )}

          <div className="grid grid-cols-6 gap-4">
            {getModalItems().map((item) => (
              <div
                key={item.id}
                onClick={() => handleItemClick(item)}
                className={`aspect-square bg-gray-700 rounded-lg overflow-hidden cursor-pointer border-2 transition-colors ${
                  getSelectedItemForTab(item)
                    ? "border-purple-400"
                    : "border-transparent hover:border-gray-500"
                }`}
              >
                <img
                  src={item.image}
                  alt={`${item.type} ${item.id}`}
                  className="w-full h-full object-cover object-top"
                />
              </div>
            ))}
          </div>

          {getModalItems().length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400">
                No items available in this category
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClothesModal;
