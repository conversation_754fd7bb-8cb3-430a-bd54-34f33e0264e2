import { Text } from "@/components/ui/text";
import type { BlogPost } from "@/services/blog.service";
import { useNavigate } from "react-router-dom";

interface BlogCardProps {
  blog: BlogPost;
}

const BlogCard: React.FC<BlogCardProps> = ({ blog }) => {
    const navigate = useNavigate();
  // Format the published date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  return (
    <div
        onClick={()=>navigate(`/blog/${blog.slug}`)}
      style={{
        background:
          "linear-gradient(#1f2937, #1f2937)  padding-box, linear-gradient(to bottom, #A166FC, #999999) border-box",
        border: "1px solid transparent",
      }}
      className="flex z-10 relative overflow-hidden rounded-xl  flex-col bg-white/5 border border-white/25 text-white  shadow-md w-full  "
    >
      <div className="absolute  bg-[#D9D9D9]/36 top-4 right-6 px-5 py-1 rounded-sm">
        <Text variant={"card_body"} className="text-white font-medium">
          {blog.category}
        </Text>
      </div>
      <img
        className="w-full h-[350px] object-cover overflow-hidden rounded-xl"
        src={blog.imageUrl}
        alt={blog.title}
      />
      <div className="flex flex-col gap-5 px-8 py-5">
        <div className="flex flex-col gap-2">
          <Text variant={"card_body"}>{formatDate(blog.publishedAt)}</Text>
          <Text variant={"card_body"} className="text-white font-semibold">
            {blog.title}
          </Text>
        </div>
        <Text variant={"card_body"}>
          BY <span className=" font-semibold">{blog.author.name}</span>
        </Text>
      </div>
    </div>
  );
};

export default BlogCard;
