import React, { useState, useEffect } from "react";
import {
  X,
  Plus,
  Tag,
  FolderPlus,
  Upload,
  Image as ImageIcon,
} from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";
import AdminBlogService from "@/services/adminBlog.service";
import type {
  BlogCategory,
  BlogTag,
  BlogPost,
} from "@/services/adminBlog.service";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";

interface CreateBlogModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (post: BlogPost) => void;
  allCategories: BlogCategory[];
  fetchCategories?: () => Promise<void>;
  postToEdit?: BlogPost | null;
}

const CreateBlogModal: React.FC<CreateBlogModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  allCategories,
  fetchCategories,
  postToEdit,
}) => {
  const { user } = useApp();
  const [isLoading, setIsLoading] = useState({
    loading: false,
    loadingFor: "",
  });
  const [newTag, setNewTag] = useState("");
  const [imagePreview, setImagePreview] = useState<string>("");
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [createdTags, setCreatedTags] = useState<BlogTag[]>([]);
  const [isCreatingTag, setIsCreatingTag] = useState(false);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState("");
  const [newCategoryDescription, setNewCategoryDescription] = useState("");
  const [isCreatingCategory, setIsCreatingCategory] = useState(false);

  const [formData, setFormData] = useState({
    title: "",
    content: "",
    excerpt: "",
    slug: "",
    categoryIds: [] as string[],
    tagIds: [] as string[],
    status: "DRAFT" as "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED",
    authorId: user?.id || "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (postToEdit) {
      setFormData({
        title: postToEdit.title,
        content: postToEdit.content,
        excerpt: postToEdit.excerpt || "",
        slug: postToEdit.slug,
        categoryIds: postToEdit.categoryIds || [],
        tagIds: postToEdit.tagIds || [],
        status: postToEdit.status,
        authorId: postToEdit.authorId || user?.id || "",
      });
      if (postToEdit.featuredImage) {
        setImagePreview(postToEdit.featuredImage);
      }
      const fetchTags = async () => {
        try {
          const allTags = await AdminBlogService.getTags();
          const postTags = allTags.filter((tag) =>
            postToEdit.tagIds?.includes(tag.id)
          );
          setCreatedTags(postTags);
        } catch (error) {
          console.error("Failed to fetch tags:", error);
        }
      };
      fetchTags();
    }
  }, [postToEdit, user?.id]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = "Title is required";
    if (!formData.content.trim()) newErrors.content = "Content is required";
    if (!formData.excerpt.trim()) newErrors.excerpt = "Excerpt is required";
    if (!formData.categoryIds.length)
      newErrors.category = "Category is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    if (name === "category") {
      setFormData((prev) => ({ ...prev, categoryIds: value ? [value] : [] }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    if (errors[name] || (name === "category" && errors.category)) {
      setErrors((prev) => ({ ...prev, [name]: "", category: "" }));
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.type.startsWith("image/")) {
        toast.error("Please select a valid image file");
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size should be less than 5MB");
        return;
      }

      setSelectedImageFile(file);
      const reader = new FileReader();
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);

      if (errors.featuredImage) {
        setErrors((prev) => ({ ...prev, featuredImage: "" }));
      }
    }
  };

  const removeImage = () => {
    setSelectedImageFile(null);
    setImagePreview("");
    const fileInput = document.getElementById(
      "image-upload"
    ) as HTMLInputElement;
    if (fileInput) fileInput.value = "";
  };

  const handleAddTag = async () => {
    if (
      !newTag.trim() ||
      createdTags.some((tag) => tag.name === newTag.trim())
    ) {
      return;
    }

    setIsCreatingTag(true);
    try {
      const createdTag = await AdminBlogService.createTag({
        name: newTag.trim(),
      });
      setCreatedTags((prev) => [...prev, createdTag]);
      setFormData((prev) => ({
        ...prev,
        tagIds: [...prev.tagIds, createdTag.id],
      }));
      setNewTag("");
      toast.success(`Tag "${createdTag.name}" created successfully!`);
    } catch (error) {
      console.error("Failed to create tag:", error);
      toast.error("Failed to create tag. Please try again.");
    } finally {
      setIsCreatingTag(false);
    }
  };

  const handleRemoveTag = (tagId: string) => {
    setFormData((prev) => ({
      ...prev,
      tagIds: prev.tagIds.filter((id) => id !== tagId),
    }));
    setCreatedTags((prev) => prev.filter((tag) => tag.id !== tagId));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) return;

    setIsCreatingCategory(true);
    try {
      const newCategory = await AdminBlogService.createCategory({
        name: newCategoryName.trim(),
        description: newCategoryDescription.trim(),
      });

      if (fetchCategories) await fetchCategories();

      setFormData((prev) => ({ ...prev, categoryIds: [newCategory.id] }));
      setNewCategoryName("");
      setNewCategoryDescription("");
      setShowAddCategory(false);
      toast.success("Category created successfully!");
    } catch (error) {
      console.error("Failed to create category:", error);
      toast.error("Failed to create category");
    } finally {
      setIsCreatingCategory(false);
    }
  };

  const handleCategoryKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleCreateCategory();
    }
    if (e.key === "Escape") {
      setShowAddCategory(false);
      setNewCategoryName("");
      setNewCategoryDescription("");
    }
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, "")
      .replace(/[\s_-]+/g, "-")
      .replace(/^-+|-+$/g, "");
  };

  const handleSubmit = async (
    status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED"
  ) => {
    if (!validateForm()) return;

    setIsLoading({ loading: true, loadingFor: status });
    try {
      const submitFormData = new FormData();
      submitFormData.append("title", formData.title);
      submitFormData.append("content", formData.content);
      submitFormData.append("excerpt", formData.excerpt);
      submitFormData.append("slug", generateSlug(formData.title));
      submitFormData.append("status", status);

      if (formData.authorId)
        submitFormData.append("authorId", formData.authorId);
      formData.categoryIds.forEach((id) =>
        submitFormData.append("categoryIds[]", id)
      );
      formData.tagIds.forEach((id) => submitFormData.append("tagIds[]", id));
      if (selectedImageFile) {
        submitFormData.append("featuredImage", selectedImageFile);
      } else if (postToEdit?.featuredImage) {
        submitFormData.append("featuredImageUrl", postToEdit.featuredImage);
      }

      let result: BlogPost;
      if (postToEdit) {
        result = await AdminBlogService.updatePost(
          postToEdit.id,
          submitFormData
        );
        toast.success("Blog post updated successfully!");
      } else {
        result = await AdminBlogService.createPost(submitFormData);
        toast.success(`Blog post ${status.toLowerCase()} successfully!`);
      }

      if (onSuccess) onSuccess(result);
      handleClose();
    } catch (error) {
      console.error("Failed to save blog post:", error);
      toast.error(
        `Failed to save: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setIsLoading({ loading: false, loadingFor: "" });
    }
  };

  const handleClose = () => {
    setFormData({
      title: "",
      content: "",
      excerpt: "",
      slug: "",
      categoryIds: [],
      tagIds: [],
      status: "DRAFT",
      authorId: user?.id || "",
    });
    setErrors({});
    setNewTag("");
    setImagePreview("");
    setSelectedImageFile(null);
    setCreatedTags([]);
    setShowAddCategory(false);
    setNewCategoryName("");
    setNewCategoryDescription("");
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#2A2D35] rounded-xl max-w-4xl w-full mx-auto relative max-h-[90vh] overflow-hidden">
        <button
          onClick={handleClose}
          disabled={isLoading.loading}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10"
        >
          <X size={20} />
        </button>

        <div className="p-6 overflow-y-auto max-h-[90vh]">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-white mb-2">
              {postToEdit ? "Edit Blog Post" : "Create New Blog Post"}
            </h2>
            <p className="text-gray-400">
              Fill in the details below to {postToEdit ? "update" : "create"}{" "}
              your blog post
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-700 border ${
                    errors.title ? "border-red-500" : "border-gray-600"
                  } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`}
                  placeholder="Enter your blog post title"
                />
                {errors.title && (
                  <p className="text-red-400 text-sm mt-1">{errors.title}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Excerpt *
                </label>
                <textarea
                  name="excerpt"
                  value={formData.excerpt}
                  onChange={handleInputChange}
                  rows={3}
                  className={`w-full px-4 py-3 bg-gray-700 border ${
                    errors.excerpt ? "border-red-500" : "border-gray-600"
                  } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors resize-none`}
                  placeholder="Brief description of your post..."
                />
                {errors.excerpt && (
                  <p className="text-red-400 text-sm mt-1">{errors.excerpt}</p>
                )}
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Category *
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowAddCategory(!showAddCategory)}
                    className="text-purple-400 hover:text-purple-300 text-sm flex items-center gap-1 transition-colors"
                  >
                    <FolderPlus size={14} />
                    Add New
                  </button>
                </div>

                {showAddCategory && (
                  <div className="mb-4 p-4 bg-gray-800 rounded-lg border border-gray-600 space-y-3">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-white">
                        Create New Category
                      </h4>
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddCategory(false);
                          setNewCategoryName("");
                          setNewCategoryDescription("");
                        }}
                        className="text-gray-400 hover:text-white transition-colors"
                      >
                        <X size={16} />
                      </button>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-400 mb-1">
                        Category Name *
                      </label>
                      <input
                        type="text"
                        value={newCategoryName}
                        onChange={(e) => setNewCategoryName(e.target.value)}
                        onKeyPress={handleCategoryKeyPress}
                        placeholder="Enter category name"
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-400 mb-1">
                        Description
                      </label>
                      <textarea
                        value={newCategoryDescription}
                        onChange={(e) =>
                          setNewCategoryDescription(e.target.value)
                        }
                        placeholder="Brief description of this category"
                        rows={2}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors text-sm resize-none"
                      />
                    </div>

                    <div className="flex gap-2 pt-2">
                      <button
                        type="button"
                        onClick={handleCreateCategory}
                        disabled={isCreatingCategory || !newCategoryName.trim()}
                        className="flex-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
                      >
                        {isCreatingCategory ? "Creating..." : "Create Category"}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddCategory(false);
                          setNewCategoryName("");
                          setNewCategoryDescription("");
                        }}
                        className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
                      >
                        Cancel
                      </button>
                    </div>

                    <p className="text-xs text-gray-400">
                      Press Enter on name field to create or Escape to cancel
                    </p>
                  </div>
                )}

                <select
                  name="category"
                  value={formData.categoryIds[0] || ""}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-700 border ${
                    errors.category ? "border-red-500" : "border-gray-600"
                  } rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors`}
                >
                  <option value="">Select a category</option>
                  {allCategories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="text-red-400 text-sm mt-1">{errors.category}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Featured Image
                </label>
                {!imagePreview ? (
                  <div className="relative">
                    <input
                      type="file"
                      id="image-upload"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <label
                      htmlFor="image-upload"
                      className="w-full h-32 border-2 border-dashed border-gray-600 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-purple-500 transition-colors bg-gray-700/50"
                    >
                      <Upload size={24} className="text-gray-400 mb-2" />
                      <span className="text-sm text-gray-400">
                        Click to upload image
                      </span>
                      <span className="text-xs text-gray-500 mt-1">
                        Max size: 5MB (JPG, PNG, GIF)
                      </span>
                    </label>
                  </div>
                ) : (
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={removeImage}
                      className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 transition-colors"
                    >
                      <X size={16} />
                    </button>
                    <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                      <ImageIcon size={12} />
                      {selectedImageFile?.name || "Existing Image"}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Content *
                </label>
                <textarea
                  name="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  rows={12}
                  className={`w-full px-4 py-3 bg-gray-700 border ${
                    errors.content ? "border-red-500" : "border-gray-600"
                  } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors resize-none`}
                  placeholder="Write your blog post content here..."
                />
                {errors.content && (
                  <p className="text-red-400 text-sm mt-1">{errors.content}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Tags
                </label>
                <div className="flex gap-2 mb-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleKeyPress}
                    disabled={isCreatingTag}
                    className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors disabled:opacity-50"
                    placeholder="Add a tag"
                  />
                  <button
                    type="button"
                    onClick={handleAddTag}
                    disabled={isCreatingTag || !newTag.trim()}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    {isCreatingTag ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Plus size={16} />
                    )}
                  </button>
                </div>

                {formData.tagIds.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tagIds.map((tagId) => {
                      const tag = createdTags.find((t) => t.id === tagId);
                      return tag ? (
                        <span
                          key={tagId}
                          className="inline-flex items-center gap-1 px-3 py-1 bg-gray-600 text-white rounded-full text-sm"
                        >
                          <Tag size={12} />
                          {tag.name}
                          <button
                            type="button"
                            onClick={() => handleRemoveTag(tagId)}
                            className="ml-1 hover:text-red-400 transition-colors"
                          >
                            <X size={12} />
                          </button>
                        </span>
                      ) : null;
                    })}
                  </div>
                )}

                {createdTags.length > 0 && (
                  <div className="mt-2 text-xs text-gray-400">
                    Created tags:{" "}
                    {createdTags.map((tag) => tag.name).join(", ")}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-3 mt-8 pt-6 border-t border-gray-700">
            <button
              onClick={handleClose}
              disabled={isLoading.loading}
              className="flex-1 px-6 py-3 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => handleSubmit("DRAFT")}
              disabled={isLoading.loading || isCreatingTag}
              className="flex-1 px-6 py-3 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
            >
              {isLoading.loading && isLoading.loadingFor === "DRAFT"
                ? "Saving..."
                : "Save as Draft"}
            </button>
            <ShadowButton
              onClick={() => handleSubmit("PUBLISHED")}
              disabled={isLoading.loading || isCreatingTag}
              className="flex-1 px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading.loading && isLoading.loadingFor === "PUBLISHED"
                ? "Publishing..."
                : postToEdit
                ? "Update"
                : "Publish"}
            </ShadowButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateBlogModal;

// import React, { useState, useEffect } from "react";
// import {
//   X,
//   Plus,
//   Tag,
//   FolderPlus,
//   Upload,
//   Image as ImageIcon,
// } from "lucide-react";
// import ShadowButton from "@/components/ui/shadowButton";
// import AdminBlogService from "@/services/adminBlog.service";
// import type {
//   CreateBlogPostPayload,
//   UpdateBlogPostPayload,
//   BlogCategory,
//   BlogTag,
//   BlogPost,
// } from "@/services/adminBlog.service";
// import { toast } from "sonner";
// import { useApp } from "@/contexts/useApp";

// interface CreateBlogModalProps {
//   isOpen: boolean;
//   onClose: () => void;
//   onSuccess?: (post: BlogPost) => void;
//   allCategories: BlogCategory[];
//   fetchCategories?: () => Promise<void>;
//   postToEdit?: BlogPost | null;
// }

// const CreateBlogModal: React.FC<CreateBlogModalProps> = ({
//   isOpen,
//   onClose,
//   onSuccess,
//   allCategories,
//   fetchCategories,
//   postToEdit,
// }) => {
// //   const isShowCatInput = false;

//   const [isLoading, setIsLoading] = useState({
//     loading: false,
//     loadingFor: "",
//   });
//   const { user } = useApp();
//   const [newTag, setNewTag] = useState("");
//   const [imagePreview, setImagePreview] = useState<string>("");
//   const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
//   const [createdTags, setCreatedTags] = useState<BlogTag[]>([]);
//   const [isCreatingTag, setIsCreatingTag] = useState(false);
//   const [showAddCategory, setShowAddCategory] = useState(false);
//   const [newCategoryName, setNewCategoryName] = useState("");
//   const [newCategoryDescription, setNewCategoryDescription] = useState("");
//   const [isCreatingCategory, setIsCreatingCategory] = useState(false);

//   const [formData, setFormData] = useState<CreateBlogPostPayload>({
//     title: "",
//     content: "",
//     excerpt: "",
//     slug: "",
//     categoryIds: [],
//     tagIds: [],
//     status: "DRAFT",
//   });

//   const [errors, setErrors] = useState<Record<string, string>>({});

//   useEffect(() => {
//     if (postToEdit) {
//       setFormData({
//         title: postToEdit.title,
//         content: postToEdit.content,
//         excerpt: postToEdit.excerpt || "",
//         slug: postToEdit.slug,
//         categoryIds: postToEdit.categoryIds || [],
//         tagIds: postToEdit.tagIds || [],
//         status: postToEdit.status,
//         featuredImage: postToEdit.featuredImage,
//         authorId: postToEdit.authorId,
//       });
//       if (postToEdit.featuredImage) {
//         setImagePreview(postToEdit.featuredImage);
//       }
//       // Fetch tags for display
//       const fetchTags = async () => {
//         try {
//           const allTags = await AdminBlogService.getTags();
//           const postTags = allTags.filter((tag) =>
//             postToEdit.tagIds?.includes(tag.id)
//           );
//           setCreatedTags(postTags);
//         } catch (error) {
//           console.error("Failed to fetch tags:", error);
//         }
//       };
//       fetchTags();
//     }
//   }, [postToEdit]);

//   const validateForm = (): boolean => {
//     const newErrors: Record<string, string> = {};

//     if (!formData.title.trim()) {
//       newErrors.title = "Title is required";
//     }
//     if (!formData.content.trim()) {
//       newErrors.content = "Content is required";
//     }
//     if (!formData.excerpt?.trim()) {
//       newErrors.excerpt = "Excerpt is required";
//     }
//     if (!formData.categoryIds?.length) {
//       newErrors.category = "Category is required";
//     }

//     setErrors(newErrors);
//     return Object.keys(newErrors).length === 0;
//   };

//   const handleInputChange = (
//     e: React.ChangeEvent<
//       HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
//     >
//   ) => {
//     const { name, value } = e.target;
//     if (name === "category") {
//       setFormData((prev) => ({ ...prev, categoryIds: value ? [value] : [] }));
//     } else {
//       setFormData((prev) => ({ ...prev, [name]: value }));
//     }

//     if (errors[name] || (name === "category" && errors.category)) {
//       setErrors((prev) => ({ ...prev, [name]: "", category: "" }));
//     }
//   };

//   const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0];
//     if (file) {
//       if (!file.type.startsWith("image/")) {
//         toast.error("Please select a valid image file");
//         return;
//       }
//       if (file.size > 5 * 1024 * 1024) {
//         toast.error("Image size should be less than 5MB");
//         return;
//       }

//       setSelectedImageFile(file);
//       const reader = new FileReader();
//       reader.onload = (event) => {
//         const result = event.target?.result as string;
//         setImagePreview(result);
//         setFormData((prev) => ({ ...prev, featuredImage: result }));
//       };
//       reader.readAsDataURL(file);

//       if (errors.featuredImage) {
//         setErrors((prev) => ({ ...prev, featuredImage: "" }));
//       }
//     }
//   };

//   const removeImage = () => {
//     setSelectedImageFile(null);
//     setImagePreview("");
//     setFormData((prev) => ({ ...prev, featuredImage: undefined }));
//     const fileInput = document.getElementById(
//       "image-upload"
//     ) as HTMLInputElement;
//     if (fileInput) fileInput.value = "";
//   };

//   const handleAddTag = async () => {
//     if (
//       !newTag.trim() ||
//       createdTags.some((tag) => tag.name === newTag.trim())
//     ) {
//       return;
//     }

//     setIsCreatingTag(true);
//     try {
//       const createdTag = await AdminBlogService.createTag({
//         name: newTag.trim(),
//       });
//       setCreatedTags((prev) => [...prev, createdTag]);
//       setFormData((prev) => ({
//         ...prev,
//         tagIds: [...(prev.tagIds || []), createdTag.id],
//       }));
//       setNewTag("");
//       toast.success(`Tag "${createdTag.name}" created successfully!`);
//     } catch (error) {
//       console.error("Failed to create tag:", error);
//       toast.error("Failed to create tag. Please try again.");
//     } finally {
//       setIsCreatingTag(false);
//     }
//   };

//   const handleRemoveTag = (tagId: string) => {
//     setFormData((prev) => ({
//       ...prev,
//       tagIds: prev.tagIds?.filter((id) => id !== tagId) || [],
//     }));
//     setCreatedTags((prev) => prev.filter((tag) => tag.id !== tagId));
//   };

//   const handleKeyPress = (e: React.KeyboardEvent) => {
//     if (e.key === "Enter") {
//       e.preventDefault();
//       handleAddTag();
//     }
//   };

//   const handleCreateCategory = async () => {
//     if (!newCategoryName.trim()) return;

//     setIsCreatingCategory(true);
//     try {
//       const newCategory = await AdminBlogService.createCategory({
//         name: newCategoryName.trim(),
//         description: newCategoryDescription.trim(),
//       });

//       if (fetchCategories) {
//         await fetchCategories();
//       }

//       setFormData((prev) => ({ ...prev, categoryIds: [newCategory.id] }));
//       setNewCategoryName("");
//       setNewCategoryDescription("");
//       setShowAddCategory(false);
//       toast.success("Category created successfully!");
//     } catch (error) {
//       console.error("Failed to create category:", error);
//       toast.error("Failed to create category");
//     } finally {
//       setIsCreatingCategory(false);
//     }
//   };

//   const handleCategoryKeyPress = (e: React.KeyboardEvent) => {
//     if (e.key === "Enter") {
//       e.preventDefault();
//       handleCreateCategory();
//     }
//     if (e.key === "Escape") {
//       setShowAddCategory(false);
//       setNewCategoryName("");
//       setNewCategoryDescription("");
//     }
//   };

//   const generateSlug = (title: string): string => {
//     return title
//       .toLowerCase()
//       .trim()
//       .replace(/[^\w\s-]/g, "")
//       .replace(/[\s_-]+/g, "-")
//       .replace(/^-+|-+$/g, "");
//   };

//   const handleSubmit = async (
//     status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED"
//   ) => {
//     if (!validateForm()) return;

//     setIsLoading({ loading: true, loadingFor: status });
//     try {
//       let featuredImage = formData.featuredImage;

//       if (selectedImageFile) {
//         // TODO: Implement actual image upload to server/cloud storage
//         console.log("Image file to upload:", selectedImageFile);
//         // featuredImage = await uploadImage(selectedImageFile);
//       }

//       if (postToEdit) {
//         const updateData: UpdateBlogPostPayload = {
//           title: formData.title,
//           content: formData.content,
//           excerpt: formData.excerpt,
//           slug: generateSlug(formData.title),
//           categoryIds: formData.categoryIds,
//           tagIds: formData.tagIds,
//           status,
//           featuredImage,
//         };
//         const updatedPost = await AdminBlogService.updatePost(
//           postToEdit.id,
//           updateData
//         );
//         if (onSuccess) {
//           onSuccess(updatedPost);
//         }
//         toast.success(`Blog post updated successfully!`);
//       } else {
//         const submitData: CreateBlogPostPayload = {
//           title: formData.title,
//           content: formData.content,
//           excerpt: formData.excerpt,
//           slug: generateSlug(formData.title),
//           categoryIds: formData.categoryIds,
//           tagIds: formData.tagIds,
//           status,
//           featuredImage,
//           authorId: user?.id,
//         };
//         const newPost = await AdminBlogService.createPost(submitData);
//         if (onSuccess) {
//           onSuccess(newPost);
//         }
//         toast.success(`Blog post ${status.toLowerCase()} successfully!`);
//       }
//       handleClose();
//     } catch (error) {
//       console.error("Failed to save blog post:", error);
//       toast.error(
//         `Failed to save: ${
//           error instanceof Error ? error.message : "Unknown error"
//         }`
//       );
//     } finally {
//       setIsLoading({ loading: false, loadingFor: "" });
//     }
//   };

//   const handleClose = () => {
//     setFormData({
//       title: "",
//       content: "",
//       excerpt: "",
//       slug: "",
//       categoryIds: [],
//       tagIds: [],
//       status: "DRAFT",
//     });
//     setErrors({});
//     setNewTag("");
//     setImagePreview("");
//     setSelectedImageFile(null);
//     setCreatedTags([]);
//     setShowAddCategory(false);
//     setNewCategoryName("");
//     setNewCategoryDescription("");
//     onClose();
//   };

//   if (!isOpen) return null;

//   return (
//     <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
//       <div className="bg-[#2A2D35] rounded-xl max-w-4xl w-full mx-auto relative max-h-[90vh] overflow-hidden">
//         <button
//           onClick={handleClose}
//           disabled={isLoading.loading}
//           className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10"
//         >
//           <X size={20} />
//         </button>

//         <div className="p-6 overflow-y-auto max-h-[90vh]">
//           <div className="mb-6">
//             <h2 className="text-2xl font-semibold text-white mb-2">
//               {postToEdit ? "Edit Blog Post" : "Create New Blog Post"}
//             </h2>
//             <p className="text-gray-400">
//               Fill in the details below to {postToEdit ? "update" : "create"}{" "}
//               your blog post
//             </p>
//           </div>

//           <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
//             <div className="space-y-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-300 mb-2">
//                   Title *
//                 </label>
//                 <input
//                   type="text"
//                   name="title"
//                   value={formData.title}
//                   onChange={handleInputChange}
//                   className={`w-full px-4 py-3 bg-gray-700 border ${
//                     errors.title ? "border-red-500" : "border-gray-600"
//                   } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`}
//                   placeholder="Enter your blog post title"
//                 />
//                 {errors.title && (
//                   <p className="text-red-400 text-sm mt-1">{errors.title}</p>
//                 )}
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-300 mb-2">
//                   Excerpt *
//                 </label>
//                 <textarea
//                   name="excerpt"
//                   value={formData.excerpt}
//                   onChange={handleInputChange}
//                   rows={3}
//                   className={`w-full px-4 py-3 bg-gray-700 border ${
//                     errors.excerpt ? "border-red-500" : "border-gray-600"
//                   } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors resize-none`}
//                   placeholder="Brief description of your post..."
//                 />
//                 {errors.excerpt && (
//                   <p className="text-red-400 text-sm mt-1">{errors.excerpt}</p>
//                 )}
//               </div>

//               <div>
//                 <div className="flex items-center justify-between mb-2">
//                   <label className="block text-sm font-medium text-gray-300">
//                     Category *
//                   </label>
//                   <button
//                     type="button"
//                     onClick={() => setShowAddCategory(!showAddCategory)}
//                     className="text-purple-400 hover:text-purple-300 text-sm flex items-center gap-1 transition-colors"
//                   >
//                     <FolderPlus size={14} />
//                     Add New
//                   </button>
//                 </div>

//                 {showAddCategory && (
//                   <div className="mb-4 p-4 bg-gray-800 rounded-lg border border-gray-600 space-y-3">
//                     <div className="flex items-center justify-between mb-3">
//                       <h4 className="text-sm font-medium text-white">
//                         Create New Category
//                       </h4>
//                       <button
//                         type="button"
//                         onClick={() => {
//                           setShowAddCategory(false);
//                           setNewCategoryName("");
//                           setNewCategoryDescription("");
//                         }}
//                         className="text-gray-400 hover:text-white transition-colors"
//                       >
//                         <X size={16} />
//                       </button>
//                     </div>

//                     <div>
//                       <label className="block text-xs font-medium text-gray-400 mb-1">
//                         Category Name *
//                       </label>
//                       <input
//                         type="text"
//                         value={newCategoryName}
//                         onChange={(e) => setNewCategoryName(e.target.value)}
//                         onKeyPress={handleCategoryKeyPress}
//                         placeholder="Enter category name"
//                         className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors text-sm"
//                       />
//                     </div>

//                     <div>
//                       <label className="block text-xs font-medium text-gray-400 mb-1">
//                         Description
//                       </label>
//                       <textarea
//                         value={newCategoryDescription}
//                         onChange={(e) =>
//                           setNewCategoryDescription(e.target.value)
//                         }
//                         placeholder="Brief description of this category"
//                         rows={2}
//                         className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors text-sm resize-none"
//                       />
//                     </div>

//                     <div className="flex gap-2 pt-2">
//                       <button
//                         type="button"
//                         onClick={handleCreateCategory}
//                         disabled={isCreatingCategory || !newCategoryName.trim()}
//                         className="flex-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
//                       >
//                         {isCreatingCategory ? "Creating..." : "Create Category"}
//                       </button>
//                       <button
//                         type="button"
//                         onClick={() => {
//                           setShowAddCategory(false);
//                           setNewCategoryName("");
//                           setNewCategoryDescription("");
//                         }}
//                         className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
//                       >
//                         Cancel
//                       </button>
//                     </div>

//                     <p className="text-xs text-gray-400">
//                       Press Enter on name field to create or Escape to cancel
//                     </p>
//                   </div>
//                 )}

//                 <select
//                   name="category"
//                   value={formData.categoryIds?.[0] || ""}
//                   onChange={handleInputChange}
//                   className={`w-full px-4 py-3 bg-gray-700 border ${
//                     errors.category ? "border-red-500" : "border-gray-600"
//                   } rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors`}
//                 >
//                   <option value="">Select a category</option>
//                   {allCategories.map((category) => (
//                     <option key={category.id} value={category.id}>
//                       {category.name}
//                     </option>
//                   ))}
//                 </select>
//                 {errors.category && (
//                   <p className="text-red-400 text-sm mt-1">{errors.category}</p>
//                 )}
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-300 mb-2">
//                   Featured Image
//                 </label>
//                 {!imagePreview ? (
//                   <div className="relative">
//                     <input
//                       type="file"
//                       id="image-upload"
//                       accept="image/*"
//                       onChange={handleImageUpload}
//                       className="hidden"
//                     />
//                     <label
//                       htmlFor="image-upload"
//                       className="w-full h-32 border-2 border-dashed border-gray-600 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-purple-500 transition-colors bg-gray-700/50"
//                     >
//                       <Upload size={24} className="text-gray-400 mb-2" />
//                       <span className="text-sm text-gray-400">
//                         Click to upload image
//                       </span>
//                       <span className="text-xs text-gray-500 mt-1">
//                         Max size: 5MB (JPG, PNG, GIF)
//                       </span>
//                     </label>
//                   </div>
//                 ) : (
//                   <div className="relative">
//                     <img
//                       src={imagePreview}
//                       alt="Preview"
//                       className="w-full h-32 object-cover rounded-lg"
//                     />
//                     <button
//                       type="button"
//                       onClick={removeImage}
//                       className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 transition-colors"
//                     >
//                       <X size={16} />
//                     </button>
//                     <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
//                       <ImageIcon size={12} />
//                       {selectedImageFile?.name || "Existing Image"}
//                     </div>
//                   </div>
//                 )}
//               </div>
//             </div>

//             <div className="space-y-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-300 mb-2">
//                   Content *
//                 </label>
//                 <textarea
//                   name="content"
//                   value={formData.content}
//                   onChange={handleInputChange}
//                   rows={12}
//                   className={`w-full px-4 py-3 bg-gray-700 border ${
//                     errors.content ? "border-red-500" : "border-gray-600"
//                   } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors resize-none`}
//                   placeholder="Write your blog post content here..."
//                 />
//                 {errors.content && (
//                   <p className="text-red-400 text-sm mt-1">{errors.content}</p>
//                 )}
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-300 mb-2">
//                   Tags
//                 </label>
//                 <div className="flex gap-2 mb-2">
//                   <input
//                     type="text"
//                     value={newTag}
//                     onChange={(e) => setNewTag(e.target.value)}
//                     onKeyPress={handleKeyPress}
//                     disabled={isCreatingTag}
//                     className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors disabled:opacity-50"
//                     placeholder="Add a tag"
//                   />
//                   <button
//                     type="button"
//                     onClick={handleAddTag}
//                     disabled={isCreatingTag || !newTag.trim()}
//                     className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
//                   >
//                     {isCreatingTag ? (
//                       <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
//                     ) : (
//                       <Plus size={16} />
//                     )}
//                   </button>
//                 </div>

//                 {formData.tagIds && formData.tagIds.length > 0 && (
//                   <div className="flex flex-wrap gap-2">
//                     {formData.tagIds.map((tagId) => {
//                       const tag = createdTags.find((t) => t.id === tagId);
//                       return tag ? (
//                         <span
//                           key={tagId}
//                           className="inline-flex items-center gap-1 px-3 py-1 bg-gray-600 text-white rounded-full text-sm"
//                         >
//                           <Tag size={12} />
//                           {tag.name}
//                           <button
//                             type="button"
//                             onClick={() => handleRemoveTag(tagId)}
//                             className="ml-1 hover:text-red-400 transition-colors"
//                           >
//                             <X size={12} />
//                           </button>
//                         </span>
//                       ) : null;
//                     })}
//                   </div>
//                 )}

//                 {createdTags.length > 0 && (
//                   <div className="mt-2 text-xs text-gray-400">
//                     Created tags:{" "}
//                     {createdTags.map((tag) => tag.name).join(", ")}
//                   </div>
//                 )}
//               </div>
//             </div>
//           </div>

//           <div className="flex gap-3 mt-8 pt-6 border-t border-gray-700">
//             <button
//               onClick={handleClose}
//               disabled={isLoading.loading}
//               className="flex-1 cursor-pointer px-6 py-3 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
//             >
//               Cancel
//             </button>
//             <button
//               onClick={() => handleSubmit("DRAFT")}
//               disabled={isLoading.loading || isCreatingTag}
//               className="flex-1 px-6 py-3 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
//             >
//               {isLoading.loading && isLoading.loadingFor === "DRAFT"
//                 ? "Saving..."
//                 : "Save as Draft"}
//             </button>
//             <ShadowButton
//               onClick={() => handleSubmit("PUBLISHED")}
//               disabled={isLoading.loading || isCreatingTag}
//               className="flex-1 px-6 py-3"
//             >
//               {isLoading.loading && isLoading.loadingFor === "PUBLISHED"
//                 ? "Publishing..."
//                 : postToEdit
//                 ? "Update"
//                 : "Publish"}
//             </ShadowButton>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default CreateBlogModal;
