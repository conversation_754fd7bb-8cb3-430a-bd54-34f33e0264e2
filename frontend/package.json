{"name": "miragicai-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@img-comparison-slider/react": "^8.0.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/vite": "^4.1.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-before-after-slider-component": "^1.1.8", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "slugify": "^1.6.6", "sonner": "^2.0.3", "swiper": "^11.2.8", "tailwind-merge": "^3.3.0", "zod": "^3.25.9"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}