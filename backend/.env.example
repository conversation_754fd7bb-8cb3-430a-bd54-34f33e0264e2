# Server Configuration
PORT=5000
NODE_ENV=development
BASE_URL=http://localhost:5000
FRONTEND_URL=http://localhost:5173
USE_MOCK_API=false

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/miragicai?schema=public"

# JWT Authentication
JWT_SECRET=your_jwt_secret_key
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
JWT_EXPIRES_IN_MS=3600000
JWT_REFRESH_EXPIRES_IN_MS=604800000

# Session Configuration
SESSION_SECRET=your_session_secret_key

# OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=/api/v1/auth/google/callback

APPLE_CLIENT_ID=your_apple_client_id
APPLE_TEAM_ID=your_apple_team_id
APPLE_KEY_ID=your_apple_key_id
APPLE_PRIVATE_KEY=your_apple_private_key

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_PRICE_ID_STARTER=price_starter_id
STRIPE_PRICE_ID_PRO=price_pro_id
STRIPE_PRICE_ID_PREMIUM=price_premium_id

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id
PAYPAL_ENVIRONMENT=sandbox

# File Storage (Cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# AI Service APIs
SYNTHESIA_API_KEY=your_synthesia_api_key
SYNTHESIA_API_URL=https://api.synthesia.io/v2

STABILITY_AI_API_KEY=your_stability_ai_api_key
STABILITY_AI_API_URL=https://api.stability.ai/v1

REMOVAL_AI_API_KEY=your_removal_ai_api_key
REMOVAL_AI_API_URL=https://api.removal.ai/3.0

# Email Service (Resend)
RESEND_API_KEY=your_resend_api_key
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Redis (for queue)
REDIS_URL=redis://localhost:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
CORS_ORIGINS=http://localhost:5173,https://yourdomain.com
TRUST_PROXY=true
