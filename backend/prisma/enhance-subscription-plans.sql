-- Add new fields to SubscriptionPlan table
ALTER TABLE "SubscriptionPlan" 
  ADD COLUMN IF NOT EXISTS "displayName" TEXT NOT NULL DEFAULT '',
  ADD COLUMN IF NOT EXISTS "description" TEXT NOT NULL DEFAULT '',
  ADD COLUMN IF NOT EXISTS "featureHighlights" JSONB NOT NULL DEFAULT '[]',
  ADD COLUMN IF NOT EXISTS "colorScheme" TEXT,
  ADD COLUMN IF NOT EXISTS "isFeatured" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "sortOrder" INTEGER NOT NULL DEFAULT 0,
  ADD COLUMN IF NOT EXISTS "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ADD COLUMN IF NOT EXISTS "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Update existing plans with sample data
UPDATE "SubscriptionPlan"
SET 
  "displayName" = 'Free',
  "description" = 'Get started with basic features',
  "featureHighlights" = '[
    {"title": "Basic AI Generation", "description": "Access to essential AI generation tools", "icon": "Sparkles"},
    {"title": "Limited Credits", "description": "5 credits per month", "icon": "CreditCard"},
    {"title": "Standard Support", "description": "Email support with 48-hour response time", "icon": "LifeBuoy"}
  ]',
  "colorScheme" = "gray",
  "sortOrder" = 0
WHERE "name" = 'Free';

UPDATE "SubscriptionPlan"
SET 
  "displayName" = 'Starter',
  "description" = 'Perfect for beginners and small projects',
  "featureHighlights" = '[
    {"title": "Standard AI Generation", "description": "Access to all standard AI generation tools", "icon": "Sparkles"},
    {"title": "100 Credits Monthly", "description": "100 credits refreshed every month", "icon": "CreditCard"},
    {"title": "Priority Support", "description": "Email support with 24-hour response time", "icon": "LifeBuoy"}
  ]',
  "colorScheme" = "blue",
  "sortOrder" = 10
WHERE "name" = 'Starter';

UPDATE "SubscriptionPlan"
SET 
  "displayName" = 'Pro',
  "description" = 'For professionals and growing businesses',
  "featureHighlights" = '[
    {"title": "Advanced AI Generation", "description": "Access to advanced AI generation tools with higher quality", "icon": "Sparkles"},
    {"title": "500 Credits Monthly", "description": "500 credits refreshed every month", "icon": "CreditCard"},
    {"title": "Priority Support", "description": "Email and chat support with 12-hour response time", "icon": "LifeBuoy"},
    {"title": "Team Collaboration", "description": "Share projects with up to 3 team members", "icon": "Users"}
  ]',
  "colorScheme" = "purple",
  "isFeatured" = true,
  "sortOrder" = 20
WHERE "name" = 'Pro';

UPDATE "SubscriptionPlan"
SET 
  "displayName" = 'Business',
  "description" = 'For enterprises and large scale usage',
  "featureHighlights" = '[
    {"title": "Premium AI Generation", "description": "Access to all AI generation tools with highest quality", "icon": "Sparkles"},
    {"title": "2000 Credits Monthly", "description": "2000 credits refreshed every month", "icon": "CreditCard"},
    {"title": "Dedicated Support", "description": "Priority email, chat, and phone support with 4-hour response time", "icon": "LifeBuoy"},
    {"title": "Team Collaboration", "description": "Share projects with unlimited team members", "icon": "Users"},
    {"title": "API Access", "description": "Full API access for custom integrations", "icon": "Code"}
  ]',
  "colorScheme" = "amber",
  "sortOrder" = 30
WHERE "name" = 'Business';

-- Update the features field with more detailed information
UPDATE "SubscriptionPlan"
SET "features" = '{
  "videoGenerationQuota": 5,
  "imageGenerationQuota": 20,
  "backgroundRemovalQuota": 10,
  "processingPriority": "standard",
  "uploadSpeed": "1x",
  "concurrentJobs": 1,
  "advancedAI": false,
  "customTemplates": 0,
  "apiAccess": false,
  "supportLevel": "basic",
  "teamMembers": 0,
  "sharedWorkspaces": 0,
  "storageLimit": "100MB",
  "assetLibrary": "basic",
  "historyRetention": "7 days"
}'
WHERE "name" = 'Free';

UPDATE "SubscriptionPlan"
SET "features" = '{
  "videoGenerationQuota": 20,
  "imageGenerationQuota": 100,
  "backgroundRemovalQuota": 50,
  "processingPriority": "standard",
  "uploadSpeed": "2x",
  "concurrentJobs": 2,
  "advancedAI": false,
  "customTemplates": 5,
  "apiAccess": false,
  "supportLevel": "standard",
  "teamMembers": 0,
  "sharedWorkspaces": 0,
  "storageLimit": "1GB",
  "assetLibrary": "standard",
  "historyRetention": "30 days"
}'
WHERE "name" = 'Starter';

UPDATE "SubscriptionPlan"
SET "features" = '{
  "videoGenerationQuota": 100,
  "imageGenerationQuota": 500,
  "backgroundRemovalQuota": 200,
  "processingPriority": "high",
  "uploadSpeed": "5x",
  "concurrentJobs": 5,
  "advancedAI": true,
  "customTemplates": 20,
  "apiAccess": false,
  "supportLevel": "priority",
  "teamMembers": 3,
  "sharedWorkspaces": 1,
  "storageLimit": "10GB",
  "assetLibrary": "premium",
  "historyRetention": "90 days"
}'
WHERE "name" = 'Pro';

UPDATE "SubscriptionPlan"
SET "features" = '{
  "videoGenerationQuota": 500,
  "imageGenerationQuota": 2000,
  "backgroundRemovalQuota": 1000,
  "processingPriority": "highest",
  "uploadSpeed": "10x",
  "concurrentJobs": 10,
  "advancedAI": true,
  "customTemplates": "unlimited",
  "apiAccess": true,
  "supportLevel": "dedicated",
  "teamMembers": "unlimited",
  "sharedWorkspaces": 10,
  "storageLimit": "100GB",
  "assetLibrary": "enterprise",
  "historyRetention": "1 year"
}'
WHERE "name" = 'Business';
