// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and Authentication
model User {
  id                              String    @id @default(uuid())
  email                           String    @unique
  password                        String? // Make password optional for OAuth users
  role                            UserRole  @default(USER)
  stripeCustomerId                String?
  paypalPayerId                   String?
  emailVerified                   Boolean   @default(false)
  lastLogin                       DateTime?
  emailVerificationOTP            String?
  emailVerificationExpiresAt      DateTime?
  passwordResetToken              String?
  passwordResetTokenExpiresAt     DateTime?
  emailVerificationToken          String? // For email verification links
  emailVerificationTokenExpiresAt DateTime?
  createdAt                       DateTime  @default(now())
  updatedAt                       DateTime  @updatedAt

  // Relations
  profile              UserProfile?
  authTokens           AuthToken[]
  oauthAccounts        OAuthAccount[] // OAuth provider accounts
  subscriptions        UserSubscription[]
  credit               Credit?
  creditTransactions   CreditTransaction[]
  creditPurchaseOrders CreditPurchaseOrder[]
  payments             Payment[]
  paypalOrders         PaypalOrder[]
  refunds              Refund[]
  videoJobs            GeneratedVideoJob[]
  imageJobs            GeneratedImageJob[]
  bgRemovalJobs        BackgroundImageRemovalJob[]
  speedPaintingJobs    SpeedPaintingJob[]
  virtualTryOns        VirtualTryOn[]
  blogPosts            BlogPost[]
  apiUsageLogs         ApiUsageLog[]
  analyticsEvents      AnalyticsEvent[]
  modelImages          ModelImage[] // Virtual try-on model images
  recentTryons         RecentTryOn[] // Recent try-on history
  clothingItems        ClothingItem[] // Clothing item catalog
  scheduledPlanChanges ScheduledPlanChange[]
}

enum UserRole {
  USER
  ADMIN
}

model UserProfile {
  id        String  @id @default(uuid())
  userId    String  @unique
  firstName String?
  lastName  String?
  avatarUrl String?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model AuthToken {
  id        String   @id @default(uuid())
  userId    String
  tokenHash String
  expiresAt DateTime

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model OAuthAccount {
  id           String    @id @default(uuid())
  userId       String
  provider     String // "google", "apple", etc.
  providerId   String // OAuth provider's user ID
  email        String?
  name         String?
  picture      String?
  accessToken  String?
  refreshToken String?
  expiresAt    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerId])
  @@unique([provider, userId])
}

// Subscription and Payment
model SubscriptionPlan {
  id                String   @id @default(uuid())
  name              String   @unique
  displayName       String // User-friendly name for display
  description       String // Brief description of the plan
  price             Float
  currency          String   @default("USD")
  interval          String // 'week', 'month', 'year'
  creditsAmount     Float // Number of credits included in the plan
  features          Json // JSONB for limits like video_generation_quota, image_generation_quota
  featureHighlights Json // Array of key features to highlight in marketing
  colorScheme       String? // CSS color scheme for UI display
  isFeatured        Boolean  @default(false) // Whether to highlight this plan as featured
  sortOrder         Int      @default(0) // Order to display plans (lower numbers first)
  stripePriceId     String?
  paypalPlanId      String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  userSubscriptions UserSubscription[]
  scheduledChanges  ScheduledPlanChange[] @relation("newPlan")
}

model UserSubscription {
  id                   String             @id @default(uuid())
  userId               String
  planId               String
  stripeSubscriptionId String?
  paypalSubscriptionId String?
  status               SubscriptionStatus
  startDate            DateTime
  endDate              DateTime?
  currentPeriodEnd     DateTime
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt

  // Relations
  user             User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan             SubscriptionPlan      @relation(fields: [planId], references: [id])
  scheduledChanges ScheduledPlanChange[] @relation("CurrentSubscription")
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  TRIALING
  UNPAID
}

enum PlanChangeType {
  UPGRADE
  DOWNGRADE
}

enum PlanChangeStatus {
  SCHEDULED
  COMPLETED
  FAILED
  CANCELLED
}

model ScheduledPlanChange {
  id                    String           @id @default(uuid())
  userId                String
  currentSubscriptionId String
  newPlanId             String
  effectiveDate         DateTime
  changeType            PlanChangeType
  status                PlanChangeStatus @default(SCHEDULED)
  processedDate         DateTime?
  errorMessage          String?
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt

  // Relations
  user                User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  currentSubscription UserSubscription @relation("CurrentSubscription", fields: [currentSubscriptionId], references: [id])
  newPlan             SubscriptionPlan @relation("newPlan", fields: [newPlanId], references: [id])
}

model Credit {
  id            String   @id @default(uuid())
  userId        String   @unique
  balance       Float    @default(0) // Changed to Float for fractional credits
  spent         Float    @default(0) // Changed to Float for fractional credits
  lastUpdatedAt DateTime @updatedAt

  // Relations
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  creditTransactions CreditTransaction[]
}

// Credit Packages (What users can buy)
model CreditPackage {
  id             String   @id @default(uuid())
  name           String // e.g., "Starter Pack", "Pro Credits"
  description    String?
  creditsAmount  Float // How many credits this package gives
  price          Float // Price in real currency (e.g., USD)
  pricePerCredit Float? // Price per credit
  currency       String   @default("USD")
  stripePriceId  String?  @unique // Stripe Price ID for this package
  paypalPlanId   String?  @unique // Or relevant PayPal product/plan ID
  isActive       Boolean  @default(true) // Can this package be purchased?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  purchaseOrders CreditPurchaseOrder[]
}

// Service Costs (Configurable costs for each service in credits)
model ServiceCost {
  id            String   @id @default(uuid())
  serviceName   String   @unique // e.g., "imageGeneration", "videoGeneration"
  displayName   String // e.g., "Image Generation", "Video Generation"
  costInCredits Float // How many credits this service costs
  description   String? // Optional description of the service
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

// Credit Purchase Orders (Tracks attempts to buy credits)
model CreditPurchaseOrder {
  id              String        @id @default(uuid())
  userId          String
  user            User          @relation(fields: [userId], references: [id])
  creditPackageId String
  creditPackage   CreditPackage @relation(fields: [creditPackageId], references: [id])

  creditsToGrant Float // How many credits this order is for
  amountPaid     Float? // Actual amount paid (in case of discounts or changes)
  currency       String? // Currency of payment

  paymentProvider String // "STRIPE", "PAYPAL"
  paymentIntentId String? @unique // Stripe Payment Intent ID or PayPal Order ID
  status          String // "PENDING", "PROCESSING", "COMPLETED", "FAILED", "REFUNDED"

  // Additional metadata from payment provider
  providerMetadata Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  creditTransaction   CreditTransaction? @relation(fields: [creditTransactionId], references: [id])
  creditTransactionId String?            @unique
}

// Credit Transaction Ledger (The Single Source of Truth for all credit movements)
model CreditTransaction {
  id       String  @id @default(uuid())
  userId   String
  user     User    @relation(fields: [userId], references: [id])
  creditId String?
  credit   Credit? @relation(fields: [creditId], references: [id])

  type   CreditTransactionType
  amount Float // Positive for credit additions, negative for deductions

  description String // e.g., "Image generation for 'prompt xyz'", "Purchase of 'Starter Pack'", "Admin correction"

  // Linking to the source of the transaction for traceability
  purchaseOrder CreditPurchaseOrder?

  relatedJobId      String? // If type is USAGE, links to the specific AI job (e.g., GeneratedImageJobId, GeneratedVideoJobId)
  relatedAdminId    String? // If type is ADJUSTMENT, links to the admin user who made the change
  relatedEntityType String? // Type of related entity (e.g., REFUND, SUBSCRIPTION, CREDIT_PURCHASE)

  // Optional: for different types of credits (paid, promo) and their specific expiry
  creditType String    @default("PAID")
  expiresAt  DateTime?

  createdAt DateTime @default(now())

  @@index([userId, createdAt]) // Important for querying user's history
}

enum CreditTransactionType {
  PURCHASE
  USAGE
  REFUND
  ADJUSTMENT_ADD
  ADJUSTMENT_SUBTRACT
  EXPIRATION
  SUBSCRIPTION_GRANT
  BONUS
}

model Payment {
  id               String          @id @default(uuid())
  userId           String
  amount           Float
  currency         String          @default("USD")
  status           PaymentStatus
  provider         PaymentProvider
  paymentType      PaymentType
  transactionId    String?
  paymentIntentId  String?
  orderId          String?
  invoicePdf       String?
  hostedInvoiceUrl String?
  description      String?
  createdAt        DateTime        @default(now())
  metadata         Json? // For storing additional payment information
  subscriptionId   String? // Reference to the subscription this payment is for

  // Relations
  user   User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  Refund Refund[]
}

enum PaymentType {
  SUBSCRIPTION
  CREDIT_PURCHASE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum PaymentProvider {
  STRIPE
  PAYPAL
}

enum RefundStatus {
  PENDING
  APPROVED
  REJECTED
}

model Refund {
  id              String          @id @default(uuid())
  userId          String
  paymentId       String
  amount          Float
  reason          String
  status          RefundStatus    @default(PENDING)
  requestDate     DateTime        @default(now())
  processedDate   DateTime?
  processedBy     String? // Admin user ID who processed the refund
  notes           String? // Notes about the refund
  refundId        String? // ID from payment processor (Stripe/PayPal)
  payment         Payment?        @relation(fields: [paymentId], references: [id])
  paymentProvider PaymentProvider
  paymentIntentId String? // Stripe payment intent ID
  paypalOrderId   String? // PayPal order ID
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// // Keeping CreditPack for backward compatibility, but will use CreditPackage going forward
// model CreditPack {
//   id            String   @id @default(uuid())
//   name          String
//   credits       Int
//   price         Float
//   currency      String   @default("USD")
//   stripePriceId String?
//   isActive      Boolean  @default(true)
//   description   String?
//   createdAt     DateTime @default(now())
//   updatedAt     DateTime @updatedAt
// }

model PaypalOrder {
  id        String   @id @default(uuid())
  userId    String
  orderId   String   @unique
  amount    Float
  credits   Int
  status    String // CREATED, COMPLETED, FAILED
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// AI Generation Jobs
model GeneratedVideoJob {
  id           String    @id @default(uuid())
  userId       String
  prompt       String
  parameters   Json // JSONB for style, aspect ratio, etc.
  status       JobStatus
  videoUrl     String?
  thumbnailUrl String?
  storagePath  String?
  publicId     String? // if using Cloudinary
  duration     Float?
  resolution   String?
  errorMessage String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model GeneratedImageJob {
  id           String    @id @default(uuid())
  userId       String
  prompt       String
  parameters   Json // JSONB for style, aspect ratio, etc.
  status       JobStatus
  imageUrl     String?
  storagePath  String?
  publicId     String?
  resolution   String?
  errorMessage String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model BackgroundImageRemovalJob {
  id                   String    @id @default(uuid())
  userId               String
  originalImageName    String
  originalImageUrl     String
  processedImageUrl    String?
  status               JobStatus
  storagePathOriginal  String
  storagePathProcessed String?
  errorMessage         String?
  metadata             Json? // Store processing metrics, timestamps, etc.
  creditsUsed          Int       @default(1)
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model SpeedPaintingJob {
  id                String    @id @default(uuid())
  userId            String
  originalImagePath String
  originalImageUrl  String
  processedVideoUrl String?
  status            JobStatus
  errorMessage      String?
  metadata          Json? // Store processing metrics, timestamps, video duration, etc.
  creditsUsed       Int       @default(5)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum JobStatus {
  PENDING
  QUEUED
  PROCESSING
  COMPLETED
  FAILED
}

// Blog
model BlogPost {
  id               String         @id @default(uuid())
  authorId         String
  title            String
  slug             String         @unique
  content          String         @db.Text
  excerpt          String?
  featuredImageUrl String?
  status           BlogPostStatus @default(DRAFT)
  publishedAt      DateTime?
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  // Relations
  author     User               @relation(fields: [authorId], references: [id])
  categories BlogPostCategory[]
  tags       BlogPostTag[]
  comments   BlogComment[]

  scheduledFor    DateTime? // If status is SCHEDULED
  metaTitle       String?
  metaDescription String?
  metaKeywords    String[]  @default([])
}

enum BlogPostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  SCHEDULED
}

model BlogCategory {
  id          String  @id @default(uuid())
  name        String  @unique
  slug        String  @unique
  description String?

  // Relations
  posts BlogPostCategory[]
}

model BlogPostCategory {
  postId     String
  categoryId String

  // Relations
  post     BlogPost     @relation(fields: [postId], references: [id], onDelete: Cascade)
  category BlogCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([postId, categoryId])
}

model BlogTag {
  id   String @id @default(uuid())
  name String @unique
  slug String @unique

  // Relations
  posts BlogPostTag[]
}

model BlogPostTag {
  postId String
  tagId  String

  // Relations
  post BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag  BlogTag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([postId, tagId])
}

model BlogComment {
  id          String   @id @default(uuid())
  postId      String
  authorName  String
  authorEmail String?
  content     String   @db.Text
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  post    BlogPost      @relation(fields: [postId], references: [id], onDelete: Cascade)
  parent  BlogComment?  @relation("ThreadedComments", fields: [parentId], references: [id], onDelete: NoAction)
  replies BlogComment[] @relation("ThreadedComments")
}

// Analytics and Logging
model ApiUsageLog {
  id                String   @id @default(uuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id])
  featureUsed       String
  creditsConsumed   Float    @default(0)
  requestTimestamp  DateTime
  responseTimestamp DateTime
  status            String   @default("success")
  errorDetails      String?
  metadata          Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model AnalyticsEvent {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  eventType String
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum FeatureType {
  VIDEO_GENERATION
  IMAGE_GENERATION
  BACKGROUND_REMOVAL
  VIRTUAL_TRY_ON
  SPEED_PAINTING
}

// Site Settings
model SiteSetting {
  id    String @id @default(uuid())
  key   String @unique
  value String @db.Text
}

// Webhook Processing
model WebhookEvent {
  eventId           String   @id
  eventType         String
  processed         Boolean  @default(true)
  processingDetails String?
  processedAt       DateTime @default(now())
  createdAt         DateTime @default(now())
}

// Virtual Try-On
model VirtualTryOn {
  id                    String           @id @default(uuid())
  userId                String
  sessionId             String? // For tracking multi-step sessions
  mode                  VirtualTryOnMode @default(SINGLE)
  humanImagePath        String // Path to the human/model image
  clothImagePath        String // Path to the top/single clothing item
  bottomClothImagePath  String? // Path to the bottom clothing item (for TOP_BOTTOM mode)
  resultImagePath       String? // Path to the processed result image
  status                JobStatus        @default(PENDING)
  processingStartedAt   DateTime? // When processing began
  processingCompletedAt DateTime? // When processing finished
  creditsUsed           Int              @default(3) // Default credits for virtual try-on
  externalLink          String? // Original URL from the API
  qualityScore          Float? // AI-generated quality assessment (0-1)
  userRating            Int? // User feedback (1-5)
  errorMessage          String?
  metadata              Json? // Processing parameters, garment types, etc.
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt
  modelImageId          String? // Reference to the model image used

  // Relations
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  modelImage   ModelImage?   @relation(fields: [modelImageId], references: [id])
  recentTryons RecentTryOn[]
}

// Model images for virtual try-on
model ModelImage {
  id         String   @id @default(uuid())
  userId     String // User who uploaded the model
  imagePath  String // Local file path
  isDefault  Boolean  @default(false) // System-provided models
  modelName  String? // Name of the model
  gender     String? // Gender of the model (MALE, FEMALE, UNISEX)
  bodyType   String? // Body type of the model
  poseType   String? // Pose type of the model
  ethnicity  String? // Ethnicity of the model
  role       UserRole @default(ADMIN) // User vs admin
  usageCount Int      @default(0) // Track usage for popularity
  isActive   Boolean  @default(true) // Is this model active/available
  imageUrl   String? // URL of the model image
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  virtualTryOns VirtualTryOn[]
}

// Recent try-ons for quick access
model RecentTryOn {
  id         String   @id @default(uuid())
  userId     String
  tryonId    String
  accessedAt DateTime @default(now())
  isFavorite Boolean  @default(false)

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  virtualTryOn VirtualTryOn @relation(fields: [tryonId], references: [id], onDelete: Cascade)

  @@unique([userId, tryonId])
}

// Clothing item catalog
model ClothingItem {
  id           String       @id @default(uuid())
  userId       String
  name         String       @default("Clothing Item")
  imagePath    String
  imageUrl     String?
  clothingType ClothingType
  category     String? // e.g., "shirt", "pants", "dress", etc.
  color        String?
  style        String?
  season       String?
  role         UserRole     @default(ADMIN)
  description  String?
  isDefault    Boolean      @default(false) // Admin vs user items
  metadata     Json?        @default("{}") // Store detection metadata
  usageCount   Int          @default(0)
  createdAt    DateTime     @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum VirtualTryOnMode {
  SINGLE // For single clothing item
  TOP_BOTTOM // For top and bottom combination
}

enum ClothingType {
  TOP
  BOTTOM
  FULL_SET
}

enum Gender {
  MALE
  FEMALE
  UNISEX
}
