{"name": "miragicai-backend", "version": "1.0.0", "description": "Backend API for Miragic-AI SaaS platform", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "lint": "npx eslint . --ext .ts", "setup:speedpainting": "node scripts/setup-speed-painting-dirs.js", "seed:plans": "npx ts-node src/scripts/seed-plans.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["saas", "ai", "video-generation", "image-generation", "background-removal"], "author": "", "license": "ISC", "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "@paypal/paypal-js": "^8.2.0", "@paypal/paypal-server-sdk": "^1.0.0", "@prisma/client": "^6.8.2", "@tensorflow/tfjs-node": "^4.22.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bullmq": "^5.52.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "form-data": "^4.0.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^4.1.0", "prisma": "^6.8.2", "rate-limiter-flexible": "^7.1.1", "raw-body": "^3.0.0", "sharp": "^0.34.1", "slugify": "^1.6.6", "stripe": "^18.1.0", "winston": "^3.17.0", "zod": "^3.24.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.30", "@types/uuid": "^10.0.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}