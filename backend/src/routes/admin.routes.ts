import { Router } from "express";
import {
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser,
  createTag,
  getAnalyticsSummary,
  updateSettings,
  getSettings,
  getRevenueAnalytics,
  getUsageAnalytics,
  getUserAnalytics,
  getAdminStats,
  getRecentUsers,
  getRecentJobs,
  getPaymentConfig,
  updatePaymentConfig,
  getSubscriptionPlanById,
  getAllTags,
} from "../controllers/admin.controller";
import {
  createBlogPostAdmin,
  updateBlogPostAdmin,
  deleteBlogPostAdmin,
  getBlogPostByIdAdmin,
  getAllBlogPostsAdminController,
  createBlogCategoryAdmin,
  updateBlogCategoryAdmin,
  deleteBlogCategoryAdmin,
  getAllBlogCategoriesAdminController,
  upload,
} from "../controllers/blog.controller";
import { AdminPlansController } from "../controllers/admin/plans.controller";

const adminPlansController = new AdminPlansController();
// Import subscription plan controllers
import {
  getAllSubscriptionPlans,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
} from "../controllers/admin.subscription.controller";

// Import credit package controllers
import {
  getAllCreditPackages,
  createCreditPackage,
  updateCreditPackage,
  deleteCreditPackage,
} from "../controllers/admin.credit.controller";
import { authenticate, authorizeAdmin } from "../middleware/auth";
import { getSpeedPaintingAnalytics } from "../controllers/admin/speedPaintingAnalytics.controller";

const router = Router();

// Apply authentication and admin authorization middleware to all admin routes
router.use(authenticate as any);
router.use(authorizeAdmin as any);

// User management routes
router.get("/users", getAllUsers as any);
router.get("/users/:userId", getUserById as any);
router.put("/users/:userId", updateUser as any);
router.delete("/users/:userId", deleteUser as any);

// Blog management routes
router.get("/blog/posts/all", getAllBlogPostsAdminController as any);
router.post("/blog/posts", upload.single("featuredImage"), createBlogPostAdmin as any);
router.put("/blog/posts/:postId", upload.single("featuredImage"), updateBlogPostAdmin as any);
router.delete("/blog/posts/:postId", deleteBlogPostAdmin as any);
router.get("/blog/posts/:postId", getBlogPostByIdAdmin as any);

// Category and tag management routes
router.post("/blog/categories", createBlogCategoryAdmin as any);
router.put("/blog/categories/:categoryId", updateBlogCategoryAdmin as any);
router.delete("/blog/categories/:categoryId", deleteBlogCategoryAdmin as any);
router.post("/blog/tags", createTag as any);
router.get("/blog/tags", getAllTags as any);
router.get("/blog/categories/all", getAllBlogCategoriesAdminController as any);

// Analytics routes
router.get("/analytics/summary", getAnalyticsSummary as any);
router.get("/analytics/revenue", getRevenueAnalytics as any);
router.get("/analytics/usage", getUsageAnalytics as any);
router.get("/analytics/users", getUserAnalytics as any);
router.get("/analytics/speed-painting", getSpeedPaintingAnalytics as any);

// Dashboard stats
router.get("/stats", getAdminStats as any);
router.get("/recent-users", getRecentUsers as any);
router.get("/recent-jobs", getRecentJobs as any);

// Settings routes
router.get("/settings", getSettings as any);
router.put("/settings", updateSettings as any);

// Payment configuration
router.get("/payment-config", getPaymentConfig as any);
router.put("/payment-config", updatePaymentConfig as any);

// Subscription plan management
router.get("/subscription-plans", getAllSubscriptionPlans as any);
router.get("/subscription-plans/:planId", getSubscriptionPlanById as any);
router.post("/subscription-plans", createSubscriptionPlan as any);
router.put("/subscription-plans/:id", updateSubscriptionPlan as any);
router.delete("/subscription-plans/:id", deleteSubscriptionPlan as any);

// Credit package management
router.get("/credit-packages", getAllCreditPackages as any);
router.post("/credit-packages", createCreditPackage as any);
router.put("/credit-packages/:id", updateCreditPackage as any);
router.delete("/credit-packages/:id", deleteCreditPackage as any);

// Service costs routes
router.get("/service-costs", adminPlansController.getServiceCosts as any);
router.put("/service-costs", adminPlansController.updateServiceCosts as any);

// Virtual Try-On Admin routes
import * as virtualTryOnAdminController from "../controllers/admin.virtualTryOn.controller";

// POST /api/v1/admin/virtual-try-on/models
// Upload admin model images (default models)
router.post(
  "/virtual-try-on/models",
  virtualTryOnAdminController.uploadFiles,
  virtualTryOnAdminController.uploadAdminModelImage as any
);

// POST /api/v1/admin/virtual-try-on/clothing
// Upload admin clothing items (default clothing)
router.post(
  "/virtual-try-on/clothing",
  virtualTryOnAdminController.uploadFiles,
  virtualTryOnAdminController.uploadAdminClothingItem as any
);

// GET /api/v1/admin/virtual-try-on/models
// Get all model images (admin view)
router.get(
  "/virtual-try-on/models",
  virtualTryOnAdminController.getAllModelImages as any
);

// GET /api/v1/admin/virtual-try-on/clothing
// Get all clothing items (admin view)
router.get(
  "/virtual-try-on/clothing",
  virtualTryOnAdminController.getAllClothingItems as any
);

// DELETE /api/v1/admin/virtual-try-on/models/:id
// Delete any model image (admin only)
router.delete(
  "/virtual-try-on/models/:id",
  virtualTryOnAdminController.deleteAnyModelImage as any
);

// DELETE /api/v1/admin/virtual-try-on/clothing/:id
// Delete any clothing item (admin only)
router.delete(
  "/virtual-try-on/clothing/:id",
  virtualTryOnAdminController.deleteAnyClothingItem as any
);

// PATCH /api/v1/admin/virtual-try-on/models/:id
// Update model image properties
router.patch(
  "/virtual-try-on/models/:id",
  virtualTryOnAdminController.updateModelImage as any
);

// PATCH /api/v1/admin/virtual-try-on/clothing/:id
// Update clothing item properties
router.patch(
  "/virtual-try-on/clothing/:id",
  virtualTryOnAdminController.updateClothingItem as any
);

// GET /api/v1/admin/virtual-try-on/stats
// Get virtual try-on statistics
router.get(
  "/virtual-try-on/stats",
  virtualTryOnAdminController.getVirtualTryOnStats as any
);

export default router;
