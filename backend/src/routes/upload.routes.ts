import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { authenticate } from '../middleware/auth';
import { Request, Response } from 'express';

const router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../uploads'));
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max file size
  },
  fileFilter: (req, file, cb) => {
    // Accept only images
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Apply authentication middleware to all upload routes
router.use(authenticate as any);

// Define the upload handler function separately
const handleFileUpload = (req: Request, res: Response): void => {
  try {
    if (!req.file) {
      res.status(400).json({
        success: false,
        error: {
          code: 'NO_FILE',
          message: 'No file uploaded',
        },
      });
      return;
    }

    // Get the file type from the request (image or background)
    const fileType = req.body.type || 'image';
    
    // In a real implementation, you would upload to a cloud storage like AWS S3
    // For now, we'll just return the local path
    const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
    const fileUrl = `${baseUrl}/uploads/${req.file.filename}`;

    res.status(200).json({
      success: true,
      data: {
        url: fileUrl,
        type: fileType,
        filename: req.file.filename,
        size: req.file.size,
      },
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'UPLOAD_ERROR',
        message: error instanceof Error ? error.message : 'Failed to upload file',
      },
    });
  }
};

// File upload endpoint
router.post('/', upload.single('file'), handleFileUpload);

// Define multiple file upload handler
const handleMultipleFileUpload = (req: Request, res: Response): void => {
  try {
    if (!req.files) {
      res.status(400).json({
        success: false,
        error: {
          code: 'NO_FILES',
          message: 'No files uploaded',
        },
      });
      return;
    }

    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
    
    // Process uploaded files
    const fileUrls: Record<string, string> = {};
    
    // Check if garm_image exists
    if (files.garm_image && files.garm_image.length > 0) {
      fileUrls.garmentImagePath = `/uploads/${files.garm_image[0].filename}`;
    }
    
    // Check if human_image exists
    if (files.human_image && files.human_image.length > 0) {
      fileUrls.humanImagePath = `/uploads/${files.human_image[0].filename}`;
    }
    
    res.status(200).json({
      success: true,
      data: fileUrls,
    });
  } catch (error) {
    console.error('Multiple upload error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'UPLOAD_ERROR',
        message: error instanceof Error ? error.message : 'Failed to upload files',
      },
    });
  }
};

// Multiple file upload endpoint
router.post('/multiple', upload.array('files', 10), (req, res) => {
  // This is a workaround for the array upload
  // We'll use fields in the actual implementation
  res.status(400).json({
    success: false,
    error: {
      code: 'INVALID_REQUEST',
      message: 'Use fields endpoint for multiple file types',
    },
  });
});

// Multiple file upload with different field names
router.post('/multiple-fields', upload.fields([
  { name: 'garm_image', maxCount: 1 },
  { name: 'human_image', maxCount: 1 }
]), handleMultipleFileUpload);

export default router;
