import { Router } from 'express';
import {
  getCurrentUser,
  updateProfile,
  updatePassword,
  getCreditTransactions,
  uploadProfileImage,
} from '../controllers/user.controller';
import { authenticate } from '../middleware/auth';
import { upload } from '../middleware/upload';

const router = Router();

// Apply authentication middleware to all user routes
router.use(authenticate as any);

// User routes
router.get('/me', getCurrentUser as any);
router.put('/me', updateProfile as any);
router.put('/me/password', updatePassword as any);
router.get('/credits/transactions', getCreditTransactions as any);
router.post('/me/avatar', upload.single('profileImage'), uploadProfileImage as any);

export default router;
