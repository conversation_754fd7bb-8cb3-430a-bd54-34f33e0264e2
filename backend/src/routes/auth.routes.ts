import { Router } from "express";
import {
  register,
  login,
  refreshToken,
  logout,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
} from "../controllers/auth.controller";
import {
  googleAuth,
  googleCallback,
  appleAuth,
} from "../controllers/oauth.controller";

const router = Router();

// Standard auth routes
router.post("/register", register as any);
router.post("/login", login as any);
router.post("/refresh-token", refreshToken as any);
router.post("/logout", logout as any);
router.post("/request-password-reset", requestPasswordReset as any);
router.post("/reset-password", resetPassword as any);
router.get("/verify-email/:token", verifyEmail as any);
router.post("/resend-verification", resendVerificationEmail as any);

// OAuth routes
router.get("/google", googleAuth);
router.get("/google/callback", googleCallback);
router.post("/apple", appleAuth as any);

export default router;
