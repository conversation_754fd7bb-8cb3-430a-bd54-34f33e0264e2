import { Router } from 'express';
import {
  register,
  login,
  refreshToken,
  logout,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
} from '../controllers/auth.controller';

const router = Router();

// Auth routes
router.post('/register', register as any);
router.post('/login', login as any);
router.post('/refresh-token', refreshToken as any);
router.post('/logout', logout as any);
router.post('/request-password-reset', requestPasswordReset as any);
router.post('/reset-password', resetPassword as any);
router.get('/verify-email/:token', verifyEmail as any);

export default router;
