// Configuration file for Miragic-AI backend
// Loads environment variables and provides typed access to configuration

// Environment detection
export const NODE_ENV = process.env.NODE_ENV || "development";
export const IS_PRODUCTION = NODE_ENV === "production";
export const IS_DEVELOPMENT = NODE_ENV === "development";
export const IS_TEST = NODE_ENV === "test";

// Server configuration
export const PORT = process.env.PORT ? parseInt(process.env.PORT, 10) : 5000;
export const APP_URL = process.env.APP_URL || "http://localhost:5000";
export const FRONTEND_URL = process.env.FRONTEND_URL || "http://localhost:5173";

// Database configuration
export const DATABASE_URL = process.env.DATABASE_URL;

// JWT configuration
export const JWT_SECRET =
  process.env.JWT_SECRET || "your-secret-key-for-development-only";
export const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";
export const JWT_REFRESH_SECRET =
  process.env.JWT_REFRESH_SECRET ||
  "your-refresh-secret-key-for-development-only";
export const JWT_REFRESH_EXPIRES_IN =
  process.env.JWT_REFRESH_EXPIRES_IN || "30d";

// Stripe configuration
export const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY || "";
export const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET || "";
export const STRIPE_PUBLISHABLE_KEY = process.env.STRIPE_PUBLISHABLE_KEY || "";

// PayPal configuration
export const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID || "";
export const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET || "";
export const PAYPAL_MODE = IS_PRODUCTION ? "live" : "sandbox";

// Email configuration (Resend)
export const RESEND_API_KEY = process.env.RESEND_API_KEY || "";
export const EMAIL_FROM = process.env.EMAIL_FROM || "<EMAIL>";

// AI Service API Keys
export const STABILITY_API_KEY = process.env.STABILITY_API_KEY || "";
export const SYNTHESIA_API_KEY = process.env.SYNTHESIA_API_KEY || "";
export const REMOVAL_AI_API_KEY = process.env.REMOVAL_AI_API_KEY || "";

// Logging configuration
export const LOG_LEVEL = process.env.LOG_LEVEL || "info";

// Security configuration
export const CORS_ORIGIN = process.env.CORS_ORIGIN || "*";
export const RATE_LIMIT_WINDOW_MS = process.env.RATE_LIMIT_WINDOW_MS
  ? parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10)
  : 15 * 60 * 1000; // 15 minutes
export const RATE_LIMIT_MAX = process.env.RATE_LIMIT_MAX
  ? parseInt(process.env.RATE_LIMIT_MAX, 10)
  : 100; // 100 requests per window

// Validate required configuration in production
if (IS_PRODUCTION) {
  const requiredEnvVars = [
    "DATABASE_URL",
    "JWT_SECRET",
    "STRIPE_SECRET_KEY",
    "STRIPE_WEBHOOK_SECRET",
    "STRIPE_PUBLISHABLE_KEY",
    "PAYPAL_CLIENT_ID",
    "PAYPAL_CLIENT_SECRET",
    "RESEND_API_KEY",
    "STABILITY_API_KEY",
    "SYNTHESIA_API_KEY",
    "REMOVAL_AI_API_KEY",
  ];

  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar]
  );

  if (missingEnvVars.length > 0) {
    console.warn(
      `⚠️ Missing required environment variables in production: ${missingEnvVars.join(
        ", "
      )}`
    );
  }
}
