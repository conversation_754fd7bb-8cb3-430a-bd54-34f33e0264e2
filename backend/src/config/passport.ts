import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { prisma } from "../index";
import { generateTokens } from "../utils/jwt";

// Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
      callbackURL:
        process.env.GOOGLE_CALLBACK_URL || "/api/v1/auth/google/callback",
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        console.log("Google OAuth profile:", profile);

        // Check if user already exists with this Google account
        let oauthAccount = await prisma.oAuthAccount.findUnique({
          where: {
            provider_providerId: {
              provider: "google",
              providerId: profile.id,
            },
          },
          include: {
            user: {
              include: {
                profile: true,
                credit: true,
              },
            },
          },
        });

        if (oauthAccount) {
          // Update OAuth account with latest tokens
          await prisma.oAuthAccount.update({
            where: { id: oauthAccount.id },
            data: {
              accessToken,
              refreshToken,
              expiresAt: refreshToken ? new Date(Date.now() + 3600000) : null, // 1 hour
            },
          });

          // Update last login
          await prisma.user.update({
            where: { id: oauthAccount.user.id },
            data: { lastLogin: new Date() },
          });

          return done(null, oauthAccount.user);
        }

        // Check if user exists with the same email
        const email = profile.emails?.[0]?.value;
        if (!email) {
          return done(new Error("No email found in Google profile"), false);
        }

        let user = await prisma.user.findUnique({
          where: { email },
          include: {
            profile: true,
            credit: true,
          },
        });

        if (user) {
          // Link existing user account with Google OAuth
          await prisma.oAuthAccount.create({
            data: {
              userId: user.id,
              provider: "google",
              providerId: profile.id,
              email,
              name: profile.displayName,
              picture: profile.photos?.[0]?.value,
              accessToken,
              refreshToken,
              expiresAt: refreshToken ? new Date(Date.now() + 3600000) : null,
            },
          });

          // Update user as email verified since Google verified it
          await prisma.user.update({
            where: { id: user.id },
            data: {
              emailVerified: true,
              lastLogin: new Date(),
            },
          });

          return done(null, user);
        }

        // Create new user with Google OAuth
        const firstName = profile.name?.givenName || "";
        const lastName = profile.name?.familyName || "";

        user = await prisma.user.create({
          data: {
            email,
            emailVerified: true, // Google accounts are pre-verified
            profile: {
              create: {
                firstName,
                lastName,
                avatarUrl: profile.photos?.[0]?.value,
              },
            },
            credit: {
              create: {
                balance: 20, // Welcome credits
              },
            },
            oauthAccounts: {
              create: {
                provider: "google",
                providerId: profile.id,
                email,
                name: profile.displayName,
                picture: profile.photos?.[0]?.value,
                accessToken,
                refreshToken,
                expiresAt: refreshToken ? new Date(Date.now() + 3600000) : null,
              },
            },
          },
          include: {
            profile: true,
            credit: true,
          },
        });

        return done(null, user);
      } catch (error) {
        console.error("Google OAuth error:", error);
        return done(error, false);
      }
    }
  )
);

// Serialize user for session
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: true,
        credit: true,
      },
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

export default passport;
