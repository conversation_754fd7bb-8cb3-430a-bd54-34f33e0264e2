import request from 'supertest';
import { app } from '../index';
import { prisma } from '../index';
import bcrypt from 'bcryptjs';

describe('Authentication System', () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.oAuthAccount.deleteMany();
    await prisma.authToken.deleteMany();
    await prisma.user.deleteMany();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('POST /api/v1/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.emailVerified).toBe(false);
      expect(response.body.data.tokens).toBeDefined();
      expect(response.body.data.tokens.accessToken).toBeDefined();
      expect(response.body.data.tokens.refreshToken).toBeDefined();
    });

    it('should not register user with existing email', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
      };

      // Create user first
      await request(app)
        .post('/api/v1/auth/register')
        .send(userData);

      // Try to register again
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('EMAIL_ALREADY_EXISTS');
    });
  });

  describe('POST /api/v1/auth/login', () => {
    beforeEach(async () => {
      // Create a test user
      const hashedPassword = await bcrypt.hash('password123', 10);
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          emailVerified: true,
          profile: {
            create: {
              firstName: 'John',
              lastName: 'Doe',
            },
          },
          credit: {
            create: {
              balance: 20,
            },
          },
        },
      });
    });

    it('should login successfully with correct credentials', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.tokens).toBeDefined();
    });

    it('should not login with incorrect password', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
    });
  });

  describe('POST /api/v1/auth/request-password-reset', () => {
    beforeEach(async () => {
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          profile: {
            create: {
              firstName: 'John',
              lastName: 'Doe',
            },
          },
          credit: {
            create: {
              balance: 20,
            },
          },
        },
      });
    });

    it('should send password reset email for existing user', async () => {
      const response = await request(app)
        .post('/api/v1/auth/request-password-reset')
        .send({
          email: '<EMAIL>',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('reset email sent');

      // Check that reset token was stored
      const user = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      expect(user?.passwordResetToken).toBeDefined();
      expect(user?.passwordResetTokenExpiresAt).toBeDefined();
    });

    it('should return success even for non-existent email (security)', async () => {
      const response = await request(app)
        .post('/api/v1/auth/request-password-reset')
        .send({
          email: '<EMAIL>',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('POST /api/v1/auth/resend-verification', () => {
    beforeEach(async () => {
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: false,
          profile: {
            create: {
              firstName: 'John',
              lastName: 'Doe',
            },
          },
          credit: {
            create: {
              balance: 20,
            },
          },
        },
      });
    });

    it('should resend verification email for unverified user', async () => {
      const response = await request(app)
        .post('/api/v1/auth/resend-verification')
        .send({
          email: '<EMAIL>',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Verification email sent');

      // Check that verification token was updated
      const user = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      expect(user?.emailVerificationToken).toBeDefined();
      expect(user?.emailVerificationTokenExpiresAt).toBeDefined();
    });

    it('should not resend verification for already verified user', async () => {
      // Update user to verified
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { emailVerified: true },
      });

      const response = await request(app)
        .post('/api/v1/auth/resend-verification')
        .send({
          email: '<EMAIL>',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('EMAIL_ALREADY_VERIFIED');
    });
  });

  describe('POST /api/v1/auth/apple', () => {
    it('should handle Apple Sign-In with new user', async () => {
      // Mock Apple ID token (simplified for testing)
      const mockIdToken = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************.mock_signature';
      
      const response = await request(app)
        .post('/api/v1/auth/apple')
        .send({
          id_token: mockIdToken,
          user: JSON.stringify({
            name: {
              firstName: 'John',
              lastName: 'Doe',
            },
          }),
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.user.emailVerified).toBe(true);
      expect(response.body.data.tokens).toBeDefined();

      // Check OAuth account was created
      const oauthAccount = await prisma.oAuthAccount.findFirst({
        where: {
          provider: 'apple',
          providerId: '**********',
        },
      });
      expect(oauthAccount).toBeDefined();
    });
  });
});
