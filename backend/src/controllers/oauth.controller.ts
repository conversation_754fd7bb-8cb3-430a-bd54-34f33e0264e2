import { Request, Response, NextFunction } from "express";
import passport from "../config/passport";
import { generateTokens } from "../utils/jwt";
import crypto from "crypto";
import { prisma } from "../index";

// Google OAuth initiation
export const googleAuth = passport.authenticate("google", {
  scope: ["profile", "email"],
});

// Google OAuth callback
export const googleCallback = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  passport.authenticate(
    "google",
    { session: false },
    async (err: any, user: any) => {
      try {
        if (err) {
          console.error("Google OAuth error:", err);
          return res.redirect(
            `${process.env.FRONTEND_URL}/?error=oauth_error&message=${encodeURIComponent(err.message)}`
          );
        }

        if (!user) {
          return res.redirect(
            `${process.env.FRONTEND_URL}/?error=oauth_failed&message=Authentication failed`
          );
        }

        // Generate JWT tokens
        const { accessToken, refreshToken } = generateTokens(user);

        // Store refresh token
        const refreshTokenHash = crypto
          .createHash("sha256")
          .update(refreshToken)
          .digest("hex");

        await prisma.authToken.create({
          data: {
            userId: user.id,
            tokenHash: refreshTokenHash,
            expiresAt: new Date(
              Date.now() +
                parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********") // Default 7 days
            ),
          },
        });

        // Redirect to frontend with tokens
        const redirectUrl =
          `${process.env.FRONTEND_URL}/auth/callback?` +
          `access_token=${accessToken}&` +
          `refresh_token=${refreshToken}&` +
          `user_id=${user.id}`;

        res.redirect(redirectUrl);
      } catch (error) {
        console.error("OAuth callback error:", error);
        res.redirect(
          `${process.env.FRONTEND_URL}/?error=oauth_error&message=Authentication failed`
        );
      }
    }
  )(req, res, next);
};

// Apple Sign-In (Web-based implementation)
export const appleAuth = async (req: Request, res: Response) => {
  try {
    const { id_token, user } = req.body;

    if (!id_token) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_ID_TOKEN",
          message: "Apple ID token is required",
        },
      });
    }

    // Verify Apple ID token (simplified - in production, use proper JWT verification)
    // For now, we'll trust the frontend verification
    const appleUserData = user ? JSON.parse(user) : null;

    // Extract email from ID token payload (in production, properly decode and verify)
    const tokenParts = id_token.split(".");
    if (tokenParts.length !== 3) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_TOKEN",
          message: "Invalid Apple ID token format",
        },
      });
    }

    const payload = JSON.parse(Buffer.from(tokenParts[1], "base64").toString());
    const email = payload.email;
    const appleUserId = payload.sub;

    if (!email || !appleUserId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_TOKEN_DATA",
          message: "Email or user ID not found in token",
        },
      });
    }

    // Check if user already exists with this Apple account
    let oauthAccount = await prisma.oAuthAccount.findUnique({
      where: {
        provider_providerId: {
          provider: "apple",
          providerId: appleUserId,
        },
      },
      include: {
        user: {
          include: {
            profile: true,
            credit: true,
          },
        },
      },
    });

    if (oauthAccount) {
      // Update last login
      await prisma.user.update({
        where: { id: oauthAccount.user.id },
        data: { lastLogin: new Date() },
      });

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(oauthAccount.user);

      // Store refresh token
      const refreshTokenHash = crypto
        .createHash("sha256")
        .update(refreshToken)
        .digest("hex");

      await prisma.authToken.create({
        data: {
          userId: oauthAccount.user.id,
          tokenHash: refreshTokenHash,
          expiresAt: new Date(
            Date.now() +
              parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********")
          ),
        },
      });

      return res.status(200).json({
        success: true,
        data: {
          user: {
            id: oauthAccount.user.id,
            email: oauthAccount.user.email,
            role: oauthAccount.user.role,
            emailVerified: oauthAccount.user.emailVerified,
            profile: oauthAccount.user.profile,
            credit: oauthAccount.user.credit,
          },
          tokens: {
            accessToken,
            refreshToken,
          },
        },
      });
    }

    // Check if user exists with the same email
    let existingUser = await prisma.user.findUnique({
      where: { email },
      include: {
        profile: true,
        credit: true,
      },
    });

    if (existingUser) {
      // Link existing user account with Apple OAuth
      await prisma.oAuthAccount.create({
        data: {
          userId: existingUser.id,
          provider: "apple",
          providerId: appleUserId,
          email,
          name: appleUserData?.name
            ? `${appleUserData.name.firstName} ${appleUserData.name.lastName}`
            : null,
        },
      });

      // Update user as email verified
      await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          emailVerified: true,
          lastLogin: new Date(),
        },
      });
    } else {
      // Create new user with Apple OAuth
      const firstName = appleUserData?.name?.firstName || "";
      const lastName = appleUserData?.name?.lastName || "";

      existingUser = await prisma.user.create({
        data: {
          email,
          emailVerified: true, // Apple accounts are pre-verified
          profile: {
            create: {
              firstName,
              lastName,
            },
          },
          credit: {
            create: {
              balance: 20, // Welcome credits
            },
          },
          oauthAccounts: {
            create: {
              provider: "apple",
              providerId: appleUserId,
              email,
              name: appleUserData?.name ? `${firstName} ${lastName}` : null,
            },
          },
        },
        include: {
          profile: true,
          credit: true,
        },
      });
    }

    if (!existingUser) {
      return res.status(500).json({
        success: false,
        error: {
          code: "USER_CREATION_FAILED",
          message: "Failed to create or find user",
        },
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(existingUser);

    // Store refresh token
    const refreshTokenHash = crypto
      .createHash("sha256")
      .update(refreshToken)
      .digest("hex");

    await prisma.authToken.create({
      data: {
        userId: existingUser.id,
        tokenHash: refreshTokenHash,
        expiresAt: new Date(
          Date.now() +
            parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********")
        ),
      },
    });

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: existingUser.id,
          email: existingUser.email,
          role: existingUser.role,
          emailVerified: existingUser.emailVerified,
          profile: existingUser.profile,
          credit: existingUser.credit,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error) {
    console.error("Apple OAuth error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "OAUTH_ERROR",
        message: "Apple authentication failed",
      },
    });
  }
};
