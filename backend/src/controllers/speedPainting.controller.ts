import { Request, Response } from "express";
import { PrismaClient, JobStatus } from "@prisma/client";
import multer from "multer";
import path from "path";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";
import { AppError } from "../utils/error";
import SpeedPaintingService from "../services/speedPainting.service";
import { CreditUsageService } from "../services/creditUsage.service";
import { RateLimiterMemory } from "rate-limiter-flexible";
import { speedPaintingLogger } from "../utils/logging";
import { APP_URL } from "../config";

const prisma = new PrismaClient();

// Rate limiter setup - 10 requests per hour per IP
const rateLimiter = new RateLimiterMemory({
  points: 10,
  duration: 60 * 60, // 1 hour
});

// Rate limiter middleware
export const rateLimitMiddleware = async (
  req: Request,
  res: Response,
  next: any
) => {
  try {
    // Get client IP
    const clientIp = req.ip || req.headers["x-forwarded-for"] || "unknown";

    // Check rate limit
    await rateLimiter.consume(clientIp.toString());
    next();
  } catch (error) {
    res.status(429).json({
      success: false,
      error: {
        code: "RATE_LIMIT_EXCEEDED",
        message: "Too many requests, please try again later.",
      },
    });
  }
};

// Initialize credit usage service
const creditUsageService = new CreditUsageService();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const userId = req.user?.id || "anonymous";
    const uploadDir = path.join(
      __dirname,
      "../../uploads/speedpainting/original",
      userId
    );

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
    const extension = path.extname(file.originalname);
    cb(null, `${uniqueSuffix}${extension}`);
  },
});

// File filter to validate image files
const fileFilter = (req: any, file: Express.Multer.File, cb: any) => {
  // Accept only image files
  if (
    file.mimetype === "image/jpeg" ||
    file.mimetype === "image/png" ||
    file.mimetype === "image/webp" ||
    file.mimetype === "image/gif"
  ) {
    cb(null, true);
  } else {
    cb(
      new AppError(
        "Invalid file type. Only JPEG, PNG, WEBP, and GIF images are allowed.",
        400
      ),
      false
    );
  }
};

// Configure multer upload
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB max file size
  },
});

/**
 * Upload an image and create a speed painting job
 */
export const createSpeedPaintingJob = async (req: Request, res: Response) => {
  try {
    // Authentication check
    if (!req.user) {
      speedPaintingLogger.warn({
        message: "Unauthorized attempt to create speed painting job",
        ip: req.ip,
        action: "unauthorized_access",
      });

      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    speedPaintingLogger.info({
      message: "Speed painting job creation initiated",
      userId: req.user.id,
      action: "job_creation_initiated",
    });

    // File validation
    if (!req.file) {
      speedPaintingLogger.warn({
        message: "Missing file in speed painting job creation",
        userId: req.user.id,
        action: "validation_error",
      });

      return res.status(400).json({
        success: false,
        error: {
          code: "FILE_REQUIRED",
          message: "Image file is required",
        },
      });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { credit: true },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Check if user has enough credits for speed painting
    const hasEnoughCredits = await creditUsageService.hasEnoughCredits(
      user.id,
      "speedPainting"
    );

    if (!hasEnoughCredits) {
      speedPaintingLogger.info({
        message: "Insufficient credits for speed painting",
        userId: user.id,
        credits: user.credit?.balance || 0,
        action: "insufficient_credits",
      });

      return res.status(402).json({
        success: false,
        error: {
          code: "INSUFFICIENT_CREDITS",
          message:
            "You don't have enough credits for this operation. Please purchase more credits.",
        },
      });
    }

    // Generate image URL
    const originalImageUrl = `${APP_URL}/uploads/speedpainting/original/${user.id}/${req.file.filename}`;

    // Create speed painting job
    const job = await SpeedPaintingService.createSpeedPaintingJob(
      user.id,
      req.file.path,
      originalImageUrl
    );

    // Return job details
    speedPaintingLogger.info({
      message: "Speed painting job created successfully",
      userId: user.id,
      jobId: job.id,
      action: "job_created",
    });

    return res.status(201).json({
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
        originalImageUrl: job.originalImageUrl,
      },
      message: "Speed painting job created successfully",
    });
  } catch (error) {
    speedPaintingLogger.error({
      message: "Error creating speed painting job",
      userId: req.user?.id,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      action: "job_creation_error",
    });

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    return res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create speed painting job",
      },
    });
  }
};

/**
 * Get a specific speed painting job
 */
export const getSpeedPaintingJob = async (req: Request, res: Response) => {
  try {
    // Authentication check
    if (!req.user) {
      speedPaintingLogger.warn({
        message: "Unauthorized attempt to access speed painting job",
        ip: req.ip,
        jobId: req.params.jobId,
        action: "unauthorized_access",
      });

      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const { jobId } = req.params;

    // Get job
    const job = await SpeedPaintingService.getSpeedPaintingJob(
      jobId,
      req.user.id
    );

    // Return job details
    speedPaintingLogger.info({
      message: "Speed painting job retrieved",
      userId: req.user.id,
      jobId: job.id,
      status: job.status,
      action: "job_retrieved",
    });

    return res.status(200).json({
      success: true,
      data: job,
    });
  } catch (error) {
    console.error("Error getting speed painting job:", error);

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    return res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get speed painting job",
      },
    });
  }
};

/**
 * Get all speed painting jobs for the current user
 */
export const getUserSpeedPaintingJobs = async (req: Request, res: Response) => {
  try {
    // Authentication check
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get jobs
    const jobs = await SpeedPaintingService.getUserSpeedPaintingJobs(
      req.user.id
    );

    // Return jobs
    return res.status(200).json({
      success: true,
      data: {
        jobs,
        count: jobs.length,
      },
    });
  } catch (error) {
    console.error("Error getting user speed painting jobs:", error);

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    return res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user speed painting jobs",
      },
    });
  }
};

/**
 * Get the queue status for speed painting jobs
 */
export const getQueueStatus = async (req: Request, res: Response) => {
  try {
    // Authentication check
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Count pending and processing jobs
    const pendingCount = await prisma.speedPaintingJob.count({
      where: {
        status: JobStatus.PENDING,
      },
    });

    const processingCount = await prisma.speedPaintingJob.count({
      where: {
        status: JobStatus.PROCESSING,
      },
    });

    // Calculate estimated wait time (rough estimate: 2 minutes per job)
    const estimatedWaitTime = (pendingCount + processingCount) * 2;

    // Return queue status
    return res.status(200).json({
      success: true,
      data: {
        queueLength: pendingCount + processingCount,
        pendingJobs: pendingCount,
        processingJobs: processingCount,
        estimatedWaitTimeMinutes: estimatedWaitTime,
      },
    });
  } catch (error) {
    console.error("Error getting queue status:", error);

    return res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get queue status",
      },
    });
  }
};
