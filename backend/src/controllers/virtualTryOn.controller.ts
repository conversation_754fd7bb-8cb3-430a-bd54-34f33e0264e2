import { Request, Response } from "express";
import { prisma } from "../index";
import { ClothingType, VirtualTryOnMode, JobStatus } from "@prisma/client";
import { AuthenticatedRequest } from "../types/auth.types";
import multer from "multer";
import path from "path";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";

// Import the virtual try-on service
import virtualTryOnService from "../services/virtualTryOn.service";
import * as utils from "../utils/virtualTryOn.utils";

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, "../../uploads/temp");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
    cb(null, `${uniqueSuffix}-${file.originalname}`);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (!file.mimetype.startsWith("image/")) {
      return cb(new Error("Only image files are allowed"));
    }
    cb(null, true);
  },
});

// Middleware to handle file uploads
export const uploadFiles = upload.fields([
  { name: "humanImage", maxCount: 1 },
  { name: "clothImage", maxCount: 1 },
  { name: "bottomClothImage", maxCount: 1 },
  { name: "modelImage", maxCount: 1 },
]);

// Ensure directories exist
utils.ensureDirectories();

/**
 * Process a virtual try-on request
 * @route POST /api/v1/virtual-try-on
 */
export const processVirtualTryOn = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    // Get user ID from authenticated request
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Get uploaded files from multer
    const files = req.files as {
      humanImage?: Express.Multer.File[];
      clothImage?: Express.Multer.File[];
      bottomClothImage?: Express.Multer.File[];
    };

    // Check if required files are uploaded - human image is optional if modelImageId is provided
    if (!files.clothImage) {
      return res.status(400).json({
        success: false,
        message: "Clothing image is required",
      });
    }

    // Human image is optional if modelImageId is provided
    if (!files.humanImage && !req.body.modelImageId) {
      return res.status(400).json({
        success: false,
        message: "Either human image or model ID is required",
      });
    }

    const humanImagePath = files.humanImage?.[0]?.path;
    const clothImagePath = files.clothImage[0].path;
    const bottomClothImagePath = files.bottomClothImage?.[0]?.path;

    // Determine mode based on whether bottom cloth image is provided
    const mode = bottomClothImagePath
      ? VirtualTryOnMode.TOP_BOTTOM
      : VirtualTryOnMode.SINGLE;

    // Get additional parameters from request body
    const { modelImageId, garmentType } = req.body;

    // Handle human image path - either from upload or from model ID
    let finalHumanImagePath: string;

    if (humanImagePath) {
      // Save the human image if it's a new model
      finalHumanImagePath = humanImagePath;
      if (req.body.saveAsModel === "true") {
        finalHumanImagePath = utils.saveHumanImage(
          humanImagePath,
          userId,
          false
        );

        // Create model image in database if requested
        const modelParams = {
          modelName:
            req.body.modelName ||
            `Model ${new Date().toISOString().split("T")[0]}`,
          gender: req.body.gender,
          bodyType: req.body.bodyType,
          poseType: req.body.poseType,
          ethnicity: req.body.ethnicity,
        };

        await virtualTryOnService.createModelImage(
          userId,
          finalHumanImagePath,
          modelParams
        );
      }
    } else if (modelImageId) {
      // Get the model image path from the database
      const modelImage = await prisma.modelImage.findUnique({
        where: { id: modelImageId },
      });

      if (!modelImage) {
        return res.status(400).json({
          success: false,
          message: "Model image not found",
        });
      }

      // Check if user has access to this model (either owns it or it's a default model)
      if (modelImage.userId !== userId && !modelImage.isDefault) {
        return res.status(403).json({
          success: false,
          message: "Access denied to this model image",
        });
      }

      finalHumanImagePath = modelImage.imagePath;
    } else {
      return res.status(400).json({
        success: false,
        message: "Either human image or model ID is required",
      });
    }

    // Save the clothing images
    const finalClothImagePath = utils.saveClothingImage(
      clothImagePath,
      userId,
      bottomClothImagePath ? "TOP" : "DRESS"
    );

    let finalBottomClothImagePath: string | undefined;
    if (bottomClothImagePath) {
      finalBottomClothImagePath = utils.saveClothingImage(
        bottomClothImagePath,
        userId,
        "BOTTOM"
      );
    }

    // Create the job in the database and start processing
    const job = await virtualTryOnService.createJob(userId, {
      humanImagePath: finalHumanImagePath,
      clothImagePath: finalClothImagePath,
      bottomClothImagePath: finalBottomClothImagePath,
      mode,
      modelImageId,
      garmentType,
    });

    // Return the job ID to the client
    return res.status(202).json({
      success: true,
      message: "Virtual try-on job created and processing started",
      data: {
        jobId: job.id,
        status: job.status,
        mode: job.mode,
        createdAt: job.createdAt,
      },
    });
  } catch (error) {
    console.error("Virtual try-on error:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while processing the virtual try-on",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get the status of a virtual try-on job
 * @route GET /api/v1/virtual-try-on/:jobId
 */
export const getVirtualTryOnJob = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const { jobId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Get the job details
    const job = await virtualTryOnService.getJob(jobId, userId);

    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Virtual try-on job not found",
      });
    }

    // Add to recent try-ons if the job is completed
    if (job.status === JobStatus.COMPLETED) {
      try {
        await prisma.recentTryOn.upsert({
          where: {
            userId_tryonId: {
              userId,
              tryonId: jobId,
            },
          },
          update: {
            accessedAt: new Date(),
          },
          create: {
            userId,
            tryonId: jobId,
            accessedAt: new Date(),
          },
        });
      } catch (error) {
        console.error("Error updating recent try-ons:", error);
        // Continue even if recent try-ons can't be updated
      }
    }

    // Return the job details
    return res.status(200).json({
      success: true,
      data: {
        id: job.id,
        status: job.status,
        mode: job.mode,
        humanImagePath: job.humanImagePath
          ? utils.toUrlPath(job.humanImagePath)
          : null,
        clothImagePath: job.clothImagePath
          ? utils.toUrlPath(job.clothImagePath)
          : null,
        bottomClothImagePath: job.bottomClothImagePath
          ? utils.toUrlPath(job.bottomClothImagePath)
          : null,
        resultImagePath: job.resultImagePath
          ? utils.toUrlPath(job.resultImagePath)
          : null,
        createdAt: job.createdAt,
        processingStartedAt: job.processingStartedAt,
        processingCompletedAt: job.processingCompletedAt,
        errorMessage: job.errorMessage,
      },
    });
  } catch (error) {
    console.error("Error getting virtual try-on job:", error);
    return res.status(500).json({
      success: false,
      message: "Error getting the virtual try-on job",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get all virtual try-on jobs for a user
 * Get user's virtual try-on history
 * @route GET /api/v1/virtual-try-on/history
 */
export const getVirtualTryOnHistory = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const history = await prisma.virtualTryOn.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return res.status(200).json({
      success: true,
      data: history,
    });
  } catch (error) {
    console.error("Error fetching virtual try-on history:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while fetching virtual try-on history",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get user's recent virtual try-ons
 * @route GET /api/v1/virtual-try-on/recent
 */
export const getRecentVirtualTryOns = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Get the most recent try-ons (limit to 10)
    const recentTryOns = await prisma.recentTryOn.findMany({
      where: {
        userId,
      },
      orderBy: {
        accessedAt: "desc",
      },
      take: 10,
    });

    // Get the virtual try-on jobs for these recent try-ons
    const tryOnIds = recentTryOns.map((recent) => recent.tryonId);
    const tryOnJobs = await prisma.virtualTryOn.findMany({
      where: {
        id: { in: tryOnIds },
      },
    });

    // Map to return only the necessary data
    const formattedRecentTryOns = recentTryOns
      .map((recent) => {
        const tryOn = tryOnJobs.find((job) => job.id === recent.tryonId);
        if (!tryOn) return null; // Skip if job not found

        return {
          id: tryOn.id,
          status: tryOn.status,
          mode: tryOn.mode,
          humanImagePath: tryOn.humanImagePath
            ? utils.toUrlPath(tryOn.humanImagePath)
            : null,
          clothImagePath: tryOn.clothImagePath
            ? utils.toUrlPath(tryOn.clothImagePath)
            : null,
          bottomClothImagePath: tryOn.bottomClothImagePath
            ? utils.toUrlPath(tryOn.bottomClothImagePath)
            : null,
          resultImagePath: tryOn.resultImagePath
            ? utils.toUrlPath(tryOn.resultImagePath)
            : null,
          createdAt: tryOn.createdAt,
          accessedAt: recent.accessedAt,
        };
      })
      .filter(Boolean);

    return res.status(200).json({
      success: true,
      data: formattedRecentTryOns,
    });
  } catch (error) {
    console.error("Error fetching recent try-ons:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while fetching recent try-ons",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get model images for a user
 * @route GET /api/v1/virtual-try-on/models
 */
export const getModelImages = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Get all model images for the user and default models
    const modelImages = await prisma.modelImage.findMany({
      where: {
        OR: [{ userId }, { isDefault: true }],
      },
      orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
    });

    // Format the response
    const formattedModelImages = modelImages.map((model) => ({
      id: model.id,
      imagePath: utils.toUrlPath(model.imagePath),
      modelName: model.modelName,
      gender: model.gender,
      bodyType: model.bodyType,
      poseType: model.poseType,
      ethnicity: model.ethnicity,
      isDefault: model.isDefault,
      createdAt: model.createdAt,
    }));

    return res.status(200).json({
      success: true,
      data: formattedModelImages,
    });
  } catch (error) {
    console.error("Error fetching model images:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while fetching model images",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get admin model images (our models)
 * @route GET /api/v1/virtual-try-on/models/admin
 */
export const getAdminModelImages = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Get only admin/default models
    const modelImages = await prisma.modelImage.findMany({
      where: {
        isDefault: true,
      },
      orderBy: [{ createdAt: "desc" }],
    });

    // Format the response
    const formattedModelImages = modelImages.map((model) => ({
      id: model.id,
      imagePath: utils.toUrlPath(model.imagePath),
      modelName: model.modelName,
      gender: model.gender,
      bodyType: model.bodyType,
      poseType: model.poseType,
      ethnicity: model.ethnicity,
      isDefault: model.isDefault,
      createdAt: model.createdAt,
    }));

    return res.status(200).json({
      success: true,
      data: formattedModelImages,
    });
  } catch (error) {
    console.error("Error fetching admin model images:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while fetching admin model images",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get user model images (your models)
 * @route GET /api/v1/virtual-try-on/models/user
 */
export const getUserModelImages = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Get only user's models
    const modelImages = await prisma.modelImage.findMany({
      where: {
        userId,
      },
      orderBy: [{ usageCount: "desc" }, { createdAt: "desc" }],
    });

    // Format the response
    const formattedModelImages = modelImages.map((model) => ({
      id: model.id,
      imagePath: utils.toUrlPath(model.imagePath),
      modelName: model.modelName,
      gender: model.gender,
      bodyType: model.bodyType,
      poseType: model.poseType,
      ethnicity: model.ethnicity,
      isDefault: model.isDefault,
      createdAt: model.createdAt,
    }));

    return res.status(200).json({
      success: true,
      data: formattedModelImages,
    });
  } catch (error) {
    console.error("Error fetching user model images:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while fetching user model images",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Upload a new model image
 * @route POST /api/v1/virtual-try-on/models
 */
export const uploadModelImage = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Get uploaded files from multer
    const files = req.files as {
      modelImage?: Express.Multer.File[];
    };

    // Check if model image is uploaded
    if (!files.modelImage) {
      return res.status(400).json({
        success: false,
        message: "Model image is required",
      });
    }

    const modelImagePath = files.modelImage[0].path;

    // Save the model image
    const finalModelImagePath = utils.saveHumanImage(
      modelImagePath,
      userId,
      false
    );

    // Create model image in database with simplified schema
    const modelImage = await prisma.modelImage.create({
      data: {
        userId,
        imagePath: finalModelImagePath,
        isDefault: false,
      },
    });

    return res.status(201).json({
      success: true,
      message: "Model image uploaded successfully",
      data: {
        id: modelImage.id,
        imagePath: utils.toUrlPath(modelImage.imagePath),
        isDefault: modelImage.isDefault,
        createdAt: modelImage.createdAt,
      },
    });
  } catch (error) {
    console.error("Error uploading model image:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while uploading model image",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Delete a model image
 * @route DELETE /api/v1/virtual-try-on/models/:id
 */
export const deleteModelImage = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const modelId = req.params.id;
    if (!modelId) {
      return res.status(400).json({
        success: false,
        message: "Model ID is required",
      });
    }

    // Find the model image
    const modelImage = await prisma.modelImage.findUnique({
      where: { id: modelId },
    });

    if (!modelImage) {
      return res.status(404).json({
        success: false,
        message: "Model image not found",
      });
    }

    // Check if the model belongs to the user
    if (modelImage.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to delete this model image",
      });
    }

    // Delete the file from storage
    try {
      fs.unlinkSync(modelImage.imagePath);
    } catch (err) {
      console.error("Error deleting model image file:", err);
      // Continue with database deletion even if file deletion fails
    }

    // Delete the model from database
    await prisma.modelImage.delete({
      where: { id: modelId },
    });

    return res.status(200).json({
      success: true,
      message: "Model image deleted successfully",
      data: { success: true },
    });
  } catch (error) {
    console.error("Error deleting model image:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while deleting model image",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
