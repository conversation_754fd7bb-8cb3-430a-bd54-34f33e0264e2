import { Request, Response } from "express";
import bcrypt from "bcrypt";
import { prisma } from "../index";
import { z } from "zod";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import {
  getProfileImageUrl,
  getAbsoluteProfileImagePath,
} from "../middleware/upload";

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  avatarUrl: z.string().optional(),
});

const updatePasswordSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string().min(8),
});

export const getCurrentUser = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user with profile, subscription, and credit
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        profile: true,
        creditPurchaseOrders: {
          include: {
            creditPackage: true,
          },
          where: {
            status: "COMPLETED",
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
        subscriptions: {
          where: {
            status: "ACTIVE",
          },
          include: {
            plan: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
        credit: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Format response data
    const userData = {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      profile: user.profile,
      subscription: user.subscriptions[0] || null,
      creditPurchase: user.creditPurchaseOrders[0] || null,
      credit: user.credit,
      createdAt: user.createdAt,
    };

    res.status(200).json({
      success: true,
      data: userData,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user data",
      },
    });
  }
};

export const updateProfile = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }
    console.log("req.body", req.body);
    // Validate request body
    const validatedData = updateProfileSchema.parse(req.body.profile);
    console.log("validatedData", validatedData);
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Update profile
    if (existingUser.profile) {
      // Update existing profile
      await prisma.userProfile.update({
        where: { userId: req.user.id },
        data: {
          firstName:
            validatedData.firstName !== undefined
              ? validatedData.firstName
              : existingUser.profile.firstName,
          lastName:
            validatedData.lastName !== undefined
              ? validatedData.lastName
              : existingUser.profile.lastName,
          avatarUrl:
            validatedData.avatarUrl !== undefined
              ? validatedData.avatarUrl
              : existingUser.profile.avatarUrl,
        },
      });
    } else {
      // Create new profile
      await prisma.userProfile.create({
        data: {
          userId: req.user.id,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          avatarUrl: validatedData.avatarUrl,
        },
      });
    }

    // Get updated user
    const updatedUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    res.status(200).json({
      success: true,
      data: {
        id: updatedUser?.id,
        email: updatedUser?.email,
        profile: updatedUser?.profile,
      },
      message: "Profile updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update profile",
      },
    });
  }
};

export const uploadProfileImage = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: "FILE_REQUIRED",
          message: "No image file provided",
        },
      });
    }

    // Process the uploaded image with sharp
    const outputFilename = `profile-${req.user.id}-${Date.now()}${path.extname(req.file.filename)}`;
    const outputPath = getAbsoluteProfileImagePath(outputFilename);

    await sharp(req.file.path)
      .resize(256, 256) // Resize to standard profile image size
      .jpeg({ quality: 90 }) // Convert to JPEG with good quality
      .toFile(outputPath);

    // Delete the original uploaded file to save space
    fs.unlinkSync(req.file.path);

    // Generate the public URL for the image
    const avatarUrl = getProfileImageUrl(outputFilename);

    // Update the user's profile with the new avatar URL
    const existingUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    if (existingUser?.profile) {
      // If the user already has a profile image, delete the old one
      if (existingUser.profile.avatarUrl) {
        const oldFilename = path.basename(existingUser.profile.avatarUrl);
        const oldFilePath = getAbsoluteProfileImagePath(oldFilename);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }
      }

      // Update existing profile
      await prisma.userProfile.update({
        where: { userId: req.user.id },
        data: {
          avatarUrl,
        },
      });
    } else {
      // Create new profile
      await prisma.userProfile.create({
        data: {
          userId: req.user.id,
          firstName: "",
          lastName: "",
          avatarUrl,
        },
      });
    }

    // Return the updated profile
    const updatedUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    res.status(200).json({
      success: true,
      data: {
        avatarUrl,
        profile: updatedUser?.profile,
      },
      message: "Profile image uploaded successfully",
    });
  } catch (error) {
    console.error("Profile image upload error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to upload profile image",
      },
    });
  }
};

export const updatePassword = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = updatePasswordSchema.parse(req.body);

    // Find user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Check if user has a password (OAuth users might not have one)
    if (!user.password) {
      return res.status(400).json({
        success: false,
        error: {
          code: "NO_PASSWORD_SET",
          message: "Cannot change password for OAuth accounts",
        },
      });
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(
      validatedData.currentPassword,
      user.password
    );

    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_PASSWORD",
          message: "Current password is incorrect",
        },
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(validatedData.newPassword, salt);

    // Update password
    await prisma.user.update({
      where: { id: req.user.id },
      data: { password: hashedPassword },
    });

    res.status(200).json({
      success: true,
      message: "Password updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update password",
      },
    });
  }
};

export const getCreditTransactions = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user's credit transactions
    const transactions = await prisma.creditTransaction.findMany({
      where: { userId: req.user.id },
      orderBy: { createdAt: "desc" },
    });

    res.status(200).json({
      success: true,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit transactions",
      },
    });
  }
};
