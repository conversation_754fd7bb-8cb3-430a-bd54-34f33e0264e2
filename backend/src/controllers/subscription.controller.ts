import { Request, Response } from "express";
import { prisma } from "../index";
import { z } from "zod";
import { AppError } from "../utils/error";
import { generateYearlyPlan } from "../utils/subscription.utils";

// Validation schemas
const subscriptionPlanSchema = z.object({
  name: z.string(),
  displayName: z.string(),
  description: z.string(),
  price: z.number().positive(),
  currency: z.string().default("USD"),
  interval: z.enum(["month", "year"]),
  creditsAmount: z.number().int().min(0),
  features: z.record(z.any()),
  featureHighlights: z.array(
    z.object({
      title: z.string(),
      description: z.string(),
      icon: z.string().optional(),
    })
  ),
  colorScheme: z.string().optional(),
  isFeatured: z.boolean().default(false),
  sortOrder: z.number().int().default(0),
  stripePriceId: z.string().optional(),
  paypalPlanId: z.string().optional(),
  isActive: z.boolean().default(true),
});

/**
 * Get all subscription plans with detailed features
 */
export const getAllSubscriptionPlans = async (req: Request, res: Response) => {
  try {
    // Get all active subscription plans ordered by price (as sortOrder might not be available yet)
    const monthlyPlans = await prisma.subscriptionPlan.findMany({
      where: { isActive: true, interval: "month" },
      orderBy: { price: "asc" },
    });

    // Get existing yearly plans
    const existingYearlyPlans = await prisma.subscriptionPlan.findMany({
      where: { isActive: true, interval: "year" },
      orderBy: { price: "asc" },
    });

    // Generate yearly plans with 20% discount if they don't already exist
    const yearlyPlans =
      existingYearlyPlans.length > 0
        ? existingYearlyPlans
        : monthlyPlans.map((plan) => generateYearlyPlan(plan));

    // Combine monthly and yearly plans ...yearlyPlans
    const allPlans = [...monthlyPlans];

    res.status(200).json({
      success: true,
      data: allPlans,
    });
  } catch (error) {
    console.error("Error fetching subscription plans:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get subscription plans",
      },
    });
  }
};

/**
 * Get a specific subscription plan by ID
 */
export const getSubscriptionPlanById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id },
    });

    if (!plan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PLAN_NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    res.status(200).json({
      success: true,
      data: plan,
    });
  } catch (error) {
    console.error("Error fetching subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get subscription plan",
      },
    });
  }
};

/**
 * Create a new subscription plan (admin only)
 */
export const createSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Only administrators can create subscription plans",
        },
      });
    }

    // Validate request body
    const validatedData = subscriptionPlanSchema.parse(req.body);

    // Create the subscription plan
    const plan = await prisma.subscriptionPlan.create({
      data: validatedData,
    });

    res.status(201).json({
      success: true,
      data: plan,
      message: "Subscription plan created successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    console.error("Error creating subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create subscription plan",
      },
    });
  }
};

/**
 * Update an existing subscription plan (admin only)
 */
export const updateSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Only administrators can update subscription plans",
        },
      });
    }

    const { id } = req.params;

    // Check if plan exists
    const existingPlan = await prisma.subscriptionPlan.findUnique({
      where: { id },
    });

    if (!existingPlan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PLAN_NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    // Validate request body
    const validatedData = subscriptionPlanSchema.partial().parse(req.body);

    // Update the subscription plan
    const updatedPlan = await prisma.subscriptionPlan.update({
      where: { id },
      data: validatedData,
    });

    res.status(200).json({
      success: true,
      data: updatedPlan,
      message: "Subscription plan updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    console.error("Error updating subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update subscription plan",
      },
    });
  }
};

/**
 * Delete a subscription plan (admin only)
 */
export const deleteSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Only administrators can delete subscription plans",
        },
      });
    }

    const { id } = req.params;

    // Check if plan exists
    const existingPlan = await prisma.subscriptionPlan.findUnique({
      where: { id },
    });

    if (!existingPlan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PLAN_NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    // Instead of deleting, mark as inactive
    await prisma.subscriptionPlan.update({
      where: { id },
      data: { isActive: false },
    });

    res.status(200).json({
      success: true,
      message: "Subscription plan deactivated successfully",
    });
  } catch (error) {
    console.error("Error deleting subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete subscription plan",
      },
    });
  }
};

/**
 * Get the current user's subscription details
 */
export const getUserSubscription = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user's active subscription with plan details
    const subscription = await prisma.userSubscription.findFirst({
      where: {
        userId: req.user.id,
        status: "ACTIVE",
      },
      include: {
        plan: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!subscription) {
      return res.status(200).json({
        success: true,
        data: null,
        message: "User has no active subscription",
      });
    }

    res.status(200).json({
      success: true,
      data: subscription,
    });
  } catch (error) {
    console.error("Error fetching user subscription:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user subscription",
      },
    });
  }
};

/**
 * Compare all subscription plans for the user
 */
/**
 * Upgrade a subscription to a higher tier plan
 */
export const upgradeSubscription = async (req: Request, res: Response) => {
  try {
    const { currentSubscriptionId, newPlanId } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("User not authenticated", 401);
    }

    if (!currentSubscriptionId || !newPlanId) {
      throw new AppError("Missing required parameters", 400);
    }

    const subscriptionService = req.app.get('subscriptionService');
    const result = await subscriptionService.upgradeSubscription(
      userId,
      currentSubscriptionId,
      newPlanId
    );

    return res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error("Error upgrading subscription:", error);
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message,
        code: error.code
      });
    }
    return res.status(500).json({
      success: false,
      error: "Failed to upgrade subscription"
    });
  }
};

/**
 * Downgrade a subscription to a lower tier plan
 */
export const downgradeSubscription = async (req: Request, res: Response) => {
  try {
    const { currentSubscriptionId, newPlanId } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("User not authenticated", 401);
    }

    if (!currentSubscriptionId || !newPlanId) {
      throw new AppError("Missing required parameters", 400);
    }

    const subscriptionService = req.app.get('subscriptionService');
    const result = await subscriptionService.downgradeSubscription(
      userId,
      currentSubscriptionId,
      newPlanId
    );

    return res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error("Error downgrading subscription:", error);
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message,
        code: error.code
      });
    }
    return res.status(500).json({
      success: false,
      error: "Failed to downgrade subscription"
    });
  }
};

/**
 * Get user's scheduled plan changes
 */
export const getScheduledPlanChanges = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("User not authenticated", 401);
    }

    const scheduledChanges = await prisma.scheduledPlanChange.findMany({
      where: {
        userId,
        status: "SCHEDULED"
      },
      include: {
        currentSubscription: {
          include: {
            plan: true
          }
        },
        newPlan: true
      },
      orderBy: {
        effectiveDate: "asc"
      }
    });

    return res.status(200).json({
      success: true,
      data: scheduledChanges
    });
  } catch (error) {
    console.error("Error getting scheduled plan changes:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to get scheduled plan changes"
    });
  }
};

/**
 * Cancel a scheduled plan change
 */
export const cancelScheduledPlanChange = async (req: Request, res: Response) => {
  try {
    const { changeId } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("User not authenticated", 401);
    }

    if (!changeId) {
      throw new AppError("Missing required parameters", 400);
    }

    const subscriptionService = req.app.get('subscriptionService');
    const result = await subscriptionService.cancelScheduledPlanChange(userId, changeId);

    return res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error("Error cancelling scheduled plan change:", error);
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message,
        code: error.code
      });
    }
    return res.status(500).json({
      success: false,
      error: "Failed to cancel scheduled plan change"
    });
  }
};

/**
 * Process all scheduled plan changes that are due (admin only)
 */
export const processScheduledPlanChanges = async (req: Request, res: Response) => {
  try {
    // Verify admin access
    const user = req.user;
    if (!user || user.role !== 'ADMIN') {
      throw new AppError("Unauthorized access", 403);
    }

    const subscriptionService = req.app.get('subscriptionService');
    const results = await subscriptionService.processScheduledPlanChanges();

    return res.status(200).json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error("Error processing scheduled plan changes:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to process scheduled plan changes"
    });
  }
};

export const compareSubscriptionPlans = async (req: Request, res: Response) => {
  try {
    // Get all active subscription plans
    const monthlyPlans = await prisma.subscriptionPlan.findMany({
      where: { isActive: true, interval: "month" },
      orderBy: { price: "asc" },
    });

    // Get existing yearly plans
    const existingYearlyPlans = await prisma.subscriptionPlan.findMany({
      where: { isActive: true, interval: "year" },
      orderBy: { price: "asc" },
    });

    // Generate yearly plans with 20% discount if they don't already exist
    const yearlyPlans =
      existingYearlyPlans.length > 0
        ? existingYearlyPlans
        : monthlyPlans.map((plan) => generateYearlyPlan(plan));

    // Combine monthly and yearly plans
    const plans = [...monthlyPlans, ...yearlyPlans];

    // If user is authenticated, get their current plan
    let currentPlanId = null;
    if (req.user) {
      const userSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId: req.user.id,
          status: "ACTIVE",
        },
        select: { planId: true },
      });

      if (userSubscription) {
        currentPlanId = userSubscription.planId;
      }
    }

    // Format the response with comparison data
    const comparisonData = {
      plans,
      currentPlanId,
      featureCategories: [
        {
          name: "Core Quotas",
          features: [
            "videoGenerationQuota",
            "imageGenerationQuota",
            "backgroundRemovalQuota",
          ],
        },
        {
          name: "Performance & Efficiency",
          features: ["processingPriority", "uploadSpeed", "concurrentJobs"],
        },
        {
          name: "Advanced Features",
          features: ["advancedAI", "customTemplates", "apiAccess"],
        },
        {
          name: "Support & Collaboration",
          features: ["supportLevel", "teamMembers", "sharedWorkspaces"],
        },
        {
          name: "Storage & Assets",
          features: ["storageLimit", "assetLibrary", "historyRetention"],
        },
      ],
    };

    res.status(200).json({
      success: true,
      data: comparisonData,
    });
  } catch (error) {
    console.error("Error comparing subscription plans:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to compare subscription plans",
      },
    });
  }
};
