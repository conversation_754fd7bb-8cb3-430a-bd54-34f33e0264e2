import { PrismaClient } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

const prisma = new PrismaClient();

async function main() {
  try {
    // Clear existing data
    await prisma.subscriptionPlan.deleteMany();
    await prisma.creditPackage.deleteMany();

    console.log("Cleared existing data");

    // Subscription plan data from provided array
    const subScriptionPlanData = [
      {
        credits_per_month: 100,
        price_per_month: 8.5,
        price_per_try: 0.085,
        currency: "USD",
      },
      {
        credits_per_month: 200,
        price_per_month: 15.0,
        price_per_try: 0.075,
        currency: "USD",
      },
      {
        credits_per_month: 300,
        price_per_month: 22.5,
        price_per_try: 0.075,
        currency: "USD",
      },
      {
        credits_per_month: 500,
        price_per_month: 35.0,
        price_per_try: 0.07,
        currency: "USD",
      },
      {
        credits_per_month: 1000,
        price_per_month: 60.0,
        price_per_try: 0.06,
        currency: "USD",
      },
      {
        credits_per_month: 2000,
        price_per_month: 100.0,
        price_per_try: 0.05,
        currency: "USD",
      },
      {
        credits_per_month: 4000,
        price_per_month: 160.0,
        price_per_try: 0.04,
        currency: "USD",
      },
      {
        credits_per_month: 8000,
        price_per_month: 240.0,
        price_per_try: 0.03,
        currency: "USD",
      },
    ];

    // Top-up plan data from provided array
    const topUpPlanData = [
      {
        credits: 10,
        price: 1.0,
        price_per_try: 0.1,
        currency: "USD",
      },
      {
        credits: 100,
        price: 9.0,
        price_per_try: 0.09,
        currency: "USD",
      },
      {
        credits: 300,
        price: 25.0,
        price_per_try: 0.083,
        currency: "USD",
      },
      {
        credits: 500,
        price: 40.0,
        price_per_try: 0.08,
        currency: "USD",
      },
      {
        credits: 1000,
        price: 70.0,
        price_per_try: 0.07,
        currency: "USD",
      },
      {
        credits: 2000,
        price: 120.0,
        price_per_try: 0.06,
        currency: "USD",
      },
      {
        credits: 4000,
        price: 200.0,
        price_per_try: 0.05,
        currency: "USD",
      },
      {
        credits: 8000,
        price: 320.0,
        price_per_try: 0.04,
        currency: "USD",
      },
    ];

    // Define subscription plans based on the provided array
    const subscriptionPlans = subScriptionPlanData.map((plan, index) => {
      return {
        id: uuidv4(),
        name: `plan-${plan.credits_per_month}`,
        displayName: `${plan.credits_per_month} Credits Plan`,
        description: `Subscription plan with ${plan.credits_per_month} credits per month`,
        price: plan.price_per_month,
        currency: plan.currency,
        interval: "month",
        creditsAmount: plan.credits_per_month,
        features: {
          credits: plan.credits_per_month,
          videoMinutes: Math.floor(plan.credits_per_month / 20), // Estimate based on credit cost
          virtualTryOns: Math.floor(plan.credits_per_month / 3),
          speedPaintings: Math.floor(plan.credits_per_month / 10),
          imageGenerations: Math.floor(plan.credits_per_month / 2),
          backgroundRemovals: Math.floor(plan.credits_per_month),
          pricePerCredit: plan.price_per_try,
        },
        featureHighlights: [
          {
            icon: "/icons/credit.svg",
            title: `${plan.credits_per_month} Credits Monthly`,
            description: "Use for any service",
          },
          {
            icon: "/icons/video.svg",
            title: `${Math.floor(
              plan.credits_per_month / 20
            )} Minutes of Video`,
            description: "Create engaging AI videos",
          },
          {
            icon: "/icons/image.svg",
            title: `${Math.floor(
              plan.credits_per_month / 2
            )} Image Generations`,
            description: "Create stunning AI images",
          },
        ],
        colorScheme: plan.credits_per_month < 500 ? "blue" : "gold",
        isFeatured: plan.credits_per_month === 1000, // Make the 1000 credit plan featured
        sortOrder: index + 1,
        isActive: true,
      };
    });

    // Define credit packages (top-up plans) based on the provided array
    const creditPackages = topUpPlanData.map((plan, index) => {
      return {
        id: uuidv4(),
        name: `${plan.credits} Credits`,
        description: `Top-up package with ${plan.credits} credits`,
        creditsAmount: plan.credits,
        price: plan.price,
        pricePerCredit: plan.price_per_try,
        currency: plan.currency,
        isActive: true,
      };
    });

    // Clear existing data
    console.log("Clearing existing data...");
    await prisma.subscriptionPlan.deleteMany();
    await prisma.creditPackage.deleteMany();

    // Insert subscription plans
    console.log("Inserting subscription plans...");
    for (const plan of subscriptionPlans) {
      await prisma.subscriptionPlan.create({
        data: plan,
      });
    }

    console.log("Created subscription plans");

    // Insert credit packages
    for (const pkg of creditPackages) {
      await prisma.creditPackage.create({
        data: pkg,
      });
    }

    console.log("Created credit packages");

    // Create a free plan in the database for reference
    // const freePlan = {
    //   id: uuidv4(),
    //   name: "free",
    //   displayName: "Free Plan",
    //   description: "Free plan for new users",
    //   price: 0,
    //   currency: "USD",
    //   interval: "month",
    //   features: {
    //     credits: 20,
    //     videoMinutes: 1,
    //     virtualTryOns: 20,
    //     speedPaintings: 4,
    //     imageGenerations: 10,
    //     backgroundRemovals: "unlimited",
    //   },
    //   featureHighlights: [
    //     {
    //       icon: "/icons/album.svg",
    //       title: "20 Free credits",
    //       description: "Use for any service",
    //     },
    //     {
    //       icon: "/icons/unlimited.svg",
    //       title: "Unlimited background removal",
    //       description: "Remove backgrounds from your images",
    //     },
    //     {
    //       icon: "/icons/setting.svg",
    //       title: "20 Virtual Try-ons",
    //       description: "Try on clothes virtually",
    //     },
    //     {
    //       icon: "/icons/ps.svg",
    //       title: "4 Speedpainting Videos",
    //       description: "Create speedpainting videos",
    //     },
    //     {
    //       icon: "/icons/refer.svg",
    //       title: "Referral Program",
    //       description: "Refer friends and earn free credits",
    //     },
    //   ],
    //   colorScheme: "gray",
    //   isFeatured: false,
    //   sortOrder: 0,
    //   isActive: true,
    // };

    // await prisma.subscriptionPlan.create({
    //   data: freePlan,
    // });

    // console.log("Created free plan");
    console.log("Seeding completed successfully");
  } catch (error) {
    console.error("Error seeding database:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
