import axios from "axios";
import {
  <PERSON>rismaClient,
  User,
  BackgroundImageRemovalJob,
  JobStatus,
} from "@prisma/client";
import { AppError } from "../utils/error";
import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import sharp from "sharp";

const prisma = new PrismaClient();

export class BgRemovalService {
  private apiKey: string;
  private apiBaseUrl: string;
  private requestQueue: Map<string, Promise<string>>;
  private requestsPerMinute: Map<string, number>;
  private lastResetTime: Map<string, number>;
  private readonly RATE_LIMIT = 10; // Max requests per minute per user

  constructor() {
    // Update to use the new Miragic AI API endpoint
    this.apiKey = process.env.MIRAGIC_AI_API_KEY || "";
    this.apiBaseUrl =
      process.env.MIRAGIC_AI_API_URL || "https://api.miragic.ai";
    this.requestQueue = new Map();
    this.requestsPerMinute = new Map();
    this.lastResetTime = new Map();

    if (!this.apiKey) {
      console.warn(
        "Miragic AI API key is not set, some features may be limited"
      );
    }

    // Clean up rate limiting data every 10 minutes
    setInterval(() => this.cleanupRateLimitData(), 10 * 60 * 1000);
  }

  /**
   * Clean up rate limiting data for users who haven't made requests in the last 10 minutes
   */
  private cleanupRateLimitData(): void {
    const now = Date.now();
    const CLEANUP_THRESHOLD = 10 * 60 * 1000; // 10 minutes

    for (const [userId, lastReset] of this.lastResetTime.entries()) {
      if (now - lastReset > CLEANUP_THRESHOLD) {
        this.requestsPerMinute.delete(userId);
        this.lastResetTime.delete(userId);
      }
    }
  }

  /**
   * Create a background removal job
   */
  async createBgRemovalJob(
    user: User,
    originalImageName: string,
    originalImageUrl: string,
    storagePathOriginal: string
  ): Promise<BackgroundImageRemovalJob> {
    try {
      // Check if user has active subscription or enough credits
      const hasAccess = await this.checkUserAccess(user.id);

      if (!hasAccess) {
        throw new AppError(
          "Insufficient credits or no active subscription",
          403
        );
      }

      // Create job record in database
      const job = await prisma.backgroundImageRemovalJob.create({
        data: {
          userId: user.id,
          originalImageName,
          originalImageUrl,
          storagePathOriginal,
          status: JobStatus.PENDING,
        },
      });

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create background removal job", 500);
    }
  }

  /**
   * Process a background removal job
   * Enhanced with better error handling, metrics tracking, and credit management
   */
  async processBgRemovalJob(jobId: string): Promise<BackgroundImageRemovalJob> {
    const startTime = Date.now();
    let errorMessage = "";
    let processingStatus = "failed";

    try {
      // Get job from database with user information
      const job = await prisma.backgroundImageRemovalJob.findUnique({
        where: { id: jobId },
        include: { user: true },
      });

      if (!job) {
        throw new AppError("Background removal job not found", 404);
      }

      // Update job status to processing
      await prisma.backgroundImageRemovalJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.PROCESSING,
          metadata: {
            processingStartTime: new Date().toISOString(),
          },
        },
      });

      // Verify user has enough credits before processing
      const hasAccess = await this.checkUserAccess(job.userId);
      if (!hasAccess) {
        throw new AppError(
          "Insufficient credits or no active subscription",
          403
        );
      }

      // Call Miragic AI API to remove background with user ID for rate limiting
      const processedImageUrl = await this.callRemovalAIAPI(
        job.originalImageUrl,
        job.userId
      );

      // Generate storage path for processed image
      const storagePathProcessed = `${path.dirname(
        job.storagePathOriginal
      )}/processed/${path.basename(job.storagePathOriginal)}`;

      // Update job with processed image URL and status
      const updatedJob = await prisma.backgroundImageRemovalJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.COMPLETED,
          processedImageUrl,
          storagePathProcessed,
          metadata: {
            // processingStartTime: job.metadata?.processingStartTime,
            processingEndTime: new Date().toISOString(),
            processingDurationMs: Date.now() - startTime,
          },
        },
      });

      // Log API usage with detailed metrics
      await prisma.apiUsageLog.create({
        data: {
          userId: job.userId,
          featureUsed: "BACKGROUND_REMOVAL",
          creditsConsumed: 1, // 1 credit per image
          requestTimestamp: new Date(startTime),
          responseTimestamp: new Date(),
          status: "success",
          metadata: {
            processingTimeMs: Date.now() - startTime,
            imageSize: job.originalImageName
              ? Buffer.from(job.originalImageName).length
              : 0,
            jobId: job.id,
            apiVersion: "2.0",
          },
        },
      });

      // Deduct credits if applicable
      await this.deductCredits(job.userId, 1);

      processingStatus = "success";
      return updatedJob;
    } catch (error) {
      // Capture error message for logging
      errorMessage = error instanceof Error ? error.message : "Unknown error";

      // Update job status to failed with detailed error information
      await prisma.backgroundImageRemovalJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.FAILED,
          errorMessage,
          metadata: {
            processingEndTime: new Date().toISOString(),
            processingDurationMs: Date.now() - startTime,
          },
        },
      });

      // Log the failure for analytics
      try {
        await prisma.apiUsageLog.create({
          data: {
            userId:
              (
                await prisma.backgroundImageRemovalJob.findUnique({
                  where: { id: jobId },
                })
              )?.userId || "unknown",
            featureUsed: "BACKGROUND_REMOVAL",
            creditsConsumed: 0, // No credits consumed on failure
            requestTimestamp: new Date(startTime),
            responseTimestamp: new Date(),
            metadata: {
              processingTimeMs: Date.now() - startTime,
            },
            status: "failed",
            errorDetails: errorMessage,
          },
        });
      } catch (logError) {
        console.error("Failed to log API usage:", logError);
      }

      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to process background removal job", 500);
    } finally {
      // Record metrics for monitoring
      console.log(
        `Background removal job ${jobId} completed with status ${processingStatus} in ${
          Date.now() - startTime
        }ms`
      );
      if (errorMessage) {
        console.error(
          `Background removal job ${jobId} failed with error: ${errorMessage}`
        );
      }
    }
  }

  /**
   * Get a background removal job
   */
  async getBgRemovalJob(
    jobId: string,
    userId: string
  ): Promise<BackgroundImageRemovalJob> {
    try {
      const job = await prisma.backgroundImageRemovalJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new AppError("Background removal job not found", 404);
      }

      // Check if job belongs to user
      if (job.userId !== userId) {
        throw new AppError(
          "Unauthorized access to background removal job",
          403
        );
      }

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to get background removal job", 500);
    }
  }

  /**
   * Get all background removal jobs for a user
   */
  async getUserBgRemovalJobs(
    userId: string
  ): Promise<BackgroundImageRemovalJob[]> {
    try {
      const jobs = await prisma.backgroundImageRemovalJob.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
      });

      return jobs;
    } catch (error) {
      throw new AppError("Failed to get user background removal jobs", 500);
    }
  }

  /**
   * Process image to remove background using Miragic AI API
   * Implements rate limiting, caching, and error handling
   */
  private async callRemovalAIAPI(
    imageUrl: string,
    userId: string = "anonymous"
  ): Promise<string> {
    try {
      // Check if we're in development mode and should use mock data
      if (
        process.env.NODE_ENV === "development" &&
        process.env.USE_MOCK_API === "true"
      ) {
        console.log("Using mock background removal in development mode");
        return this.getMockProcessedImage(imageUrl);
      }

      // Check rate limiting for this user
      this.checkRateLimit(userId);

      // Check if this image URL is already being processed (request deduplication)
      const cacheKey = `${userId}:${imageUrl}`;
      if (this.requestQueue.has(cacheKey)) {
        console.log(`Reusing in-progress request for image: ${imageUrl}`);
        return this.requestQueue.get(cacheKey)!;
      }

      // Create a new promise for this request and add it to the queue
      const requestPromise = this.executeRemovalRequest(
        imageUrl,
        userId,
        cacheKey
      );
      this.requestQueue.set(cacheKey, requestPromise);

      // Return the promise result
      return await requestPromise;
    } catch (error) {
      console.error("Error calling Miragic AI API:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to process image for background removal", 500);
    }
  }

  /**
   * Execute the actual API request to Miragic AI
   */
  private async executeRemovalRequest(
    imageUrl: string,
    userId: string,
    cacheKey: string
  ): Promise<string> {
    try {
      console.log(
        `Calling Miragic AI API to remove background from image: ${imageUrl}`
      );

      // For Node.js environment, use form-data package instead of browser's FormData
      const FormData = require("form-data");
      const formData = new FormData();

      // If the URL is from our own server, we can use the file directly
      if (imageUrl.includes("/uploads/")) {
        const filePath = path.join(
          process.cwd(),
          "uploads",
          path.basename(imageUrl)
        );
        if (fs.existsSync(filePath)) {
          // Using fs.createReadStream is compatible with form-data package
          formData.append("file", fs.createReadStream(filePath));
        } else {
          // If file doesn't exist locally, use the URL
          formData.append("image_url", imageUrl);
        }
      } else {
        // For external URLs, pass the URL directly
        formData.append("image_url", imageUrl);
      }

      // Call the Miragic AI API with proper timeout and retry logic
      const response = await axios.post(
        `${this.apiBaseUrl}/remove_background`,
        formData,
        {
          headers: {
            ...formData.getHeaders(), // form-data package has getHeaders method
            Authorization: `Bearer ${this.apiKey}`,
          },
          timeout: 30000, // 30 second timeout
          maxContentLength: 20 * 1024 * 1024, // 20MB max response size
        }
      );

      // Remove from request queue after completion
      this.requestQueue.delete(cacheKey);

      // Check if the API call was successful
      if (!response.data || response.data.status !== "success") {
        console.error("Miragic AI API error:", response.data);
        throw new AppError(
          `API error: ${response.data?.message || "Unknown error"}`,
          500
        );
      }

      // Get the URL of the processed image
      const processedImageUrl = response.data.image_url;
      console.log(
        `Background removed successfully, processed image URL: ${processedImageUrl}`
      );

      // Check if we should download and store the image locally
      if (process.env.STORE_PROCESSED_IMAGES === "true") {
        await this.downloadAndStoreProcessedImage(processedImageUrl, userId);
      }

      return processedImageUrl;
    } catch (error) {
      // Remove from request queue on error
      this.requestQueue.delete(cacheKey);

      if (axios.isAxiosError(error) && error.response) {
        console.error(
          `API error (${error.response.status}):`,
          error.response.data
        );
        throw new AppError(
          `API error: ${error.response.status} - ${
            error.response.data?.message || error.message
          }`,
          error.response.status
        );
      }

      throw error;
    }
  }

  /**
   * Download and store processed image locally or in cloud storage
   */
  private async downloadAndStoreProcessedImage(
    imageUrl: string,
    userId: string
  ): Promise<string> {
    try {
      // Create directory if it doesn't exist
      const processedDir = path.join(process.cwd(), "uploads", "processed");
      if (!fs.existsSync(processedDir)) {
        fs.mkdirSync(processedDir, { recursive: true });
      }

      // Generate a unique filename
      const filename = `${uuidv4()}.png`;
      const outputPath = path.join(processedDir, filename);

      // Download the image
      const response = await axios.get(imageUrl, { responseType: "stream" });
      const writer = fs.createWriteStream(outputPath);

      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on("finish", () => {
          const baseUrl = process.env.BASE_URL || "http://localhost:5000";
          resolve(`${baseUrl}/uploads/processed/${filename}`);
        });
        writer.on("error", reject);
      });
    } catch (error) {
      console.error("Error downloading processed image:", error);
      // Return the original URL if download fails
      return imageUrl;
    }
  }

  /**
   * Check rate limit for a user and throw an error if exceeded
   */
  private checkRateLimit(userId: string): void {
    const now = Date.now();
    const ONE_MINUTE = 60 * 1000;

    // Initialize or reset counters if needed
    if (
      !this.requestsPerMinute.has(userId) ||
      !this.lastResetTime.has(userId)
    ) {
      this.requestsPerMinute.set(userId, 0);
      this.lastResetTime.set(userId, now);
    }

    // Reset counter if a minute has passed
    const lastReset = this.lastResetTime.get(userId)!;
    if (now - lastReset > ONE_MINUTE) {
      this.requestsPerMinute.set(userId, 0);
      this.lastResetTime.set(userId, now);
    }

    // Check current count
    const currentCount = this.requestsPerMinute.get(userId)!;
    if (currentCount >= this.RATE_LIMIT) {
      const resetTime = new Date(lastReset + ONE_MINUTE);
      throw new AppError(
        `Rate limit exceeded. Try again after ${resetTime.toISOString()}`,
        429
      );
    }

    // Increment the counter
    this.requestsPerMinute.set(userId, currentCount + 1);
  }

  /**
   * Get mock processed image for development
   */
  private async getMockProcessedImage(imageUrl: string): Promise<string> {
    try {
      // Extract filename from URL
      const filename = path.basename(imageUrl);

      // Determine input path
      const uploadsDir = path.join(process.cwd(), "uploads");
      const inputPath = path.join(uploadsDir, filename);

      // Create processed directory if it doesn't exist
      const processedDir = path.join(uploadsDir, "processed");
      if (!fs.existsSync(processedDir)) {
        fs.mkdirSync(processedDir, { recursive: true });
      }

      // Generate output filename with transparent background
      const outputFilename = `${path.parse(filename).name}-nobg.png`;
      const outputPath = path.join(processedDir, outputFilename);

      // Process the image using Sharp to simulate background removal
      // This doesn't actually remove the background but simulates processing
      await sharp(inputPath)
        .resize(800)
        .modulate({ brightness: 1.1, saturation: 1.2 }) // Enhance the image a bit
        .sharpen() // Make it look more processed
        .toFormat("png")
        .toFile(outputPath);

      console.log(
        `Mock background removal completed, output file: ${outputPath}`
      );

      // Return the URL to the processed image
      const baseUrl = process.env.BASE_URL || "http://localhost:5000";
      return `${baseUrl}/uploads/processed/${outputFilename}`;
    } catch (error) {
      console.error("Error creating mock processed image:", error);
      throw new AppError("Failed to create mock processed image", 500);
    }
  }

  /**
   * Check if user has access to background removal
   * Either through an active subscription or enough credits
   */
  private async checkUserAccess(userId: string): Promise<boolean> {
    try {
      // Check if user has an active subscription
      const activeSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
      });

      if (activeSubscription) {
        // Check if subscription plan includes background removal
        const features = activeSubscription.plan.features as any;
        if (features.background_removal_quota > 0) {
          // Check if user has used up their quota
          const usageCount = await prisma.apiUsageLog.count({
            where: {
              userId,
              featureUsed: "BACKGROUND_REMOVAL",
              requestTimestamp: {
                gte: new Date(new Date().setDate(new Date().getDate() - 30)), // Last 30 days
              },
            },
          });

          if (usageCount < features.background_removal_quota) {
            return true;
          }
        }
      }

      // Check if user has enough credits
      const userCredit = await prisma.credit.findUnique({
        where: { userId },
      });

      if (userCredit && userCredit.balance >= 1) {
        // 1 credit per background removal
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error checking user access:", error);
      return false;
    }
  }

  /**
   * Deduct credits from user's account
   */
  private async deductCredits(userId: string, credits: number): Promise<void> {
    try {
      // Check if user has an active subscription
      const activeSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
      });

      if (activeSubscription) {
        // If user has an active subscription, don't deduct credits
        // as it's included in their plan
        return;
      }

      // Deduct credits from user's account
      await prisma.credit.update({
        where: { userId },
        data: {
          balance: {
            decrement: credits,
          },
        },
      });

      // Log credit transaction
      await prisma.creditTransaction.create({
        data: {
          userId,
          amount: -credits,
          type: "USAGE",
          description: "Background removal",
        },
      });
    } catch (error) {
      console.error("Error deducting credits:", error);
    }
  }
}
