import { PaymentType, PrismaClient, User } from "@prisma/client";
import <PERSON><PERSON> from "stripe";
import { AppError } from "../utils/error";
import { CreditService } from "./credit.service";
import * as paypal from "@paypal/paypal-server-sdk";
import {
  STRIPE_SECRET_KEY,
  PAYPAL_CLIENT_ID,
  PAYPAL_CLIENT_SECRET,
  NODE_ENV,
} from "../config";

// Define PayPal environment types to fix TypeScript errors
type PayPalEnvironment = {
  clientId: string;
  clientSecret: string;
  baseUrl?: string;
};

// Extend PrismaClient to include PaypalOrder model
// declare module "@prisma/client" {
//   interface PrismaClient {

//     paypalOrder: {
//       create: (args: any) => Promise<any>;
//       findUnique: (args: any) => Promise<any>;
//       update: (args: any) => Promise<any>;
//       findFirst: (args: any) => Promise<any>;
//     };
//   }
// }

// Define PayPal order resource type
type PayPalOrderResource = {
  id: string;
  status: string;
  supplementary_data?: {
    related_ids?: {
      order_id?: string;
    };
  };
  related_ids?: {
    order_id?: string;
  };
};

const prisma = new PrismaClient();
const creditService = new CreditService();
export class PaymentService {
  private stripe: Stripe;
  private paypalClient: any;

  constructor() {
    // Check if Stripe key is available
    if (!STRIPE_SECRET_KEY) {
      console.warn(
        "Warning: STRIPE_SECRET_KEY is not set in environment variables"
      );
    }

    this.stripe = new Stripe(STRIPE_SECRET_KEY || "sk_test_dummy", {
      apiVersion: "2022-11-15" as any,
    });

    // Initialize PayPal client if keys are available

    // For now, we'll disable PayPal client initialization to avoid errors
    // We can re-enable this when we have proper PayPal SDK setup
    this.paypalClient = null;
    console.log(
      "PayPal client initialization skipped - will be configured later"
    );

    /* Commented out until PayPal SDK is properly set up
    if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
      console.warn(
        "Warning: PayPal API keys are not set in environment variables"
      );
      // Set paypalClient to null to avoid initialization errors
      this.paypalClient = null;
    } else {
      try {
        // Initialize PayPal client with environment
        const environment: PayPalEnvironment = {
          clientId: PAYPAL_CLIENT_ID,
          clientSecret: PAYPAL_CLIENT_SECRET,
        };

        this.paypalClient = new (paypal as any).core.PayPalHttpClient(
          NODE_ENV === "production"
            ? new (paypal as any).core.LiveEnvironment(
                environment.clientId,
                environment.clientSecret
              )
            : new (paypal as any).core.SandboxEnvironment(
                environment.clientId,
                environment.clientSecret
              )
        );
      } catch (error) {
        console.error("Failed to initialize PayPal client:", error);
        this.paypalClient = null;
      }
    }
    */
  }

  /**
   * Get all available subscription plans
   */
  async getSubscriptionPlans() {
    try {
      const plans = await prisma.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: { price: "asc" },
      });

      return plans;
    } catch (error) {
      throw new AppError("Failed to get subscription plans", 500);
    }
  }

  /**
   * Get all available credit packs
   */
  async getCreditPacks() {
    try {
      const creditPacks = await prisma.creditPackage.findMany({
        where: { isActive: true },
        orderBy: { price: "asc" },
      });

      return creditPacks;
    } catch (error) {
      throw new AppError("Failed to get credit packs", 500);
    }
  }

  /**
   * Create a Stripe checkout session for subscription
   */
  async createStripeCheckoutSession(
    user: User,
    planId: string,
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      const subPlanId = planId.endsWith("-yearly")
        ? planId.slice(0, -"-yearly".length)
        : planId;
      // Get plan details
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: subPlanId },
      });

      if (!plan) {
        throw new AppError("Subscription plan not found", 404);
      }

      // Check if user already has a Stripe customer ID
      let customerId = user.stripeCustomerId;

      if (!customerId) {
        // Create a new customer in Stripe
        const customer = await this.stripe.customers.create({
          email: user.email,
          metadata: {
            userId: user.id,
          },
        });
        console.log("customerId", customerId);
        customerId = customer.id;

        // Update user with Stripe customer ID
        await prisma.user.update({
          where: { id: user.id },
          data: { stripeCustomerId: customerId },
        });
      }

      // Prepare line items based on whether we have a Stripe price ID or not
      let lineItems;

      if (plan.stripePriceId) {
        // If we have a Stripe price ID, use it directly
        lineItems = [
          {
            price: plan.stripePriceId,
            quantity: 1,
          },
        ];
      } else {
        // If no Stripe price ID, create a price_data object
        lineItems = [
          {
            price_data: {
              currency: plan.currency.toLowerCase(),
              product_data: {
                name: plan.name, // Use name as it's guaranteed to exist
                description: `${plan.name} subscription plan`,
              },
              unit_amount: Math.round(plan.price * 100), // Convert to cents
              recurring: {
                interval:
                  plan.interval === "year"
                    ? "year"
                    : ("month" as "year" | "month"),
              },
            },
            quantity: 1,
          },
        ];
      }
      // console.log("lineItems", lineItems);
      // Create checkout session
      const session = await this.stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ["card"],
        line_items: lineItems,
        mode: "subscription",
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          userId: user.id,
          planId: plan.id,
        },
        subscription_data: {
          metadata: {
            userId: user.id,
            planId: plan.id,
            type: "subscription",
          },
        },
      });
      // console.log("plan", plan);
      // console.log("session", session);
      return { sessionId: session.id, url: session.url };
    } catch (error) {
      console.error("Error creating Stripe checkout session:", error);
      throw new AppError("Failed to create checkout session", 500);
    }
  }

  /**
   * Create a Stripe Payment Intent for credit purchase
   * This allows using Stripe Elements directly on the site instead of redirecting
   */
  async createStripePaymentIntent(params: {
    amount: number;
    currency: string;
    customerId: string | null;
    metadata: {
      orderId: string;
      packageId: string;
      userId: string;
      credits: string;
      type: string;
    };
  }): Promise<Stripe.PaymentIntent> {
    try {
      // Create the payment intent options object without the customer field initially
      const paymentIntentOptions: Stripe.PaymentIntentCreateParams = {
        amount: params.amount,
        currency: params.currency,
        metadata: params.metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      };

      // Only add the customer if it's a valid non-empty string
      if (params.customerId) {
        paymentIntentOptions.customer = params.customerId;
      }

      const paymentIntent = await this.stripe.paymentIntents.create(
        paymentIntentOptions
      );

      return paymentIntent;
    } catch (error) {
      console.error("Error creating Stripe payment intent:", error);
      throw new AppError(
        "Failed to create payment intent",
        // "PAYMENT_INTENT_CREATION_FAILED",
        500
      );
    }
  }

  /**
   * Create a Stripe checkout session for credit purchase
   */
  async createStripeCreditCheckoutSession(
    user: User,
    amount: number,
    credits: number,
    successUrl: string,
    cancelUrl: string,
    purchaseOrderId?: string
  ) {
    try {
      // Check if user already has a Stripe customer ID
      let customerId = user.stripeCustomerId;

      if (!customerId) {
        // Create a new customer in Stripe
        const customer = await this.stripe.customers.create({
          email: user.email,
          metadata: {
            userId: user.id,
          },
        });

        customerId = customer.id;

        // Update user with Stripe customer ID
        await prisma.user.update({
          where: { id: user.id },
          data: { stripeCustomerId: customerId },
        });
      }

      // Create a payment intent
      const session = await this.stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ["card"],
        line_items: [
          {
            price_data: {
              currency: "usd",
              product_data: {
                name: `${credits} Credits`,
                description: `Purchase ${credits} credits for Miragic-AI`,
              },
              unit_amount: amount * 100, // Convert to cents
            },
            quantity: 1,
          },
        ],
        mode: "payment",
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          userId: user.id,
          credits: credits.toString(),
          type: "credit_purchase",
          purchaseOrderId: purchaseOrderId || "",
        },
      });

      // If we have a purchase order ID, update it with the session ID
      if (purchaseOrderId) {
        await prisma.creditPurchaseOrder.update({
          where: { id: purchaseOrderId },
          data: {
            paymentIntentId: session.id,
            status: "PROCESSING",
          },
        });
      }

      return { sessionId: session.id, url: session.url };
    } catch (error) {
      console.error("Error creating Stripe credit checkout session:", error);
      throw new AppError("Failed to create credit checkout session", 500);
    }
  }

  /**
   * Handle Stripe webhook events with idempotency
   */
  async handleStripeWebhook(event: Stripe.Event, idempotencyKey?: string) {
    try {
      // Check if this event has already been processed (idempotency check)
      if (idempotencyKey) {
        const existingEvent = await prisma.webhookEvent.findUnique({
          where: { eventId: idempotencyKey },
        });

        if (existingEvent) {
          console.log(`Event ${idempotencyKey} already processed, skipping`);
          return; // Skip processing if already handled
        }
      }

      // Process the event based on its type
      let processingResult;
      switch (event.type) {
        case "checkout.session.completed":
          processingResult = await this.handleCheckoutSessionCompleted(
            event.data.object as Stripe.Checkout.Session
          );
          break;

        case "invoice.paid":
          processingResult = await this.handleInvoicePaid(
            event.data.object as Stripe.Invoice
          );
          break;
        case "charge.refund.updated":
          processingResult = await this.handleChargeRefundUpdated(
            event.data.object as Stripe.Refund
          );
          break;
        case "invoice.payment_failed":
          processingResult = await this.handleInvoicePaymentFailed(
            event.data.object as Stripe.Invoice
          );
          break;

        case "customer.subscription.updated":
          processingResult = await this.handleSubscriptionUpdated(
            event.data.object as Stripe.Subscription
          );
          break;

        case "customer.subscription.deleted":
          processingResult = await this.handleSubscriptionDeleted(
            event.data.object as Stripe.Subscription
          );
          break;
        case "charge.succeeded":
          processingResult = await this.handleChargeSucceeded(
            event.data.object as Stripe.Charge
          );
          break;
        default:
          processingResult = {
            processed: false,
            message: `Unhandled event type: ${event.type}`,
          };
          console.log(`Unhandled event type: ${event.type}`);
      }

      // Record that we've processed this event (for idempotency)
      if (idempotencyKey) {
        await prisma.webhookEvent.create({
          data: {
            eventId: idempotencyKey,
            eventType: event.type,
            processed: processingResult?.processed ?? true,
            processingDetails:
              processingResult?.message || "Successfully processed",
            processedAt: new Date(),
          },
        });
      }
    } catch (error) {
      console.error(`Error handling Stripe webhook (${event.type}):`, error);
      throw new AppError(
        `Failed to handle Stripe webhook (${event.type}): ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        500
      );
    }
  }

  /**
   * Handle checkout session completed event
   * @returns Processing result for idempotency tracking
   */
  private async handleCheckoutSessionCompleted(
    session: Stripe.Checkout.Session
  ): Promise<{ processed: boolean; message: string }> {
    try {
      const { userId, planId, credits, type } = session.metadata || {};

      if (!userId) {
        throw new Error("User ID not found in session metadata");
      }

      // Log the checkout session for debugging
      console.log(
        `Processing checkout session ${session.id} for user ${userId}`
      );
      console.log(`Session metadata:`, session.metadata);
      console.log(`Session status: ${session.status}`);

      if (type === "credit_purchase" && credits) {
        // Handle credit purchase
        const creditAmount = parseInt(credits, 10);
        const purchaseOrderId = session.metadata?.purchaseOrderId;

        if (purchaseOrderId) {
          // Find the purchase order
          const purchaseOrder = await prisma.creditPurchaseOrder.findUnique({
            where: { id: purchaseOrderId },
          });

          if (purchaseOrder && purchaseOrder.status !== "COMPLETED") {
            // Import the credit service to complete the purchase
            const creditService =
              new (require("./credit.service").CreditService)();

            // Complete the credit purchase
            await creditService.completeCreditPurchase(
              purchaseOrderId,
              session.payment_intent as string,
              session.amount_total ? session.amount_total / 100 : undefined
            );

            return {
              processed: true,
              message: `Successfully processed credit purchase of ${creditAmount} credits for user ${userId} with purchase order ${purchaseOrderId}`,
            };
          } else if (purchaseOrder && purchaseOrder.status === "COMPLETED") {
            // Already processed, return success for idempotency
            return {
              processed: true,
              message: `Credit purchase already processed for order ${purchaseOrderId}`,
            };
          }
        }

        // Fallback for backward compatibility or if no purchase order found
        // Add credits to user's account
        await prisma.credit.upsert({
          where: { userId },
          update: {
            balance: {
              increment: creditAmount,
            },
          },
          create: {
            userId,
            balance: creditAmount,
          },
        });

        // Log credit transaction
        await prisma.creditTransaction.create({
          data: {
            userId,
            amount: creditAmount,
            type: "PURCHASE",
            description: `Purchased ${creditAmount} credits`,
          },
        });

        // Log payment
        await prisma.payment.create({
          data: {
            userId,
            amount: session.amount_total ? session.amount_total / 100 : 0, // Convert from cents
            currency: session.currency || "usd",
            status: "COMPLETED",
            provider: "STRIPE",
            transactionId: session.payment_intent as string,
            paymentType: "CREDIT_PURCHASE",
          },
        });

        return {
          processed: true,
          message: `Successfully processed credit purchase of ${creditAmount} credits for user ${userId}`,
        };
      } else if (planId) {
        // For subscription purchases, we need to handle the initial subscription creation here
        // even though invoice.paid will handle future payments

        // If the session has a subscription, we can use it to create the subscription record
        if (session.subscription) {
          // Retrieve the subscription details
          const subscription = await this.stripe.subscriptions.retrieve(
            session.subscription as string
          );

          // Get the plan from the database
          const plan = await prisma.subscriptionPlan.findUnique({
            where: { id: planId },
          });

          if (!plan) {
            throw new Error(`Subscription plan ${planId} not found`);
          }

          const now = new Date();
          // Access current_period_end from the raw subscription object
          const periodEnd = new Date(
            (subscription as any).current_period_end * 1000
          );

          // Generate a unique ID for the subscription record
          const subscriptionId = subscription.id;

          // Create or update the user subscription
          await prisma.userSubscription.upsert({
            where: {
              id: subscriptionId, // Use the Stripe subscription ID as our primary key
            },
            update: {
              status: this.mapStripeStatusToPrismaStatus(subscription.status),
              planId: plan.id,
              currentPeriodEnd: periodEnd,
              stripeSubscriptionId: subscription.id, // Ensure this is set in updates too
            },
            create: {
              id: subscriptionId,
              userId,
              planId: plan.id,
              stripeSubscriptionId: subscription.id,
              status: this.mapStripeStatusToPrismaStatus(subscription.status),
              startDate: now,
              currentPeriodEnd: periodEnd,
              endDate: null,
            },
          });

          // Log payment if there's a payment intent
          if (session.payment_intent) {
            await prisma.payment.create({
              data: {
                userId,
                amount: session.amount_total ? session.amount_total / 100 : 0,
                currency: session.currency || "usd",
                status: "COMPLETED",
                provider: "STRIPE",
                transactionId: session.payment_intent as string,
                paymentType: "SUBSCRIPTION",
                metadata: {
                  subscriptionId: subscription.id,
                },
              },
            });
          }

          return {
            processed: true,
            message: `Successfully processed subscription purchase for plan ${plan.name} for user ${userId}`,
          };
        } else {
          // If there's no subscription yet, this might be a pending setup
          console.log(
            `No subscription found in session ${session.id}, waiting for invoice.paid event`
          );
          return {
            processed: true,
            message: `Checkout completed but waiting for subscription activation for user ${userId}`,
          };
        }
      }

      return {
        processed: false,
        message: `Unhandled checkout session type for session ${session.id}`,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(
        `Error handling checkout session completed (${session.id}):`,
        error
      );
      return { processed: false, message: `Error: ${errorMessage}` };
    }
  }

  /**
   * Handle invoice paid event
   * @returns Processing result for idempotency tracking
   */
  private async handleInvoicePaid(
    invoice: Stripe.Invoice
  ): Promise<{ processed: boolean; message: string }> {
    try {
      console.log(`Processing invoice paid event for invoice ${invoice.id}`);
      console.log("invoice", invoice);
      // Validate that this invoice has a subscription
      if (!invoice.parent?.subscription_details?.subscription) {
        console.error(`Invoice ${invoice.id} has no subscription attached`);
        return {
          processed: false,
          message: `Invoice has no subscription attached`,
        };
      }

      // Get subscription from invoice
      const subscriptionId = invoice.parent?.subscription_details
        ?.subscription as string;

      const subscription = await this.stripe.subscriptions.retrieve(
        subscriptionId
      );

      // Get user by Stripe customer ID
      if (!invoice.customer) {
        const errorMsg = `No customer found in invoice ${invoice.id}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }

      const customerId =
        typeof invoice.customer === "string"
          ? invoice.customer
          : invoice.customer.id;

      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: customerId },
      });

      if (!user) {
        const errorMsg = `User not found for Stripe customer ID: ${customerId}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }
      console.log("subscription", subscription);
      const planId = subscription?.metadata?.planId;
      const purchaseOrderId = subscription?.metadata?.purchaseOrderId;
      console.log(
        "subscription.items?.data?.[0]",
        subscription.items?.data?.[0]
      );
      // Get plan from subscription
      if (
        !subscription.items?.data?.[0]?.price?.id &&
        !planId &&
        !purchaseOrderId
      ) {
        const errorMsg = `No price ID found in subscription ${subscriptionId}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }

      const priceId = subscription.items?.data?.[0]?.price?.id;
      const plan = await prisma.subscriptionPlan.findFirst({
        where: { id: planId },
      });
      const creditPurchaseOrder = await prisma.creditPurchaseOrder.findFirst({
        where: { id: purchaseOrderId },
        include: {
          creditPackage: true,
        },
      });
      console.log("plan", plan);
      console.log("creditPurchaseOrder", creditPurchaseOrder);
      console.log("creditPurchaseOrder s", creditPurchaseOrder?.creditPackage);

      if (!plan) {
        const errorMsg = `Subscription plan not found for price ID: ${priceId}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }
      console.log(
        "subscription.items?.data?.[0]",
        subscription.items?.data?.[0]
      );
      const now = new Date();
      const periodEnd = new Date(
        subscription.items?.data?.[0]?.current_period_end * 1000
      );

      // Log the subscription details for debugging
      console.log(`Subscription details for ${subscriptionId}:`, {
        status: subscription.status,
        planId: plan.id,
        userId: user.id,
        currentPeriodEnd: periodEnd,
      });

      // Update or create user subscription
      await prisma.userSubscription.upsert({
        where: {
          id: subscriptionId,
        },
        update: {
          status: this.mapStripeStatusToPrismaStatus(subscription.status),
          stripeSubscriptionId: subscriptionId,
          planId: plan.id,
          currentPeriodEnd: periodEnd,
        },
        create: {
          id: subscriptionId,
          userId: user.id,
          planId: plan.id,
          stripeSubscriptionId: subscriptionId,
          status: this.mapStripeStatusToPrismaStatus(subscription.status),
          startDate: now,
          currentPeriodEnd: periodEnd,
          endDate: null,
        },
      });
      if (subscription.metadata.type === "subscription") {
        await prisma.credit.update({
          where: {
            userId: user.id,
          },
          data: {
            balance: {
              increment: +plan.creditsAmount,
            },
          },
        });
      } else if (subscription.metadata.type === "credit_purchase") {
        await prisma.credit.update({
          where: {
            userId: user.id,
          },
          data: {
            balance: {
              increment: +Number(
                creditPurchaseOrder?.creditPackage?.creditsAmount
              ),
            },
          },
        });
      }

      // Record payment
      const paymentIntent = (invoice as any).payment_intent;
      const transactionId =
        typeof paymentIntent === "string"
          ? paymentIntent
          : paymentIntent?.id || invoice.id;

      await prisma.payment.create({
        data: {
          userId: user.id,
          amount: invoice.amount_paid / 100, // Convert from cents
          currency: invoice.currency,
          status: "COMPLETED",
          provider: "STRIPE",
          transactionId: transactionId,
          paymentType:
            subscription.metadata.type === "subscription"
              ? "SUBSCRIPTION"
              : "CREDIT_PURCHASE",

          subscriptionId: subscription.id,
          invoicePdf: invoice.invoice_pdf,
          hostedInvoiceUrl: invoice.hosted_invoice_url,

          metadata: {
            subscriptionId: subscription.id,
          },
        },
      });

      return {
        processed: true,
        message: `Successfully processed invoice payment for user ${user.id}, plan ${plan.name}`,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(`Error handling invoice paid (${invoice.id}):`, error);
      return { processed: false, message: `Error: ${errorMessage}` };
    }
  }
  /**
   * handleChargeRefundUpdated
   * **/
  private async handleChargeRefundUpdated(refund: Stripe.Refund) {
    try {
      console.log(
        `Processing charge refund updated event for refund ${refund.id}`
      );
      console.log("refund", refund);
      // Check if this is a credit purchase
      const payment = await prisma.payment.findFirst({
        where: { transactionId: refund.payment_intent as string },
      });
      console.log("payment", payment);
      if (!payment) {
        console.log(`No payment found for refund ${refund.id}`);
        return;
      }

      // Update payment status
      await prisma.payment.update({
        where: { id: payment.id },
        data: { status: "REFUNDED" },
      });
      await prisma.userSubscription.updateMany({
        where: { stripeSubscriptionId: payment.subscriptionId },
        data: {
          status: "CANCELED",
          currentPeriodEnd: new Date(),
          endDate: new Date(),
        },
      });
      await creditService.updateCreditExpiryForUser(payment.userId);
      const cancelFromStripe = await this.stripe.subscriptions.cancel(
        payment.subscriptionId as string
      );
      console.log("cancelFromStripe", cancelFromStripe);
      console.log(
        `Successfully processed charge refund updated event for refund ${refund.id}`
      );
    } catch (error) {
      console.error(
        `Error processing charge refund updated event (${refund.id}):`,
        error
      );
    }
  }
  /**
   * Handle charge succeeded event
   * @returns Processing result for idempotency tracking
   */
  private async handleChargeSucceeded(
    charge: Stripe.Charge
  ): Promise<{ processed: boolean; message: string }> {
    try {
      console.log(`Processing charge succeeded event for charge ${charge.id}`);

      // Check if this is a credit purchase
      if (charge.metadata.type !== "credit_purchase") {
        console.log(`Charge ${charge.id} is not a credit purchase, skipping`);
        return {
          processed: true,
          message: `Charge is not a credit purchase`,
        };
      }

      const userId = charge.metadata.userId;
      if (!userId) {
        const errorMsg = `No user ID found in charge metadata ${charge.id}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        const errorMsg = `User not found for ID: ${userId}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }

      const packageId = charge.metadata.packageId;
      if (!packageId) {
        const errorMsg = `No package ID found in charge ${charge.id}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }

      const creditPackage = await prisma.creditPackage.findUnique({
        where: { id: packageId },
      });

      if (!creditPackage) {
        const errorMsg = `Credit package not found for package ID: ${packageId}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }

      // Get the order ID from metadata
      const orderId = charge.metadata.orderId;
      if (!orderId) {
        const errorMsg = `No order ID found in charge metadata ${charge.id}`;
        console.error(errorMsg);
        return { processed: false, message: errorMsg };
      }

      // Get payment intent ID
      const paymentIntent = charge.payment_intent;
      const paymentIntentId =
        typeof paymentIntent === "string"
          ? paymentIntent
          : paymentIntent?.id || charge.id;

      // Use the credit service to complete the purchase properly
      try {
        // Import the credit service to complete the purchase
        const creditService = new (require("./credit.service").CreditService)();

        // Complete the credit purchase
        await creditService.completeCreditPurchase(
          orderId,
          paymentIntentId,
          charge.amount / 100 // Convert from cents
        );

        return {
          processed: true,
          message: `Successfully processed credit purchase for user ${userId}, package ${creditPackage.name}`,
        };
      } catch (purchaseError) {
        console.error(`Error completing credit purchase:`, purchaseError);
        return {
          processed: false,
          message: `Error completing purchase: ${
            purchaseError instanceof Error
              ? purchaseError.message
              : "Unknown error"
          }`,
        };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(`Error handling charge succeeded (${charge.id}):`, error);
      return { processed: false, message: `Error: ${errorMessage}` };
    }
  }

  /**
   * Handle invoice payment failed event
   */
  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
    try {
      // Get user by Stripe customer ID
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: invoice.customer as string },
      });

      if (!user) {
        throw new Error("User not found for Stripe customer ID");
      }

      // Update subscription status if there is one
      const subscriptionId = (invoice as any).subscription as string;
      if (subscriptionId) {
        await prisma.userSubscription.updateMany({
          where: {
            userId: user.id,
            stripeSubscriptionId: subscriptionId,
          },
          data: {
            status: "PAST_DUE",
          },
        });
      }

      // Record failed payment
      await prisma.payment.create({
        data: {
          userId: user.id,
          amount: invoice.amount_due / 100, // Convert from cents
          currency: invoice.currency,
          status: "FAILED",
          provider: "STRIPE",
          transactionId:
            ((invoice as any).payment_intent as string) || invoice.id,
          paymentType: "SUBSCRIPTION",
        },
      });
    } catch (error) {
      console.error("Error handling invoice payment failed:", error);
    }
  }

  /**
   * Handle subscription updated event
   */
  private async handleSubscriptionUpdated(subscription: Stripe.Subscription) {
    try {
      // Get user by Stripe customer ID
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: subscription.customer as string },
      });

      if (!user) {
        throw new Error("User not found for Stripe customer ID");
      }

      const newStatus = this.mapStripeStatusToPrismaStatus(subscription.status);
      console.log("subscription", subscription);
      console.log(
        "(subscription as any).current_period_end",
        (subscription as any).current_period_end
      );
      console.log(
        "subscription.items?.data?.[0]?.current_period_end",
        subscription.items?.data?.[0]?.current_period_end
      );
      // Validate and convert current_period_end
      const currentPeriodEnd =
        (subscription as any).current_period_end ||
        subscription.items?.data?.[0]?.current_period_end;
      if (!currentPeriodEnd || typeof currentPeriodEnd !== "number") {
        throw new Error(`Invalid current_period_end: ${currentPeriodEnd}`);
      }

      // Validate and convert cancel_at if it exists
      let endDate: Date | null = null;
      if (subscription.cancel_at) {
        if (typeof subscription.cancel_at !== "number") {
          throw new Error(`Invalid cancel_at: ${subscription.cancel_at}`);
        }
        endDate = new Date(subscription.cancel_at * 1000);
      }

      // Update subscription status
      await prisma.userSubscription.updateMany({
        where: {
          userId: user.id,
          stripeSubscriptionId: subscription.id,
        },
        data: {
          status: newStatus,
          currentPeriodEnd: new Date(currentPeriodEnd * 1000),
          endDate: endDate,
        },
      });

      // Update credit expiry based on new subscription status
      // If subscription is active or trialing, credits shouldn't expire
      // If subscription is canceled or past due, credits should expire after 2 years
      if (newStatus === "ACTIVE" || newStatus === "TRIALING") {
        console.log(
          `Updating credit expiry for user ${user.id} - subscription is now ${newStatus}`
        );
        await creditService.updateCreditExpiryForUser(user.id);
      }
    } catch (error) {
      console.error("Error handling subscription updated:", error);
      // Log the subscription object for debugging
      console.error(
        "Subscription object:",
        JSON.stringify(subscription, null, 2)
      );
    }
  }
  // private async handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  //   try {
  //     // Get user by Stripe customer ID
  //     const user = await prisma.user.findFirst({
  //       where: { stripeCustomerId: subscription.customer as string },
  //     });

  //     if (!user) {
  //       throw new Error("User not found for Stripe customer ID");
  //     }

  //     const newStatus = this.mapStripeStatusToPrismaStatus(subscription.status);

  //     // Update subscription status
  //     await prisma.userSubscription.updateMany({
  //       where: {
  //         userId: user.id,
  //         stripeSubscriptionId: subscription.id,
  //       },
  //       data: {
  //         status: newStatus,
  //         currentPeriodEnd: new Date(
  //           (subscription as any).current_period_end * 1000
  //         ),
  //         endDate: subscription.cancel_at
  //           ? new Date(subscription.cancel_at * 1000)
  //           : null,
  //       },
  //     });

  //     // Update credit expiry based on new subscription status
  //     // If subscription is active or trialing, credits shouldn't expire
  //     // If subscription is canceled or past due, credits should expire after 2 years
  //     if (newStatus === "ACTIVE" || newStatus === "TRIALING") {
  //       console.log(
  //         `Updating credit expiry for user ${user.id} - subscription is now ${newStatus}`
  //       );
  //       await creditService.updateCreditExpiryForUser(user.id);
  //     }
  //   } catch (error) {
  //     console.error("Error handling subscription updated:", error);
  //   }
  // }

  /**
   * Handle subscription deleted event
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription) {
    try {
      // Get user by Stripe customer ID
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: subscription.customer as string },
      });

      if (!user) {
        throw new Error("User not found for Stripe customer ID");
      }

      // Update subscription status
      await prisma.userSubscription.updateMany({
        where: {
          userId: user.id,
          stripeSubscriptionId: subscription.id,
        },
        data: {
          status: "CANCELED",
          endDate: new Date(),
        },
      });

      // When subscription is deleted, update credit expiry to set expiry dates
      console.log(
        `Updating credit expiry for user ${user.id} - subscription has been deleted`
      );
      await creditService.updateCreditExpiryForUser(user.id);
    } catch (error) {
      console.error("Error handling subscription deleted:", error);
    }
  }

  /**
   * Map Stripe subscription status to Prisma subscription status
   */
  private mapStripeStatusToPrismaStatus(
    stripeStatus: string
  ): "ACTIVE" | "CANCELED" | "PAST_DUE" | "TRIALING" | "UNPAID" {
    switch (stripeStatus) {
      case "active":
        return "ACTIVE";
      case "canceled":
        return "CANCELED";
      case "past_due":
        return "PAST_DUE";
      case "trialing":
        return "TRIALING";
      case "unpaid":
        return "UNPAID";
      default:
        return "ACTIVE";
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(userId: string, subscriptionId: string) {
    try {
      // Get user subscription
      const userSubscription = await prisma.userSubscription.findFirst({
        where: {
          id: subscriptionId,
          userId: userId,
        },
      });

      if (!userSubscription) {
        throw new AppError("Subscription not found", 404);
      }

      if (userSubscription.stripeSubscriptionId) {
        // Cancel subscription in Stripe
        await this.stripe.subscriptions.update(
          userSubscription.stripeSubscriptionId,
          {
            cancel_at_period_end: true,
          }
        );

        // Update subscription in database
        await prisma.userSubscription.update({
          where: { id: subscriptionId },
          data: {
            status: "CANCELED",
            endDate: userSubscription.currentPeriodEnd,
          },
        });
      } else if (userSubscription.paypalSubscriptionId) {
        // TODO: Implement PayPal subscription cancellation
        throw new AppError(
          "PayPal subscription cancellation not implemented",
          501
        );
      } else {
        throw new AppError("Invalid subscription", 400);
      }

      return { message: "Subscription canceled successfully" };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error("Error canceling subscription:", error);
      throw new AppError("Failed to cancel subscription", 500);
    }
  }

  /**
   * Get user's invoices
   */
  async getUserInvoices(user: User) {
    try {
      // Get user's payments
      const payments = await prisma.payment.findMany({
        where: { userId: user.id },
        orderBy: { createdAt: "desc" },
      });

      return payments;
    } catch (error) {
      console.error("Error getting user invoices:", error);
      throw new AppError("Failed to get user invoices", 500);
    }
  }

  /**
   * Create a refund in Stripe
   */
  async createStripeRefund(paymentIntentId: string, amount: number) {
    try {
      // Check if Stripe is initialized
      if (!this.stripe) {
        throw new AppError(
          "Stripe is not initialized",
          500,
          "STRIPE_NOT_INITIALIZED"
        );
      }

      // Create the refund
      const refund = await this.stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: Math.round(amount * 100), // Convert to cents
      });

      return refund;
    } catch (error) {
      console.error("Error creating Stripe refund:", error);
      throw new AppError(
        "Failed to create refund",
        500,
        "REFUND_CREATION_FAILED"
      );
    }
  }

  /**
   * Create a refund in PayPal
   */
  async createPayPalRefund(orderId: string, amount: number) {
    try {
      // Check if PayPal client is initialized
      if (!this.paypalClient) {
        console.warn(
          "PayPal client is not initialized - skipping actual refund API call"
        );
        // For now, we'll just log the refund request and return a mock response
        // This allows the system to work without actual PayPal integration
        console.log(
          `MOCK PayPal refund for order ${orderId} with amount ${amount}`
        );

        return {
          id: `mock-refund-${Date.now()}`,
          status: "COMPLETED",
          amount: {
            value: amount.toString(),
            currency_code: "USD",
          },
        };
      }

      // For PayPal refunds, we need to first get the capture ID from the order
      // Using any type to avoid TypeScript errors with PayPal SDK
      const getOrderRequest = new (paypal as any).orders.OrdersGetRequest(
        orderId
      );
      const orderResponse = await this.paypalClient.execute(getOrderRequest);

      // Get the capture ID from the order
      const captureId =
        orderResponse.result.purchase_units[0].payments.captures[0].id;

      // Create a refund request for the capture
      // Using any type to avoid TypeScript errors with PayPal SDK
      const refundRequest = new (paypal as any).payments.CapturesRefundRequest(
        captureId
      );
      refundRequest.requestBody({
        amount: {
          value: amount.toString(),
          currency_code: "USD",
        },
      });

      // Execute the refund request
      const refundResponse = await this.paypalClient.execute(refundRequest);

      return refundResponse.result;
    } catch (error) {
      console.error("Error creating PayPal refund:", error);
      throw new AppError(
        "Failed to create PayPal refund",
        500,
        "PAYPAL_REFUND_FAILED"
      );
    }
  }

  /**
   * Capture a PayPal order after approval
   */
  async capturePayPalOrder(orderId: string) {
    try {
      // Check if PayPal client is initialized
      if (!this.paypalClient) {
        throw new AppError(
          "PayPal client not initialized. Please check your API keys.",
          500
        );
      }

      // Find the order in our database
      const orderRecord = await prisma.paypalOrder.findUnique({
        where: { orderId },
      });

      if (!orderRecord) {
        throw new AppError("Order not found", 404);
      }

      // Capture the PayPal order
      const request = new (paypal as any).orders.OrdersCaptureRequest(orderId);
      request.requestBody({});
      const capture = await this.paypalClient.execute(request);

      if (capture.result.status === "COMPLETED") {
        // Update order status
        await prisma.paypalOrder.update({
          where: { orderId },
          data: { status: "COMPLETED" },
        });

        // Get current user credit or create if it doesn't exist
        const userCredit = await prisma.credit.findUnique({
          where: { userId: orderRecord.userId },
        });

        if (userCredit) {
          // Update existing credit
          await prisma.credit.update({
            where: { userId: orderRecord.userId },
            data: { balance: userCredit.balance + orderRecord.credits },
          });
        } else {
          // Create new credit record
          await prisma.credit.create({
            data: {
              userId: orderRecord.userId,
              balance: orderRecord.credits,
            },
          });
        }

        // Record payment
        await prisma.payment.create({
          data: {
            userId: orderRecord.userId,
            amount: orderRecord.amount,
            currency: "USD",
            status: "COMPLETED",
            provider: "PAYPAL",
            transactionId: orderId,
            orderId: orderId,
            paymentType: "CREDIT_PURCHASE",
          },
        });

        return {
          success: true,
          message: "Payment completed successfully",
          credits: orderRecord.credits,
        };
      } else {
        throw new AppError("Payment not completed", 400);
      }
    } catch (error: any) {
      console.error("Error capturing PayPal order:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to capture PayPal order", 500);
    }
  }

  /**
   * Create a PayPal order for credit purchase
   */
  async createPayPalOrder(
    userId: string,
    amount: number,
    credits: number,
    purchaseOrderId: string
  ) {
    try {
      if (!this.paypalClient) {
        throw new AppError("PayPal client is not initialized", 500);
      }

      // Get user
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new AppError("User not found", 404);
      }

      // TODO: Implement actual PayPal order creation
      // This is a placeholder until PayPal SDK is properly integrated
      console.log(`Creating PayPal order for ${credits} credits at $${amount}`);

      // For now, we'll create a mock PayPal order
      const mockOrderId = `PAYPAL-${Date.now()}-${Math.floor(
        Math.random() * 1000
      )}`;

      // Update the purchase order with the PayPal order ID
      await prisma.creditPurchaseOrder.update({
        where: { id: purchaseOrderId },
        data: {
          paymentIntentId: mockOrderId,
          status: "PROCESSING",
        },
      });

      return {
        orderId: mockOrderId,
        status: "CREATED",
      };
    } catch (error) {
      console.error("Error creating PayPal order:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create PayPal order", 500);
    }
  }

  /**
   * Handle PayPal webhook events
   */
  async handlePayPalWebhook(event: any) {
    try {
      // Check if PayPal client is initialized
      if (!this.paypalClient) {
        console.warn(
          "PayPal client not initialized. Webhook events cannot be processed."
        );
        return {
          received: true,
          processed: false,
          reason: "PayPal client not initialized",
        };
      }

      const eventType = event.event_type;

      switch (eventType) {
        case "PAYMENT.CAPTURE.COMPLETED":
          await this.handlePayPalPaymentCompleted(event.resource);
          break;
        case "PAYMENT.CAPTURE.DENIED":
        case "PAYMENT.CAPTURE.REFUNDED":
          await this.handlePayPalPaymentFailed(event.resource);
          break;
        default:
          console.log(`Unhandled PayPal webhook event: ${eventType}`);
      }

      return { received: true, processed: true };
    } catch (error) {
      console.error("Error handling PayPal webhook:", error);
      throw new AppError("Failed to handle PayPal webhook", 500);
    }
  }

  /**
   * Handle PayPal payment completed event
   */
  private async handlePayPalPaymentCompleted(resource: PayPalOrderResource) {
    try {
      // Extract order ID from various possible locations in the resource
      let orderId: string | undefined;

      if (resource.supplementary_data?.related_ids?.order_id) {
        orderId = resource.supplementary_data.related_ids.order_id;
      } else if (resource.related_ids?.order_id) {
        orderId = resource.related_ids.order_id;
      } else {
        orderId = resource.id;
      }
      if (!orderId) return;

      // Find the order in our database
      const orderRecord = await prisma.paypalOrder.findUnique({
        where: { orderId },
      });

      if (!orderRecord || orderRecord.status === "COMPLETED") return;

      // Update order status
      await prisma.paypalOrder.update({
        where: { orderId },
        data: { status: "COMPLETED" },
      });

      // Get current user credit or create if it doesn't exist
      const userCredit = await prisma.credit.findUnique({
        where: { userId: orderRecord.userId },
      });

      if (userCredit) {
        // Update existing credit
        await prisma.credit.update({
          where: { userId: orderRecord.userId },
          data: { balance: userCredit.balance + orderRecord.credits },
        });
      } else {
        // Create new credit record
        await prisma.credit.create({
          data: {
            userId: orderRecord.userId,
            balance: orderRecord.credits,
          },
        });
      }

      // Record payment if not already recorded
      const existingPayment = await prisma.payment.findFirst({
        where: { transactionId: orderId },
      });

      if (!existingPayment) {
        await prisma.payment.create({
          data: {
            userId: orderRecord.userId,
            amount: orderRecord.amount,
            currency: "USD",
            status: "COMPLETED",
            provider: "PAYPAL",
            transactionId: orderId,
            orderId: orderId,
            paymentType: "CREDIT_PURCHASE",
          },
        });
      }
    } catch (error) {
      console.error("Error handling PayPal payment completed:", error);
    }
  }

  /**
   * Handle PayPal payment failed event
   */
  private async handlePayPalPaymentFailed(resource: PayPalOrderResource) {
    try {
      // Extract order ID from various possible locations in the resource
      let orderId: string | undefined;

      if (resource.supplementary_data?.related_ids?.order_id) {
        orderId = resource.supplementary_data.related_ids.order_id;
      } else if (resource.related_ids?.order_id) {
        orderId = resource.related_ids.order_id;
      } else {
        orderId = resource.id;
      }
      if (!orderId) return;

      // Find the order in our database
      const orderRecord = await prisma.paypalOrder.findUnique({
        where: { orderId },
      });

      if (!orderRecord) return;

      // Update order status
      await prisma.paypalOrder.update({
        where: { orderId },
        data: { status: "FAILED" },
      });

      // Record failed payment
      const existingPayment = await prisma.payment.findFirst({
        where: { transactionId: orderId },
      });

      if (!existingPayment) {
        await prisma.payment.create({
          data: {
            userId: orderRecord.userId,
            amount: orderRecord.amount,
            currency: "USD",
            status: "FAILED",
            provider: "PAYPAL",
            transactionId: orderId,
            orderId: orderId,
            paymentType: "CREDIT_PURCHASE",
          },
        });
      }
    } catch (error) {
      console.error("Error handling PayPal payment failed:", error);
    }
  }
}
