import {
  PrismaClient,
  VirtualTryOn,
  VirtualTryOnMode,
  ClothingType,
  ModelImage,
  JobStatus,
} from "@prisma/client";
import axios from "axios";
import FormData from "form-data";
import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { prisma } from "../index";
import * as utils from "../utils/virtualTryOn.utils";
import { CreditUsageService } from "./creditUsage.service";

// Initialize credit usage service
const creditUsageService = new CreditUsageService();
import * as config from "../config";

// API configuration
const MIRAGIC_AI_API_KEY = process.env.MIRAGIC_AI_API_KEY || "";
const MIRAGIC_AI_API_URL =
  process.env.MIRAGIC_AI_API_URL || "https://api.miragic.ai";

// Ensure all directories exist
utils.ensureDirectories();

export class VirtualTryOnService {
  /**
   * Creates a new virtual try-on job
   * @param userId User ID
   * @param params Job parameters
   * @returns Created job
   */
  async createJob(
    userId: string,
    params: {
      humanImagePath: string;
      clothImagePath: string;
      bottomClothImagePath?: string;
      mode: VirtualTryOnMode;
      modelImageId?: string;
      garmentType?: string;
    }
  ): Promise<VirtualTryOn> {
    try {
      // Create a new job in the database
      const job = await prisma.virtualTryOn.create({
        data: {
          userId,
          humanImagePath: params.humanImagePath,
          clothImagePath: params.clothImagePath,
          bottomClothImagePath: params.bottomClothImagePath,
          mode: params.mode,
          modelImageId: params.modelImageId,
          status: JobStatus.PENDING,
          metadata: {
            garmentType: params.garmentType,
            createdAt: new Date().toISOString(),
          },
          sessionId: uuidv4(),
        },
      });

      // Process the job asynchronously
      this.processJob(job.id).catch((error) => {
        console.error(`Error processing job ${job.id}:`, error);
      });

      return job;
    } catch (error) {
      console.error("Error creating virtual try-on job:", error);
      throw new Error(
        `Failed to create virtual try-on job: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Processes a virtual try-on job
   * @param jobId Job ID
   */
  async processJob(jobId: string): Promise<void> {
    try {
      // Update job status to processing
      await prisma.virtualTryOn.update({
        where: { id: jobId },
        data: {
          status: JobStatus.PROCESSING,
          processingStartedAt: new Date(),
        },
      });

      // Get job details
      const job = await prisma.virtualTryOn.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new Error(`Job ${jobId} not found`);
      }

      // Get full paths to the uploaded files
      const humanImagePath = utils.getFullPath(job.humanImagePath);
      const clothImagePath = utils.getFullPath(job.clothImagePath);
      const bottomClothImagePath = job.bottomClothImagePath
        ? utils.getFullPath(job.bottomClothImagePath)
        : undefined;

      // Create form data for the API request
      const formData = new FormData();
      formData.append("human_image", fs.createReadStream(humanImagePath));
      formData.append("cloth_image", fs.createReadStream(clothImagePath));

      // Add bottom cloth image if available
      if (bottomClothImagePath) {
        formData.append(
          "low_cloth_image",
          fs.createReadStream(bottomClothImagePath)
        );
      }

      // Determine mode and garment_type based on job configuration
      let mode: string;
      let garmentType: string;

      const metadata = (job.metadata as Record<string, any>) || {};

      if (job.mode === VirtualTryOnMode.SINGLE) {
        mode = "single";

        // Map garment types correctly for single mode
        if (metadata?.garmentType) {
          switch (metadata.garmentType) {
            case "upper_body":
            case "TOP":
              garmentType = "upper";
              break;
            case "lower_body":
            case "BOTTOM":
              garmentType = "lower";
              break;
            case "full_body":
            case "DRESS":
            case "full_set":
              garmentType = "full_set";
              break;
            default:
              garmentType = "upper";
          }
        } else {
          garmentType = "upper"; // default
        }
      } else {
        // TOP_BOTTOM mode - combination of top and bottom
        mode = "comb";
        garmentType = ""; // Empty for combination mode
      }

      formData.append("mode", mode);
      formData.append("garment_type", garmentType);

      console.log(
        `Processing job ${jobId} with mode: ${mode}, garment_type: ${garmentType}`
      );

      // Make the API call
      const response = await axios.post(
        `${MIRAGIC_AI_API_URL}/virtual_try_on`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${MIRAGIC_AI_API_KEY}`,
          },
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        }
      );

      // Check if the API call was successful
      if (response.data && response.data.link) {
        // Save the processed image
        const resultImagePath = await utils.saveProcessedImage(
          response.data.link,
          job.userId,
          job.mode
        );
        console.log("resultImagePath", resultImagePath);
        // Deduct credits for the operation
        const clothFileName = path.basename(job.clothImagePath);
        const humanFileName = path.basename(job.humanImagePath);
        await creditUsageService.deductVirtualTryOnCredits(
          job.userId,
          clothFileName,
          humanFileName,
          job.id
        );

        // Update job with processed image URL and status
        await prisma.virtualTryOn.update({
          where: { id: jobId },
          data: {
            resultImagePath,
            status: JobStatus.COMPLETED,
            processingCompletedAt: new Date(),
            externalLink: response.data.link,
            metadata: {
              ...metadata,
              processingTimeMs:
                new Date().getTime() -
                new Date(job.processingStartedAt || new Date()).getTime(),
              apiResponse: response.data,
            },
          },
        });

        // Add to recent try-ons
        await this.addToRecentTryOns(job.userId, jobId);

        // Increment model usage count if a model was used
        if (job.modelImageId) {
          await this.incrementModelUsage(job.modelImageId);
        }
      } else {
        throw new Error("API response does not contain a result image link");
      }
    } catch (error) {
      console.error(`Error processing job ${jobId}:`, error);

      // Update job status to failed
      await prisma.virtualTryOn.update({
        where: { id: jobId },
        data: {
          status: JobStatus.FAILED,
          processingCompletedAt: new Date(),
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        },
      });
    }
  }

  /**
   * Gets a virtual try-on job by ID
   * @param jobId Job ID
   * @param userId User ID (for authorization)
   * @returns Job details
   */
  async getJob(jobId: string, userId: string): Promise<VirtualTryOn | null> {
    const job = await prisma.virtualTryOn.findUnique({
      where: { id: jobId },
    });

    // Ensure the job belongs to the user
    if (job && job.userId !== userId) {
      return null;
    }

    return job;
  }

  /**
   * Gets all virtual try-on jobs for a user
   * @param userId User ID
   * @param limit Maximum number of jobs to return
   * @param offset Offset for pagination
   * @returns List of jobs
   */
  async getUserJobs(
    userId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<VirtualTryOn[]> {
    return prisma.virtualTryOn.findMany({
      where: { userId },
      orderBy: { createdAt: "desc" },
      take: limit,
      skip: offset,
    });
  }

  /**
   * Gets recent try-ons for a user
   * @param userId User ID
   * @param limit Maximum number of jobs to return
   * @returns List of recent try-ons
   */
  async getRecentTryOns(
    userId: string,
    limit: number = 5
  ): Promise<VirtualTryOn[]> {
    const recentTryOns = await prisma.recentTryOn.findMany({
      where: { userId },
      orderBy: { accessedAt: "desc" },
      take: limit,
      include: {
        virtualTryOn: true,
      },
    });

    return recentTryOns.map((recent) => recent.virtualTryOn);
  }

  /**
   * Adds a job to the user's recent try-ons
   * @param userId User ID
   * @param jobId Job ID
   */
  private async addToRecentTryOns(
    userId: string,
    jobId: string
  ): Promise<void> {
    // Check if the job is already in recent try-ons
    const existing = await prisma.recentTryOn.findUnique({
      where: {
        userId_tryonId: {
          userId,
          tryonId: jobId,
        },
      },
    });

    if (existing) {
      // Update the accessed time
      await prisma.recentTryOn.update({
        where: {
          id: existing.id,
        },
        data: {
          accessedAt: new Date(),
        },
      });
    } else {
      // Add to recent try-ons
      await prisma.recentTryOn.create({
        data: {
          userId,
          tryonId: jobId,
        },
      });
    }
  }

  /**
   * Increments the usage count for a model
   * @param modelId Model ID
   */
  private async incrementModelUsage(modelId: string): Promise<void> {
    await prisma.modelImage.update({
      where: { id: modelId },
      data: {
        usageCount: {
          increment: 1,
        },
      },
    });
  }

  /**
   * Creates a new model image
   * @param userId User ID
   * @param imagePath Path to the image
   * @param params Model parameters
   * @returns Created model
   */
  async createModelImage(
    userId: string,
    imagePath: string,
    params: {
      modelName?: string;
      gender?: "MALE" | "FEMALE" | "UNISEX";
      bodyType?: string;
      poseType?: string;
      ethnicity?: string;
      isDefault?: boolean;
    }
  ): Promise<ModelImage> {
    return prisma.modelImage.create({
      data: {
        userId: params.isDefault ? process.env.ADMIN_USER_ID || userId : userId,
        imagePath,
        modelName: params.modelName,
        gender: params.gender || "UNISEX",
        bodyType: params.bodyType,
        poseType: params.poseType,
        ethnicity: params.ethnicity,
        isDefault: params.isDefault || false,
      },
    });
  }

  /**
   * Gets all model images available to a user
   * @param userId User ID
   * @param filters Optional filters
   * @returns List of models
   */
  async getModelImages(
    userId: string,
    filters?: {
      gender?: "MALE" | "FEMALE" | "UNISEX";
      bodyType?: string;
      poseType?: string;
      isDefault?: boolean;
    }
  ): Promise<ModelImage[]> {
    // Build filter conditions
    const where: any = {
      OR: [{ userId }, { isDefault: true }],
      isActive: true,
    };

    // Add optional filters
    if (filters) {
      if (filters.gender) where.gender = filters.gender;
      if (filters.bodyType) where.bodyType = filters.bodyType;
      if (filters.poseType) where.poseType = filters.poseType;
      if (filters.isDefault !== undefined) where.isDefault = filters.isDefault;
    }

    return prisma.modelImage.findMany({
      where,
      orderBy: [{ isDefault: "desc" }, { usageCount: "desc" }],
    });
  }

  /**
   * Gets admin model images only (our models)
   * @param filters Optional filters
   * @returns List of admin model images
   */
  async getAdminModelImages(filters?: {
    gender?: "MALE" | "FEMALE" | "UNISEX";
    bodyType?: string;
    poseType?: string;
  }): Promise<ModelImage[]> {
    // Build filter conditions
    const where: any = {
      isDefault: true,
      isActive: true,
    };

    // Add optional filters
    if (filters) {
      if (filters.gender) where.gender = filters.gender;
      if (filters.bodyType) where.bodyType = filters.bodyType;
      if (filters.poseType) where.poseType = filters.poseType;
    }

    return prisma.modelImage.findMany({
      where,
      orderBy: [{ createdAt: "desc" }],
    });
  }

  /**
   * Gets user model images only (your models)
   * @param userId User ID
   * @param filters Optional filters
   * @returns List of user model images
   */
  async getUserModelImages(
    userId: string,
    filters?: {
      gender?: "MALE" | "FEMALE" | "UNISEX";
      bodyType?: string;
      poseType?: string;
    }
  ): Promise<ModelImage[]> {
    // Build filter conditions
    const where: any = {
      userId,
      isActive: true,
    };

    // Add optional filters
    if (filters) {
      if (filters.gender) where.gender = filters.gender;
      if (filters.bodyType) where.bodyType = filters.bodyType;
      if (filters.poseType) where.poseType = filters.poseType;
    }

    return prisma.modelImage.findMany({
      where,
      orderBy: [{ usageCount: "desc" }, { createdAt: "desc" }],
    });
  }

  /**
   * Creates a new clothing item
   * @param userId User ID
   * @param imagePath Path to the image
   * @param params Clothing parameters
   * @returns Created clothing item
   */
  async createClothingItem(
    userId: string,
    imagePath: string,
    params: {
      clothingType: ClothingType;
      category?: string;
      color?: string;
      style?: string;
      season?: string;
    }
  ): Promise<any> {
    return prisma.clothingItem.create({
      data: {
        userId,
        imagePath,
        clothingType: params.clothingType,
        category: params.category,
        color: params.color,
        style: params.style,
        season: params.season,
      },
    });
  }

  /**
   * Gets all clothing items for a user
   * @param userId User ID
   * @param filters Optional filters
   * @returns List of clothing items
   */
  async getClothingItems(
    userId: string,
    filters?: {
      clothingType?: ClothingType;
      category?: string;
      color?: string;
      style?: string;
      season?: string;
    }
  ): Promise<any[]> {
    // Build filter conditions
    const where: any = { userId };

    // Add optional filters
    if (filters) {
      if (filters.clothingType) where.clothingType = filters.clothingType;
      if (filters.category) where.category = filters.category;
      if (filters.color) where.color = filters.color;
      if (filters.style) where.style = filters.style;
      if (filters.season) where.season = filters.season;
    }

    return prisma.clothingItem.findMany({
      where,
      orderBy: { createdAt: "desc" },
    });
  }
}

export default new VirtualTryOnService();
