import { PrismaClient, Prisma, BlogPostStatus, BlogCategory, BlogTag } from '@prisma/client';
import { AppError } from '../middleware/errorHandler';

type BlogCategoryWithCount = Prisma.BlogCategoryGetPayload<{
  include: {
    _count: {
      select: { posts: true }
    }
  }
}>;

const prisma = new PrismaClient();

export interface CreateBlogPostData {
  title: string;
  content: string;
  authorId: string;
  excerpt?: string;
  featuredImageUrl?: string;
  status?: BlogPostStatus;
  publishedAt?: Date;
  scheduledFor?: Date;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  categoryIds?: string[];
  tagIds?: string[];
}

const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[\s\W-]+/g, '-') // Replace spaces and non-word chars (except hyphen) with a hyphen
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

export const createBlogPost = async (data: CreateBlogPostData) => {
  const { title, content, authorId, categoryIds, tagIds, status, ...restData } = data;

  const slug = generateSlug(title);

  // Check if slug already exists, append a suffix if it does (simple version)
  // For a production system, a more robust unique slug generation might be needed
  let uniqueSlug = slug;
  let counter = 1;
  while (await prisma.blogPost.findUnique({ where: { slug: uniqueSlug } })) {
    uniqueSlug = `${slug}-${counter}`;
    counter++;
  }

  const postData: Prisma.BlogPostCreateInput = {
    title,
    slug: uniqueSlug,
    content,
    author: { connect: { id: authorId } },
    status: status || BlogPostStatus.DRAFT,
    ...restData,
  };

  if (categoryIds && categoryIds.length > 0) {
    postData.categories = {
      create: categoryIds.map(catId => ({ category: { connect: { id: catId } } })),
    };
  }

  if (tagIds && tagIds.length > 0) {
    postData.tags = {
      create: tagIds.map(tagId => ({ tag: { connect: { id: tagId } } })),
    };
  }
  
  if (status === BlogPostStatus.PUBLISHED && !restData.publishedAt) {
    postData.publishedAt = new Date();
  }

  return prisma.blogPost.create({
    data: postData,
    include: {
      author: {
        select: {
          id: true,
          email: true,
          profile: { 
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      },
      categories: { include: { category: true } },
      tags: { include: { tag: true } },
    },
  });
};

export interface UpdateBlogPostData {
  title?: string;
  content?: string;
  excerpt?: string;
  featuredImageUrl?: string;
  status?: BlogPostStatus;
  publishedAt?: Date | null;
  scheduledFor?: Date | null;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  categoryIds?: string[];
  tagIds?: string[];
}

export const updateBlogPost = async (postId: string, data: UpdateBlogPostData) => {
  const { title, categoryIds, tagIds, status, ...restData } = data;

  const updateData: Prisma.BlogPostUpdateInput = { ...restData };

  if (title) {
    updateData.title = title;
    const newSlug = generateSlug(title);
    const existingPostWithSlug = await prisma.blogPost.findFirst({
      where: { slug: newSlug, NOT: { id: postId } },
    });
    if (existingPostWithSlug) {
      updateData.slug = `${newSlug}-${Date.now().toString(36).slice(-4)}`;
    } else {
      updateData.slug = newSlug;
    }
  }
  
  if (status === BlogPostStatus.PUBLISHED && !updateData.publishedAt) {
    const currentPost = await prisma.blogPost.findUnique({ where: { id: postId }, select: { publishedAt: true }});
    if (!currentPost?.publishedAt) {
        updateData.publishedAt = new Date();
    }
  } else if (status && status !== BlogPostStatus.PUBLISHED && status !== BlogPostStatus.SCHEDULED) {
      updateData.scheduledFor = null;
  }

  if (categoryIds) {
    updateData.categories = {
      deleteMany: {}, 
      create: categoryIds.map(catId => ({ category: { connect: { id: catId } } })),
    };
  }

  if (tagIds) {
    updateData.tags = {
      deleteMany: {},
      create: tagIds.map(tagId => ({ tag: { connect: { id: tagId } } })),
    };
  }

  return prisma.blogPost.update({
    where: { id: postId },
    data: updateData,
    include: {
      author: {
        select: {
          id: true,
          email: true,
          profile: {
            select: { firstName: true, lastName: true },
          },
        },
      },
      categories: { include: { category: true } },
      tags: { include: { tag: true } },
    },
  });
};
export const deleteBlogPost = async (postId: string) => {
  return prisma.blogPost.delete({
    where: { id: postId },
  });
};
export const getBlogPostById = async (postId: string) => {
  return prisma.blogPost.findUnique({
    where: { id: postId },
    include: {
      author: {
        select: {
          id: true,
          email: true,
          profile: {
            select: { firstName: true, lastName: true },
          },
        },
      },
      categories: { include: { category: true } },
      tags: { include: { tag: true } },
      // Temporarily commenting out comments include due to persistent lint error
      // TODO: Resolve lint error (ID: 08ff9d4f-e70e-4b7c-b1ae-9dd585574107) and re-enable comments include
      /*
      comments: {
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              profile: { select: { firstName: true, lastName: true, avatarUrl: true } },
            },
          },
          parent: true, 
          replies: true, 
        }
      }
      */
    },
  });
};
export interface GetAllBlogPostsAdminParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: BlogPostStatus[];
  categoryIds?: string[];
  tagIds?: string[];
  authorId?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'publishedAt' | 'title' | 'status';
  sortOrder?: 'asc' | 'desc';
}

export const getAllBlogPostsAdmin = async (params: GetAllBlogPostsAdminParams) => {
  const {
    page = 1,
    limit = 10,
    search,
    status,
    categoryIds,
    tagIds,
    authorId,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = params;

  const skip = (page - 1) * limit;
  const where: Prisma.BlogPostWhereInput = {};

  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { content: { contains: search, mode: 'insensitive' } },
    ];
  }

  if (status && status.length > 0) {
    where.status = { in: status };
  }

  if (categoryIds && categoryIds.length > 0) {
    where.categories = { some: { categoryId: { in: categoryIds } } };
  }

  if (tagIds && tagIds.length > 0) {
    where.tags = { some: { tagId: { in: tagIds } } };
  }

  if (authorId) {
    where.authorId = authorId;
  }
  
  const orderBy: Prisma.BlogPostOrderByWithRelationInput = {};
  if (sortBy) {
    orderBy[sortBy] = sortOrder;
  }

  const posts = await prisma.blogPost.findMany({
    where,
    skip,
    take: limit,
    orderBy,
    include: {
      author: {
        select: {
          id: true,
          email: true,
          profile: { select: { firstName: true, lastName: true } },
        },
      },
      categories: { include: { category: true } },
      tags: { include: { tag: true } },
    },
  });

  const totalPosts = await prisma.blogPost.count({ where });

  return {
    data: posts,
    pagination: {
      total: totalPosts,
      page,
      limit,
      totalPages: Math.ceil(totalPosts / limit),
    },
  };
};
export interface CreateBlogCategoryData {
  name: string;
  slug?: string; // Added optional slug
  description?: string | null;
}

export const createBlogCategory = async (data: CreateBlogCategoryData) => {
  const { name, description, slug: providedSlug } = data;
  let slug = providedSlug ? generateSlug(providedSlug) : generateSlug(name);

  let existingCategory = await prisma.blogCategory.findUnique({ where: { slug } });
  let counter = 1;
  while (existingCategory) {
    slug = `${generateSlug(name)}-${counter}`;
    existingCategory = await prisma.blogCategory.findUnique({ where: { slug } });
    counter++;
  }

  return prisma.blogCategory.create({
    data: {
      name,
      slug,
      description,
    },
  });
};
export interface UpdateBlogCategoryData {
  name?: string;
  description?: string | null;
}

export const updateBlogCategory = async (categoryId: string, data: UpdateBlogCategoryData) => {
  const { name, description } = data;
  const updateData: Prisma.BlogCategoryUpdateInput = {};

  if (name) {
    updateData.name = name;
    let newSlug = generateSlug(name);
    
    const existingCategoryWithSlug = await prisma.blogCategory.findFirst({
      where: { slug: newSlug, NOT: { id: categoryId } },
    });
    if (existingCategoryWithSlug) {
      newSlug = `${newSlug}-${Date.now().toString(36).slice(-4)}`;
    }
    updateData.slug = newSlug;
  }

  if (description !== undefined) {
    updateData.description = description;
  }

  return prisma.blogCategory.update({
    where: { id: categoryId },
    data: updateData,
  });
};
export const deleteBlogCategory = async (categoryId: string) => {
  const category = await prisma.blogCategory.findUnique({
    where: { id: categoryId },
  });

  if (!category) {
    throw new AppError('Blog category not found.', 404);
  }

  return prisma.blogCategory.delete({
    where: { id: categoryId },
  });
};
export interface GetAllBlogCategoriesAdminParams {
  page?: number;
  limit?: number;
  search?: string;
}

export const getAllBlogCategoriesAdmin = async (params: GetAllBlogCategoriesAdminParams) => {
  const { page = 1, limit = 10, search } = params;
  const skip = (page - 1) * limit;

  const where: Prisma.BlogCategoryWhereInput = {};
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { slug: { contains: search, mode: 'insensitive' } },
    ];
  }

  const categories: BlogCategoryWithCount[] = await prisma.blogCategory.findMany({
    where,
    skip,
    take: limit,
    orderBy: { name: 'asc' },
    include: {
      _count: {
        select: { posts: true },
      },
    },
  });

  const totalCategories = await prisma.blogCategory.count({ where });

  return {
    data: categories.map((category: BlogCategoryWithCount) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      postCount: category._count.posts,
    })),
    pagination: {
      totalItems: totalCategories,
      currentPage: page,
      pageSize: limit,
      totalPages: Math.ceil(totalCategories / limit),
    },
  };
};

// We will add more service functions here for update, delete, get, etc.
// and for categories and tags.
