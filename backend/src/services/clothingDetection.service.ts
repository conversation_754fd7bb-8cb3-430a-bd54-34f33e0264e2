import * as tf from '@tensorflow/tfjs-node';
import sharp from 'sharp';
import fs from 'fs';
import path from 'path';

interface ClothingDetectionResult {
  clothingType: 'TOP' | 'BOTTOM' | 'DRESS';
  confidence: number;
  details: {
    predictions: Array<{
      category: string;
      confidence: number;
    }>;
  };
}

class ClothingDetectionService {
  private model: tf.LayersModel | null = null;
  private isModelLoaded = false;

  // Clothing type keywords mapping
  private readonly clothingKeywords = {
    TOP: [
      'shirt', 'blouse', 't-shirt', 'tshirt', 'tank', 'top', 'sweater', 
      'hoodie', 'jacket', 'blazer', 'cardigan', 'vest', 'polo', 'tunic',
      'crop', 'halter', 'camisole', 'bodysuit', 'bra', 'bikini_top'
    ],
    BOTTOM: [
      'pants', 'jeans', 'trousers', 'shorts', 'skirt', 'leggings', 
      'joggers', 'sweatpants', 'chinos', 'slacks', 'capris', 'culottes',
      'palazzo', 'bermuda', 'cargo', 'denim', 'bikini_bottom'
    ],
    DRESS: [
      'dress', 'gown', 'frock', 'sundress', 'maxi', 'midi', 'mini',
      'cocktail', 'evening', 'wedding', 'formal', 'casual_dress',
      'shift', 'wrap', 'bodycon', 'a-line', 'jumpsuit', 'romper',
      'overall', 'playsuit', 'coverall'
    ]
  };

  constructor() {
    this.initializeModel();
  }

  private async initializeModel(): Promise<void> {
    try {
      // For now, we'll use a rule-based approach with image analysis
      // In production, you would load a pre-trained clothing classification model
      console.log('Clothing detection service initialized');
      this.isModelLoaded = true;
    } catch (error) {
      console.error('Error initializing clothing detection model:', error);
      this.isModelLoaded = false;
    }
  }

  /**
   * Detect clothing type from image file
   */
  async detectClothingType(imagePath: string): Promise<ClothingDetectionResult> {
    try {
      // Analyze image dimensions and aspect ratio
      const imageAnalysis = await this.analyzeImageDimensions(imagePath);
      
      // Use filename analysis as primary method
      const filenameAnalysis = this.analyzeFilename(imagePath);
      
      // Combine analyses for final prediction
      const finalPrediction = this.combineAnalyses(imageAnalysis, filenameAnalysis);
      
      return finalPrediction;
    } catch (error) {
      console.error('Error detecting clothing type:', error);
      // Return default prediction with low confidence
      return {
        clothingType: 'TOP',
        confidence: 0.3,
        details: {
          predictions: [
            { category: 'TOP', confidence: 0.3 },
            { category: 'BOTTOM', confidence: 0.25 },
            { category: 'DRESS', confidence: 0.25 }
          ]
        }
      };
    }
  }

  /**
   * Analyze image dimensions to infer clothing type
   */
  private async analyzeImageDimensions(imagePath: string): Promise<{
    aspectRatio: number;
    width: number;
    height: number;
    prediction: 'TOP' | 'BOTTOM' | 'DRESS';
    confidence: number;
  }> {
    const metadata = await sharp(imagePath).metadata();
    const width = metadata.width || 0;
    const height = metadata.height || 0;
    const aspectRatio = width / height;

    let prediction: 'TOP' | 'BOTTOM' | 'DRESS' = 'TOP';
    let confidence = 0.6;

    // Aspect ratio analysis
    if (aspectRatio > 1.2) {
      // Wide images are likely tops or bottoms
      prediction = 'TOP';
      confidence = 0.65;
    } else if (aspectRatio < 0.7) {
      // Tall images are likely dresses or full-body clothing
      prediction = 'DRESS';
      confidence = 0.7;
    } else {
      // Square-ish images could be any type
      prediction = 'TOP';
      confidence = 0.5;
    }

    return {
      aspectRatio,
      width,
      height,
      prediction,
      confidence
    };
  }

  /**
   * Analyze filename for clothing type keywords
   */
  private analyzeFilename(imagePath: string): {
    prediction: 'TOP' | 'BOTTOM' | 'DRESS';
    confidence: number;
    matchedKeywords: string[];
  } {
    const filename = path.basename(imagePath).toLowerCase();
    const matchedKeywords: string[] = [];
    const scores = { TOP: 0, BOTTOM: 0, DRESS: 0 };

    // Check for keywords in filename
    Object.entries(this.clothingKeywords).forEach(([type, keywords]) => {
      keywords.forEach(keyword => {
        if (filename.includes(keyword.toLowerCase())) {
          matchedKeywords.push(keyword);
          scores[type as keyof typeof scores] += 1;
        }
      });
    });

    // Find the type with highest score
    const maxScore = Math.max(scores.TOP, scores.BOTTOM, scores.DRESS);
    let prediction: 'TOP' | 'BOTTOM' | 'DRESS' = 'TOP';
    
    if (maxScore > 0) {
      if (scores.DRESS === maxScore) prediction = 'DRESS';
      else if (scores.BOTTOM === maxScore) prediction = 'BOTTOM';
      else prediction = 'TOP';
    }

    const confidence = maxScore > 0 ? Math.min(0.9, 0.6 + (maxScore * 0.1)) : 0.3;

    return {
      prediction,
      confidence,
      matchedKeywords
    };
  }

  /**
   * Combine different analyses for final prediction
   */
  private combineAnalyses(
    imageAnalysis: any,
    filenameAnalysis: any
  ): ClothingDetectionResult {
    // Filename analysis has higher weight if keywords are found
    const filenameWeight = filenameAnalysis.matchedKeywords.length > 0 ? 0.7 : 0.3;
    const imageWeight = 1 - filenameWeight;

    const scores = {
      TOP: 0,
      BOTTOM: 0,
      DRESS: 0
    };

    // Add weighted scores
    scores[filenameAnalysis.prediction] += filenameAnalysis.confidence * filenameWeight;
    scores[imageAnalysis.prediction] += imageAnalysis.confidence * imageWeight;

    // Find the prediction with highest score
    const maxScore = Math.max(scores.TOP, scores.BOTTOM, scores.DRESS);
    let finalPrediction: 'TOP' | 'BOTTOM' | 'DRESS' = 'TOP';
    
    if (scores.DRESS === maxScore) finalPrediction = 'DRESS';
    else if (scores.BOTTOM === maxScore) finalPrediction = 'BOTTOM';
    else finalPrediction = 'TOP';

    // Calculate confidence (minimum 0.4, maximum 0.95)
    const confidence = Math.max(0.4, Math.min(0.95, maxScore));

    return {
      clothingType: finalPrediction,
      confidence,
      details: {
        predictions: [
          { category: 'TOP', confidence: scores.TOP },
          { category: 'BOTTOM', confidence: scores.BOTTOM },
          { category: 'DRESS', confidence: scores.DRESS }
        ].sort((a, b) => b.confidence - a.confidence)
      }
    };
  }

  /**
   * Validate if the service is ready
   */
  isReady(): boolean {
    return this.isModelLoaded;
  }

  /**
   * Get clothing type from user input with validation
   */
  validateClothingType(userInput: string): 'TOP' | 'BOTTOM' | 'DRESS' {
    const input = userInput.toUpperCase();
    if (['TOP', 'BOTTOM', 'DRESS'].includes(input)) {
      return input as 'TOP' | 'BOTTOM' | 'DRESS';
    }
    return 'TOP'; // Default fallback
  }
}

export default new ClothingDetectionService();
