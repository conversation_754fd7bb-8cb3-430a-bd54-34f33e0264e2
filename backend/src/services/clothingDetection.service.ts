import * as tf from "@tensorflow/tfjs-node";
import sharp from "sharp";
import fs from "fs";
import path from "path";
import axios from "axios";

interface ClothingDetectionResult {
  clothingType: "TOP" | "BOTTOM" | "DRESS";
  confidence: number;
  details: {
    predictions: Array<{
      category: string;
      confidence: number;
    }>;
  };
}

class ClothingDetectionService {
  private model: tf.LayersModel | null = null;
  private isModelLoaded = false;

  // Clothing type keywords mapping
  private readonly clothingKeywords = {
    TOP: [
      "shirt",
      "blouse",
      "t-shirt",
      "tshirt",
      "tank",
      "top",
      "sweater",
      "hoodie",
      "jacket",
      "blazer",
      "cardigan",
      "vest",
      "polo",
      "tunic",
      "crop",
      "halter",
      "camisole",
      "bodysuit",
      "bra",
      "bikini_top",
    ],
    BOTTOM: [
      "pants",
      "jeans",
      "trousers",
      "shorts",
      "skirt",
      "leggings",
      "joggers",
      "sweatpants",
      "chinos",
      "slacks",
      "capris",
      "culottes",
      "palazzo",
      "bermuda",
      "cargo",
      "denim",
      "bikini_bottom",
    ],
    DRESS: [
      "dress",
      "gown",
      "frock",
      "sundress",
      "maxi",
      "midi",
      "mini",
      "cocktail",
      "evening",
      "wedding",
      "formal",
      "casual_dress",
      "shift",
      "wrap",
      "bodycon",
      "a-line",
      "jumpsuit",
      "romper",
      "overall",
      "playsuit",
      "coverall",
    ],
  };

  constructor() {
    this.initializeModel();
  }

  private async initializeModel(): Promise<void> {
    try {
      // For now, we'll use a rule-based approach with image analysis
      // In production, you would load a pre-trained clothing classification model
      console.log("Clothing detection service initialized");
      this.isModelLoaded = true;
    } catch (error) {
      console.error("Error initializing clothing detection model:", error);
      this.isModelLoaded = false;
    }
  }

  /**
   * Detect clothing type from image file with enhanced analysis
   */
  async detectClothingType(
    imagePath: string
  ): Promise<ClothingDetectionResult> {
    try {
      console.log("🔍 Starting enhanced AI clothing detection for:", imagePath);

      // Multiple analysis methods
      const imageAnalysis = await this.analyzeImageDimensions(imagePath);
      const filenameAnalysis = this.analyzeFilename(imagePath);
      const colorAnalysis = await this.analyzeImageColors(imagePath);
      const shapeAnalysis = await this.analyzeImageShape(imagePath);

      // Combine all analyses for final prediction
      const finalPrediction = this.combineEnhancedAnalyses(
        imageAnalysis,
        filenameAnalysis,
        colorAnalysis,
        shapeAnalysis
      );

      console.log("✅ Enhanced AI Detection Result:", {
        filename: path.basename(imagePath),
        detectedType: finalPrediction.clothingType,
        confidence: finalPrediction.confidence,
        details: finalPrediction.details,
      });

      return finalPrediction;
    } catch (error) {
      console.error("⚠️ Error in enhanced clothing detection:", error);
      // Return default prediction with low confidence
      return {
        clothingType: "TOP",
        confidence: 0.3,
        details: {
          predictions: [
            { category: "TOP", confidence: 0.3 },
            { category: "BOTTOM", confidence: 0.25 },
            { category: "DRESS", confidence: 0.25 },
          ],
        },
      };
    }
  }

  /**
   * Enhanced image dimension analysis
   */
  private async analyzeImageDimensions(imagePath: string): Promise<{
    aspectRatio: number;
    width: number;
    height: number;
    prediction: "TOP" | "BOTTOM" | "DRESS";
    confidence: number;
  }> {
    const metadata = await sharp(imagePath).metadata();
    const width = metadata.width || 0;
    const height = metadata.height || 0;
    const aspectRatio = width / height;

    let prediction: "TOP" | "BOTTOM" | "DRESS" = "TOP";
    let confidence = 0.6;

    // Enhanced aspect ratio analysis
    if (aspectRatio > 1.5) {
      // Very wide images are likely tops (laid flat)
      prediction = "TOP";
      confidence = 0.75;
    } else if (aspectRatio > 1.1 && aspectRatio <= 1.5) {
      // Moderately wide images could be tops or bottoms
      prediction = "TOP";
      confidence = 0.65;
    } else if (aspectRatio < 0.6) {
      // Very tall images are likely dresses or full-body clothing
      prediction = "DRESS";
      confidence = 0.8;
    } else if (aspectRatio >= 0.6 && aspectRatio < 0.9) {
      // Tall images could be dresses or long tops
      prediction = "DRESS";
      confidence = 0.7;
    } else {
      // Square-ish images could be any type, default to TOP
      prediction = "TOP";
      confidence = 0.5;
    }

    return {
      aspectRatio,
      width,
      height,
      prediction,
      confidence,
    };
  }

  /**
   * Analyze image colors for clothing type hints
   */
  private async analyzeImageColors(imagePath: string): Promise<{
    dominantColors: string[];
    prediction: "TOP" | "BOTTOM" | "DRESS";
    confidence: number;
  }> {
    try {
      const { dominant } = await sharp(imagePath)
        .resize(100, 100)
        .raw()
        .toBuffer({ resolveWithObject: true });

      // Simple color analysis - in production, use more sophisticated color detection
      const prediction: "TOP" | "BOTTOM" | "DRESS" = "TOP";
      const confidence = 0.4; // Lower confidence for color-based detection

      return {
        dominantColors: ["unknown"],
        prediction,
        confidence,
      };
    } catch (error) {
      return {
        dominantColors: [],
        prediction: "TOP",
        confidence: 0.3,
      };
    }
  }

  /**
   * Analyze image shape characteristics
   */
  private async analyzeImageShape(imagePath: string): Promise<{
    shapeFeatures: string[];
    prediction: "TOP" | "BOTTOM" | "DRESS";
    confidence: number;
  }> {
    try {
      const stats = await sharp(imagePath).stats();

      // Analyze image statistics for shape characteristics
      let prediction: "TOP" | "BOTTOM" | "DRESS" = "TOP";
      let confidence = 0.5;

      // Use image statistics to infer garment type
      // This is a simplified approach - in production, use computer vision
      const channels = stats.channels;
      if (channels && channels.length > 0) {
        const avgMean =
          channels.reduce((sum, ch) => sum + ch.mean, 0) / channels.length;

        // Darker images might be bottoms (jeans, pants)
        if (avgMean < 100) {
          prediction = "BOTTOM";
          confidence = 0.6;
        } else if (avgMean > 180) {
          // Very bright images might be tops (white shirts, etc.)
          prediction = "TOP";
          confidence = 0.65;
        }
      }

      return {
        shapeFeatures: ["analyzed"],
        prediction,
        confidence,
      };
    } catch (error) {
      return {
        shapeFeatures: [],
        prediction: "TOP",
        confidence: 0.3,
      };
    }
  }

  /**
   * Analyze filename for clothing type keywords
   */
  private analyzeFilename(imagePath: string): {
    prediction: "TOP" | "BOTTOM" | "DRESS";
    confidence: number;
    matchedKeywords: string[];
  } {
    const filename = path.basename(imagePath).toLowerCase();
    const matchedKeywords: string[] = [];
    const scores = { TOP: 0, BOTTOM: 0, DRESS: 0 };

    // Check for keywords in filename
    Object.entries(this.clothingKeywords).forEach(([type, keywords]) => {
      keywords.forEach((keyword) => {
        if (filename.includes(keyword.toLowerCase())) {
          matchedKeywords.push(keyword);
          scores[type as keyof typeof scores] += 1;
        }
      });
    });

    // Find the type with highest score
    const maxScore = Math.max(scores.TOP, scores.BOTTOM, scores.DRESS);
    let prediction: "TOP" | "BOTTOM" | "DRESS" = "TOP";

    if (maxScore > 0) {
      if (scores.DRESS === maxScore) prediction = "DRESS";
      else if (scores.BOTTOM === maxScore) prediction = "BOTTOM";
      else prediction = "TOP";
    }

    const confidence = maxScore > 0 ? Math.min(0.9, 0.6 + maxScore * 0.1) : 0.3;

    return {
      prediction,
      confidence,
      matchedKeywords,
    };
  }

  /**
   * Combine enhanced analyses for final prediction
   */
  private combineEnhancedAnalyses(
    imageAnalysis: any,
    filenameAnalysis: any,
    colorAnalysis: any,
    shapeAnalysis: any
  ): ClothingDetectionResult {
    // Weighted scoring system
    const weights = {
      filename: filenameAnalysis.matchedKeywords.length > 0 ? 0.4 : 0.2,
      image: 0.3,
      color: 0.15,
      shape: 0.15,
    };

    // Normalize weights to sum to 1
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
    Object.keys(weights).forEach((key) => {
      weights[key as keyof typeof weights] /= totalWeight;
    });

    const scores = {
      TOP: 0,
      BOTTOM: 0,
      DRESS: 0,
    };

    // Add weighted scores from all analyses
    scores[filenameAnalysis.prediction] +=
      filenameAnalysis.confidence * weights.filename;
    scores[imageAnalysis.prediction] +=
      imageAnalysis.confidence * weights.image;
    scores[colorAnalysis.prediction] +=
      colorAnalysis.confidence * weights.color;
    scores[shapeAnalysis.prediction] +=
      shapeAnalysis.confidence * weights.shape;

    // Find the prediction with highest score
    const maxScore = Math.max(scores.TOP, scores.BOTTOM, scores.DRESS);
    let finalPrediction: "TOP" | "BOTTOM" | "DRESS" = "TOP";

    if (scores.DRESS === maxScore) finalPrediction = "DRESS";
    else if (scores.BOTTOM === maxScore) finalPrediction = "BOTTOM";
    else finalPrediction = "TOP";

    // Calculate confidence with enhanced scoring
    let confidence = Math.max(0.4, Math.min(0.95, maxScore));

    // Boost confidence if multiple analyses agree
    const agreementCount = [
      imageAnalysis,
      filenameAnalysis,
      colorAnalysis,
      shapeAnalysis,
    ].filter((analysis) => analysis.prediction === finalPrediction).length;

    if (agreementCount >= 3) {
      confidence = Math.min(0.95, confidence + 0.1);
    } else if (agreementCount >= 2) {
      confidence = Math.min(0.9, confidence + 0.05);
    }

    return {
      clothingType: finalPrediction,
      confidence,
      details: {
        predictions: [
          { category: "TOP", confidence: scores.TOP },
          { category: "BOTTOM", confidence: scores.BOTTOM },
          { category: "DRESS", confidence: scores.DRESS },
        ].sort((a, b) => b.confidence - a.confidence),
      },
    };
  }

  /**
   * Validate if the service is ready
   */
  isReady(): boolean {
    return this.isModelLoaded;
  }

  /**
   * Get clothing type from user input with validation
   */
  validateClothingType(userInput: string): "TOP" | "BOTTOM" | "DRESS" {
    const input = userInput.toUpperCase();
    if (["TOP", "BOTTOM", "DRESS"].includes(input)) {
      return input as "TOP" | "BOTTOM" | "DRESS";
    }
    return "TOP"; // Default fallback
  }
}

export default new ClothingDetectionService();
