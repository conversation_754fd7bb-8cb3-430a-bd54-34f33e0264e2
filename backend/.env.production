# Production Environment Configuration
PORT=5000
NODE_ENV=production
BASE_URL=https://api.miragic-ai.com
FRONTEND_URL=https://miragic-ai.com
USE_MOCK_API=false

# Database
DATABASE_URL="*************************************************/miragicai?schema=public"

# JWT Authentication
JWT_SECRET=replace_with_strong_secret_key_in_production
JWT_REFRESH_SECRET=replace_with_strong_refresh_secret_key_in_production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Stripe
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key

# PayPal
PAYPAL_CLIENT_ID=your_paypal_live_client_id
PAYPAL_CLIENT_SECRET=your_paypal_live_client_secret
PAYPAL_WEBHOOK_ID=your_paypal_live_webhook_id
PAYPAL_ENVIRONMENT=live

# File Storage (Cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# AI Service APIs
SYNTHESIA_API_KEY=your_synthesia_api_key
SYNTHESIA_API_URL=https://api.synthesia.io/v2

STABILITY_AI_API_KEY=your_stability_ai_api_key
STABILITY_AI_API_URL=https://api.stability.ai/v1

REMOVAL_AI_API_KEY=your_removal_ai_api_key
REMOVAL_AI_API_URL=https://api.removal.ai/3.0

# Email Service (Resend)
RESEND_API_KEY=your_resend_api_key
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Redis (for queue)
REDIS_URL=redis://redis-host:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
CORS_ORIGINS=https://miragic-ai.com,https://www.miragic-ai.com,https://admin.miragic-ai.com
TRUST_PROXY=true

# Logging
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn

# Feature Flags
ENABLE_REFUND_AUTOMATION=true
ENABLE_CREDIT_EXPIRY=true
ENABLE_SUBSCRIPTION_ROLLOVER=true
